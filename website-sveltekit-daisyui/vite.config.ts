import { paraglideVitePlugin } from '@inlang/paraglide-js';
import devtoolsJson from 'vite-plugin-devtools-json';
import tailwindcss from '@tailwindcss/vite';
import { sveltekit } from '@sveltejs/kit/vite';
import { defineConfig, loadEnv } from 'vite';

export default defineConfig(({ mode }) => {
	const env = loadEnv(mode, '.', '');
	return {
		plugins: [tailwindcss(), sveltekit(), devtoolsJson(),
		paraglideVitePlugin({
			project: './project.inlang',
			outdir: './src/lib/paraglide'
		})],
		server: {
			port: parseInt(env.VITE_PORT || '8000'),
			host: true,
		},
		optimizeDeps: {
			// include: [
			// 	'intl-messageformat',
			// 	'deepmerge',
			// 	'devalue',
			// 	'zod',
			// 	'chart.js',
			// 	'svelte-i18n',
			// 	'@iconify/svelte',
			// 	'chartjs-adapter-date-fns',
			// 	'sweetalert2-neutral',
			// ],
		},
	};
});