import { handleCustomerApiError } from '$lib/utils/auth';

const API_BASE_URL = 'http://localhost:5000/v1';

interface ApiResponse<T = any> {
    success: boolean;
    message?: string;
    data?: T;
    error?: string;
}

class CustomerApiClient {
    private getAuthHeaders(): HeadersInit {
        return {
            'Content-Type': 'application/json',
        };
    }

    async request<T>(endpoint: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
        try {
            const response = await fetch(`${API_BASE_URL}${endpoint}`, {
                ...options,
                headers: {
                    ...this.getAuthHeaders(),
                    ...options.headers,
                },
                credentials: 'include', // Include cookies
            });

            const data = await response.json();

            if (!response.ok) {
                // Handle token refresh for 401 errors
                if (response.status === 401) {
                    const retrySuccess = await handleCustomerApiError({ message: '401' });
                    if (retrySuccess) {
                        // Retry the request after token refresh
                        return this.request(endpoint, options);
                    }
                }
                throw new Error(data.message || 'เกิดข้อผิดพลาด');
            }

            return data;
        } catch (error) {
            console.error('API Error:', error);
            throw error;
        }
    }

    // Customer Auth methods
    async signup(data: {
        email: string;
        password: string;
        confirmPassword: string;
        firstName: string;
        lastName: string;
        phone?: string;
    }) {
        return this.request('/customers/signup', {
            method: 'POST',
            body: JSON.stringify(data),
        });
    }

    async signin(email: string, password: string) {
        return this.request('/customers/signin', {
            method: 'POST',
            body: JSON.stringify({ email, password }),
        });
    }

    async getProfile() {
        return this.request('/customers/me');
    }

    async updateProfile(data: any) {
        return this.request('/customers/me', {
            method: 'PUT',
            body: JSON.stringify(data),
        });
    }

    async changePassword(currentPassword: string, newPassword: string) {
        return this.request('/customers/me/change-password', {
            method: 'PUT',
            body: JSON.stringify({ currentPassword, newPassword }),
        });
    }

    async signout() {
        return this.request('/customers/me/signout', {
            method: 'POST',
        });
    }

    async verifyEmail(token: string) {
        return this.request('/customers/verify-email', {
            method: 'POST',
            body: JSON.stringify({ token }),
        });
    }

    async forgotPassword(email: string) {
        return this.request('/customers/forgot-password', {
            method: 'POST',
            body: JSON.stringify({ email }),
        });
    }

    async resetPassword(token: string, password: string) {
        return this.request('/customers/reset-password', {
            method: 'POST',
            body: JSON.stringify({ token, password }),
        });
    }

    async resendVerification(email: string) {
        return this.request('/customers/resend-verification', {
            method: 'POST',
            body: JSON.stringify({ email }),
        });
    }

    async refreshToken() {
        return this.request('/customers/refresh', {
            method: 'POST',
        });
    }
}

export const customerApi = new CustomerApiClient();