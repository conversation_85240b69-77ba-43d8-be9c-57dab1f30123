import { writable } from 'svelte/store';
import { browser } from '$app/environment';

interface Customer {
    _id: string;
    email: string;
    firstName: string;
    lastName: string;
    phone?: string;
    avatar?: string;
    address?: {
        street?: string;
        city?: string;
        province?: string;
        postalCode?: string;
        country?: string;
    };
    dateOfBirth?: string;
    gender?: 'male' | 'female' | 'other';
    isEmailVerified: boolean;
    isActive: boolean;
}

interface CustomerAuthState {
    customer: Customer | null;
    isAuthenticated: boolean;
    isLoading: boolean;
}

const initialState: CustomerAuthState = {
    customer: null,
    isAuthenticated: false,
    isLoading: true,
};

function createCustomerStore() {
    const { subscribe, set, update } = writable<CustomerAuthState>(initialState);

    return {
        subscribe,
        signin: (customer: Customer) => {
            set({
                customer,
                isAuthenticated: true,
                isLoading: false,
            });
        },
        signout: () => {
            set({
                customer: null,
                isAuthenticated: false,
                isLoading: false,
            });
            console.log('Customer store signout completed');
        },
        setCustomer: (customer: Customer) => {
            update(state => ({
                ...state,
                customer,
                isAuthenticated: true,
                isLoading: false,
            }));
        },
        setLoading: (isLoading: boolean) => {
            update(state => ({
                ...state,
                isLoading,
            }));
        },
    };
}

export const customerStore = createCustomerStore();