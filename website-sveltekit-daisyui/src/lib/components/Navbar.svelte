<script lang="ts">
	import { customerStore } from "$lib/stores/customer";
	import { onMount } from "svelte";
	import { customerApi } from "$lib/api";

	let isAuthenticated = false;
	let customer: any = null;

	// Subscribe to customer store
	customerStore.subscribe((state) => {
		isAuthenticated = state.isAuthenticated;
		customer = state.customer;
	});

	// Check authentication on mount
	onMount(async () => {
		const token = localStorage.getItem("customerAccessToken");
		if (token) {
			try {
				const response = await customerApi.getProfile();
				if (response.success) {
					customerStore.setCustomer(response.data as any);
				}
			} catch (error) {
				// Token might be expired, clear it completely
				localStorage.removeItem("customerAccessToken");
				localStorage.removeItem("customerRefreshToken");
				sessionStorage.removeItem("customerAccessToken");
				sessionStorage.removeItem("customerRefreshToken");
				customerStore.signout();
			}
		} else {
			customerStore.setLoading(false);
		}
	});

	async function handleSignout() {
		console.log("Signout function called!");

		// Import the force signout utility
		const { forceSignout } = await import("$lib/utils/auth");
		await forceSignout();
	}
</script>

<div class="navbar bg-base-100 shadow-lg">
	<div class="navbar-start">
		<div class="dropdown">
			<div tabindex="0" role="button" class="btn btn-ghost lg:hidden">
				<svg
					xmlns="http://www.w3.org/2000/svg"
					class="h-5 w-5"
					fill="none"
					viewBox="0 0 24 24"
					stroke="currentColor"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						stroke-width="2"
						d="M4 6h16M4 12h8m-8 6h16"
					/>
				</svg>
			</div>
			<ul
				tabindex="0"
				class="menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow bg-base-100 rounded-box w-52"
			>
				<li><a href="/">หน้าหลัก</a></li>
				<li><a href="/products">สินค้า</a></li>
				<li><a href="/about">เกี่ยวกับเรา</a></li>
				<li><a href="/contact">ติดต่อเรา</a></li>
			</ul>
		</div>
		<a href="/" class="btn btn-ghost text-xl">WebShop</a>
	</div>

	<div class="navbar-center hidden lg:flex">
		<ul class="menu menu-horizontal px-1">
			<li><a href="/">หน้าหลัก</a></li>
			<li><a href="/products">สินค้า</a></li>
			<li><a href="/about">เกี่ยวกับเรา</a></li>
			<li><a href="/contact">ติดต่อเรา</a></li>
		</ul>
	</div>

	<div class="navbar-end">
		{#if isAuthenticated && customer}
			<div class="dropdown dropdown-end">
				<div
					tabindex="0"
					role="button"
					class="btn btn-ghost btn-circle avatar"
				>
					<div class="w-10 rounded-full">
						{#if customer.avatar}
							<img alt="Avatar" src={customer.avatar} />
						{:else}
							<div
								class="bg-neutral text-neutral-content rounded-full w-10 h-10 flex items-center justify-center"
							>
								<span class="text-sm"
									>{customer.firstName.charAt(0)}</span
								>
							</div>
						{/if}
					</div>
				</div>
				<ul
					tabindex="0"
					class="mt-3 z-[1] p-2 shadow menu menu-sm dropdown-content bg-base-100 rounded-box w-52"
				>
					<li class="menu-title">
						<span>{customer.firstName} {customer.lastName}</span>
					</li>
					<li><a href="/profile">โปรไฟล์</a></li>
					<li><a href="/orders">คำสั่งซื้อ</a></li>
					<li><a href="/wishlist">รายการโปรด</a></li>
					<li><hr /></li>
					<li><button onclick={handleSignout}>ออกจากระบบ</button></li>
				</ul>
			</div>
		{:else}
			<div class="flex gap-2">
				<a href="/signin" class="btn btn-ghost">เข้าสู่ระบบ</a>
				<a href="/signup" class="btn btn-primary">สมัครสมาชิก</a>
			</div>
		{/if}
	</div>
</div>
