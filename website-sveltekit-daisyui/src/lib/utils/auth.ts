import { customerStore } from '$lib/stores/customer';
import { customerApi } from '$lib/api';
import { browser } from '$app/environment';

export async function refreshCustomerToken(): Promise<boolean> {
	if (!browser) return false;

	try {
		const response = await fetch('http://localhost:5000/v1/customers/refresh', {
			method: 'POST',
			credentials: 'include',
			headers: {
				'Content-Type': 'application/json',
			},
		});

		if (response.ok) {
			return true;
		}

		return false;
	} catch (error) {
		console.error('Customer token refresh failed:', error);
		return false;
	}
}

export async function handleCustomerApiError(error: any): Promise<boolean> {
	if (error.message?.includes('401') || error.message?.includes('Unauthorized')) {
		const refreshed = await refreshCustomerToken();
		if (!refreshed) {
			// Redirect to login if refresh fails
			window.location.href = '/login';
			return false;
		}
		return true;
	}
	return false;
}

export async function forceSignout() {
    console.log('Force signout initiated...');

    if (!browser) return;

    try {
        // Try API signout first (but don't wait for it)
        customerApi.signout().catch(error => {
            console.log('API signout failed:', error);
        });
    } catch (error) {
        console.log('API signout error:', error);
    }

    // Update store
    customerStore.signout();

    console.log('Force signout completed');

    // Force page reload to ensure clean state
    setTimeout(() => {
        window.location.href = '/';
    }, 100);
}

export function isAuthenticated(): boolean {
    if (!browser) return false;

    // Check if customer cookie exists
    const cookies = document.cookie.split(';');
    return cookies.some(cookie => 
        cookie.trim().startsWith('customerAccessToken=')
    );
}

export function getAuthToken(): string | null {
    if (!browser) return null;

    // Get token from cookie
    const cookies = document.cookie.split(';');
    const tokenCookie = cookies.find(cookie => 
        cookie.trim().startsWith('customerAccessToken=')
    );
    
    if (tokenCookie) {
        return tokenCookie.split('=')[1];
    }
    
    return null;
}