<script lang="ts">
	import { onMount } from "svelte";
	import { page } from "$app/state";
	import { goto } from "$app/navigation";
	import { customerApi } from "$lib/api";
	import Swal from "sweetalert2-neutral";

	let token = "";
	let formData = {
		password: "",
		confirmPassword: "",
	};
	let loading = false;
	let errors: Record<string, string> = {};

	onMount(() => {
		token = page.url.searchParams.get("token") || "";
		if (!token) {
			Swal.fire(
				"ข้อผิดพลาด",
				"ไม่พบ token สำหรับรีเซ็ตรหัสผ่าน",
				"error",
			).then(() => {
				goto("/signin");
			});
		}
	});

	function validateForm() {
		errors = {};

		if (!formData.password) {
			errors.password = "กรุณากรอกรหัสผ่านใหม่";
		} else if (formData.password.length < 6) {
			errors.password = "รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร";
		}

		if (!formData.confirmPassword) {
			errors.confirmPassword = "กรุณายืนยันรหัสผ่าน";
		} else if (formData.password !== formData.confirmPassword) {
			errors.confirmPassword = "รหัสผ่านไม่ตรงกัน";
		}

		return Object.keys(errors).length === 0;
	}

	async function handleSubmit() {
		if (!validateForm()) return;

		try {
			loading = true;
			const response = await customerApi.resetPassword(
				token,
				formData.password,
			);

			if (response.success) {
				await Swal.fire({
					title: "สำเร็จ!",
					text: response.message || "รีเซ็ตรหัสผ่านสำเร็จ",
					icon: "success",
					confirmButtonText: "ตกลง",
				});

				goto("/signin");
			}
		} catch (error: any) {
			Swal.fire("ข้อผิดพลาด", error.message || "เกิดข้อผิดพลาด", "error");
		} finally {
			loading = false;
		}
	}
</script>

<svelte:head>
	<title>รีเซ็ตรหัสผ่าน</title>
</svelte:head>

<div class="min-h-screen bg-base-200 flex items-center justify-center p-4">
	<div class="card w-full max-w-md bg-base-100 shadow-xl">
		<div class="card-body">
			<h2 class="card-title text-center text-2xl font-bold mb-6">
				รีเซ็ตรหัสผ่าน
			</h2>

			<form on:submit|preventDefault={handleSubmit} class="space-y-4">
				<!-- New Password -->
				<div class="form-control">
					<label class="label" for="password">
						<span class="label-text">รหัสผ่านใหม่</span>
					</label>
					<input
						id="password"
						type="password"
						class="input input-bordered {errors.password
							? 'input-error'
							: ''}"
						bind:value={formData.password}
						disabled={loading}
						required
					/>
					{#if errors.password}
						<label class="label">
							<span class="label-text-alt text-error"
								>{errors.password}</span
							>
						</label>
					{/if}
				</div>

				<!-- Confirm Password -->
				<div class="form-control">
					<label class="label" for="confirmPassword">
						<span class="label-text">ยืนยันรหัสผ่านใหม่</span>
					</label>
					<input
						id="confirmPassword"
						type="password"
						class="input input-bordered {errors.confirmPassword
							? 'input-error'
							: ''}"
						bind:value={formData.confirmPassword}
						disabled={loading}
						required
					/>
					{#if errors.confirmPassword}
						<label class="label">
							<span class="label-text-alt text-error"
								>{errors.confirmPassword}</span
							>
						</label>
					{/if}
				</div>

				<!-- Submit Button -->
				<div class="form-control mt-6">
					<button
						type="submit"
						class="btn btn-primary"
						class:loading
						disabled={loading}
					>
						{loading ? "กำลังรีเซ็ต..." : "รีเซ็ตรหัสผ่าน"}
					</button>
				</div>
			</form>

			<div class="text-center mt-4">
				<a href="/signin" class="link link-primary">กลับไปเข้าสู่ระบบ</a
				>
			</div>
		</div>
	</div>
</div>
