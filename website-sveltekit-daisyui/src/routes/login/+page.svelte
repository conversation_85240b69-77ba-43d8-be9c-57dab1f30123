<script lang="ts">
	import { goto } from "$app/navigation";
	import { customerApi } from "$lib/api";
	import { customerStore } from "$lib/stores/customer";
	import Swal from "sweetalert2-neutral";

	let formData = {
		email: "",
		password: "",
	};
	let loading = false;
	let errors: Record<string, string> = {};

	function validateForm() {
		errors = {};

		if (!formData.email) {
			errors.email = "กรุณากรอกอีเมล";
		} else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
			errors.email = "รูปแบบอีเมลไม่ถูกต้อง";
		}

		if (!formData.password) {
			errors.password = "กรุณากรอกรหัสผ่าน";
		}

		return Object.keys(errors).length === 0;
	}

	async function handleSubmit() {
		if (!validateForm()) return;

		try {
			loading = true;
			const response = await customerApi.signin(
				formData.email,
				formData.password,
			);

			if (response.success) {
				customerStore.signin(
					response.data.customer,
					response.data.accessToken,
					response.data.refreshToken,
				);

				await Swal.fire({
					title: "เข้าสู่ระบบสำเร็จ!",
					text: `ยินดีต้อนรับ ${response.data.customer.firstName}`,
					icon: "success",
					timer: 2000,
					showConfirmButton: false,
				});

				goto("/profile");
			}
		} catch (error: any) {
			Swal.fire("ข้อผิดพลาด", error.message || "เกิดข้อผิดพลาด", "error");
		} finally {
			loading = false;
		}
	}

	async function handleForgotPassword() {
		if (!formData.email) {
			Swal.fire("ข้อผิดพลาด", "กรุณากรอกอีเมลก่อน", "error");
			return;
		}

		try {
			const response = await customerApi.forgotPassword(formData.email);
			if (response.success) {
				Swal.fire("สำเร็จ", response.message, "success");
			}
		} catch (error: any) {
			Swal.fire("ข้อผิดพลาด", error.message || "เกิดข้อผิดพลาด", "error");
		}
	}
</script>

<svelte:head>
	<title>เข้าสู่ระบบ</title>
</svelte:head>

<div class="min-h-screen bg-base-200 flex items-center justify-center p-4">
	<div class="card w-full max-w-md bg-base-100 shadow-xl">
		<div class="card-body">
			<h2 class="card-title text-center text-2xl font-bold mb-6">
				เข้าสู่ระบบ
			</h2>

			<form on:submit|preventDefault={handleSubmit} class="space-y-4">
				<!-- Email -->
				<div class="form-control">
					<label class="label" for="email">
						<span class="label-text">อีเมล</span>
					</label>
					<input
						id="email"
						type="email"
						class="input input-bordered {errors.email
							? 'input-error'
							: ''}"
						bind:value={formData.email}
						disabled={loading}
						required
					/>
					{#if errors.email}
						<label class="label">
							<span class="label-text-alt text-error"
								>{errors.email}</span
							>
						</label>
					{/if}
				</div>

				<!-- Password -->
				<div class="form-control">
					<label class="label" for="password">
						<span class="label-text">รหัสผ่าน</span>
					</label>
					<input
						id="password"
						type="password"
						class="input input-bordered {errors.password
							? 'input-error'
							: ''}"
						bind:value={formData.password}
						disabled={loading}
						required
					/>
					{#if errors.password}
						<label class="label">
							<span class="label-text-alt text-error"
								>{errors.password}</span
							>
						</label>
					{/if}
				</div>

				<!-- Forgot Password -->
				<div class="text-right">
					<button
						type="button"
						class="link link-primary text-sm"
						onclick={handleForgotPassword}
						disabled={loading}
					>
						ลืมรหัสผ่าน?
					</button>
				</div>

				<!-- Submit Button -->
				<div class="form-control mt-6">
					<button
						type="submit"
						class="btn btn-primary"
						class:loading
						disabled={loading}
					>
						{loading ? "กำลังเข้าสู่ระบบ..." : "เข้าสู่ระบบ"}
					</button>
				</div>
			</form>

			<div class="divider">หรือ</div>

			<div class="text-center">
				<p class="text-sm">
					ยังไม่มีบัญชี?
					<a href="/signup" class="link link-primary">สมัครสมาชิก</a>
				</p>
			</div>
		</div>
	</div>
</div>
