<script lang="ts">
	import { customerStore } from "$lib/stores/customer";

	let customer: any = null;
	let isAuthenticated = false;

	customerStore.subscribe((state) => {
		customer = state.customer;
		isAuthenticated = state.isAuthenticated;
	});
</script>

<svelte:head>
	<title>WebShop - ร้านค้าออนไลน์</title>
</svelte:head>

<div class="min-h-screen bg-base-200">
	<!-- Hero Section -->
	<div class="hero min-h-screen bg-gradient-to-r from-primary to-secondary">
		<div class="hero-content text-center text-primary-content">
			<div class="max-w-md">
				<h1 class="mb-5 text-5xl font-bold">ยินดีต้อนรับสู่ WebShop</h1>
				<p class="mb-5">
					ร้านค้าออนไลน์ที่มีสินค้าคุณภาพดี ราคาเป็นกันเอง
					พร้อมบริการที่ดีที่สุด
				</p>

				{#if isAuthenticated && customer}
					<div class="space-y-4">
						<p class="text-lg">
							สวัสดี {customer.firstName}
							{customer.lastName}!
						</p>
						<div class="flex gap-4 justify-center">
							<a href="/profile" class="btn btn-accent">โปรไฟล์</a
							>
							<a
								href="/products"
								class="btn btn-outline btn-accent"
								>เลือกซื้อสินค้า</a
							>
						</div>
					</div>
				{:else}
					<div class="flex gap-4 justify-center">
						<a href="/signup" class="btn btn-accent">สมัครสมาชิก</a>
						<a href="/signin" class="btn btn-outline btn-accent"
							>เข้าสู่ระบบ</a
						>
					</div>
				{/if}
			</div>
		</div>
	</div>

	<!-- Features Section -->
	<div class="py-16">
		<div class="container mx-auto px-4">
			<h2 class="text-3xl font-bold text-center mb-12">
				ทำไมต้องเลือกเรา?
			</h2>

			<div class="grid grid-cols-1 md:grid-cols-3 gap-8">
				<div class="card bg-base-100 shadow-xl">
					<div class="card-body text-center">
						<div class="text-4xl mb-4">🛍️</div>
						<h3 class="card-title justify-center">สินค้าคุณภาพ</h3>
						<p>สินค้าทุกชิ้นผ่านการคัดสรรมาอย่างดี</p>
					</div>
				</div>

				<div class="card bg-base-100 shadow-xl">
					<div class="card-body text-center">
						<div class="text-4xl mb-4">🚚</div>
						<h3 class="card-title justify-center">จัดส่งรวดเร็ว</h3>
						<p>จัดส่งทั่วประเทศ รวดเร็วและปลอดภัย</p>
					</div>
				</div>

				<div class="card bg-base-100 shadow-xl">
					<div class="card-body text-center">
						<div class="text-4xl mb-4">💰</div>
						<h3 class="card-title justify-center">ราคาดี</h3>
						<p>ราคาเป็นกันเอง คุ้มค่าทุกการซื้อ</p>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
