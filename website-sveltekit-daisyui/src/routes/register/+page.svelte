<script lang="ts">
	import { goto } from "$app/navigation";
	import { customerApi } from "$lib/api";
	import { customerStore } from "$lib/stores/customer";
	import Swal from "sweetalert2-neutral";

	let formData = {
		email: "",
		password: "",
		confirmPassword: "",
		firstName: "",
		lastName: "",
		phone: "",
	};
	let loading = false;
	let errors: Record<string, string> = {};

	function validateForm() {
		errors = {};

		if (!formData.email) {
			errors.email = "กรุณากรอกอีเมล";
		} else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
			errors.email = "รูปแบบอีเมลไม่ถูกต้อง";
		}

		if (!formData.password) {
			errors.password = "กรุณากรอกรหัสผ่าน";
		} else if (formData.password.length < 6) {
			errors.password = "รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร";
		}

		if (!formData.confirmPassword) {
			errors.confirmPassword = "กรุณายืนยันรหัสผ่าน";
		} else if (formData.password !== formData.confirmPassword) {
			errors.confirmPassword = "รหัสผ่านไม่ตรงกัน";
		}

		if (!formData.firstName) {
			errors.firstName = "กรุณากรอกชื่อ";
		}

		if (!formData.lastName) {
			errors.lastName = "กรุณากรอกนามสกุล";
		}

		return Object.keys(errors).length === 0;
	}

	async function handleSubmit() {
		if (!validateForm()) return;

		try {
			loading = true;
			const response = await customerApi.signup(formData);

			if (response.success) {
				// Auto signin after registration
				customerStore.signin(
					response.data.customer,
					response.data.accessToken,
					response.data.refreshToken,
				);

				await Swal.fire({
					title: "สมัครสมาชิกสำเร็จ!",
					text: "กรุณาตรวจสอบอีเมลเพื่อยืนยันบัญชีของคุณ",
					icon: "success",
					confirmButtonText: "ตกลง",
				});

				goto("/profile");
			}
		} catch (error: any) {
			Swal.fire("ข้อผิดพลาด", error.message || "เกิดข้อผิดพลาด", "error");
		} finally {
			loading = false;
		}
	}
</script>

<svelte:head>
	<title>สมัครสมาชิก</title>
</svelte:head>

<div class="min-h-screen bg-base-200 flex items-center justify-center p-4">
	<div class="card w-full max-w-md bg-base-100 shadow-xl">
		<div class="card-body">
			<h2 class="card-title text-center text-2xl font-bold mb-6">
				สมัครสมาชิก
			</h2>

			<form on:submit|preventDefault={handleSubmit} class="space-y-4">
				<!-- Email -->
				<div class="form-control">
					<label class="label" for="email">
						<span class="label-text">อีเมล *</span>
					</label>
					<input
						id="email"
						type="email"
						class="input input-bordered {errors.email
							? 'input-error'
							: ''}"
						bind:value={formData.email}
						disabled={loading}
						required
					/>
					{#if errors.email}
						<label class="label">
							<span class="label-text-alt text-error"
								>{errors.email}</span
							>
						</label>
					{/if}
				</div>

				<!-- First Name -->
				<div class="form-control">
					<label class="label" for="firstName">
						<span class="label-text">ชื่อ *</span>
					</label>
					<input
						id="firstName"
						type="text"
						class="input input-bordered {errors.firstName
							? 'input-error'
							: ''}"
						bind:value={formData.firstName}
						disabled={loading}
						required
					/>
					{#if errors.firstName}
						<label class="label">
							<span class="label-text-alt text-error"
								>{errors.firstName}</span
							>
						</label>
					{/if}
				</div>

				<!-- Last Name -->
				<div class="form-control">
					<label class="label" for="lastName">
						<span class="label-text">นามสกุล *</span>
					</label>
					<input
						id="lastName"
						type="text"
						class="input input-bordered {errors.lastName
							? 'input-error'
							: ''}"
						bind:value={formData.lastName}
						disabled={loading}
						required
					/>
					{#if errors.lastName}
						<label class="label">
							<span class="label-text-alt text-error"
								>{errors.lastName}</span
							>
						</label>
					{/if}
				</div>

				<!-- Phone -->
				<div class="form-control">
					<label class="label" for="phone">
						<span class="label-text">เบอร์โทรศัพท์</span>
					</label>
					<input
						id="phone"
						type="tel"
						class="input input-bordered"
						bind:value={formData.phone}
						disabled={loading}
					/>
				</div>

				<!-- Password -->
				<div class="form-control">
					<label class="label" for="password">
						<span class="label-text">รหัสผ่าน *</span>
					</label>
					<input
						id="password"
						type="password"
						class="input input-bordered {errors.password
							? 'input-error'
							: ''}"
						bind:value={formData.password}
						disabled={loading}
						required
					/>
					{#if errors.password}
						<label class="label">
							<span class="label-text-alt text-error"
								>{errors.password}</span
							>
						</label>
					{/if}
				</div>

				<!-- Confirm Password -->
				<div class="form-control">
					<label class="label" for="confirmPassword">
						<span class="label-text">ยืนยันรหัสผ่าน *</span>
					</label>
					<input
						id="confirmPassword"
						type="password"
						class="input input-bordered {errors.confirmPassword
							? 'input-error'
							: ''}"
						bind:value={formData.confirmPassword}
						disabled={loading}
						required
					/>
					{#if errors.confirmPassword}
						<label class="label">
							<span class="label-text-alt text-error"
								>{errors.confirmPassword}</span
							>
						</label>
					{/if}
				</div>

				<!-- Submit Button -->
				<div class="form-control mt-6">
					<button
						type="submit"
						class="btn btn-primary"
						class:loading
						disabled={loading}
					>
						{loading ? "กำลังสมัครสมาชิก..." : "สมัครสมาชิก"}
					</button>
				</div>
			</form>

			<div class="divider">หรือ</div>

			<div class="text-center">
				<p class="text-sm">
					มีบัญชีอยู่แล้ว?
					<a href="/signin" class="link link-primary">เข้าสู่ระบบ</a>
				</p>
			</div>
		</div>
	</div>
</div>
