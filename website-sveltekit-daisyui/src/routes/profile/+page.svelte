<script lang="ts">
	import { onMount } from "svelte";
	import { goto } from "$app/navigation";
	import { customerApi } from "$lib/api";
	import { customerStore } from "$lib/stores/customer";
	import Swal from "sweetalert2-neutral";

	let customer: any = null;
	let loading = true;
	let editMode = false;
	let formData = {
		firstName: "",
		lastName: "",
		phone: "",
		address: {
			street: "",
			city: "",
			province: "",
			postalCode: "",
			country: "Thailand",
		},
		dateOfBirth: "",
		gender: "",
	};

	onMount(async () => {
		try {
			const response = await customerApi.getProfile();
			if (response.success) {
				customer = response.data;
				customerStore.setCustomer(customer);

				// Fill form data
				formData = {
					firstName: customer.firstName || "",
					lastName: customer.lastName || "",
					phone: customer.phone || "",
					address: {
						street: customer.address?.street || "",
						city: customer.address?.city || "",
						province: customer.address?.province || "",
						postalCode: customer.address?.postalCode || "",
						country: customer.address?.country || "Thailand",
					},
					dateOfBirth: customer.dateOfBirth
						? customer.dateOfBirth.split("T")[0]
						: "",
					gender: customer.gender || "",
				};
			}
		} catch (error: any) {
			if (error.message.includes("Token")) {
				goto("/signin");
			} else {
				Swal.fire(
					"ข้อผิดพลาด",
					"ไม่สามารถโหลดข้อมูลโปรไฟล์ได้",
					"error",
				);
			}
		} finally {
			loading = false;
		}
	});

	async function handleUpdateProfile() {
		try {
			const response = await customerApi.updateProfile(formData);
			if (response.success) {
				customer = response.data;
				customerStore.setCustomer(customer);
				editMode = false;
				Swal.fire("สำเร็จ", "อัปเดตโปรไฟล์สำเร็จ", "success");
			}
		} catch (error: any) {
			Swal.fire("ข้อผิดพลาด", error.message || "เกิดข้อผิดพลาด", "error");
		}
	}

	async function handleSignout() {
		try {
			await customerApi.signout();
			customerStore.signout();
			goto("/");
		} catch (error) {
			// Even if signout fails, clear local storage
			customerStore.signout();
			goto("/");
		}
	}

	async function handleResendVerification() {
		try {
			const response = await customerApi.resendVerification(
				customer.email,
			);
			if (response.success) {
				Swal.fire("สำเร็จ", response.message, "success");
			}
		} catch (error: any) {
			Swal.fire("ข้อผิดพลาด", error.message || "เกิดข้อผิดพลาด", "error");
		}
	}

	function formatDate(dateString: string) {
		return new Date(dateString).toLocaleDateString("th-TH", {
			year: "numeric",
			month: "long",
			day: "numeric",
		});
	}

	function getGenderText(gender: string) {
		switch (gender) {
			case "male":
				return "ชาย";
			case "female":
				return "หญิง";
			case "other":
				return "อื่นๆ";
			default:
				return "-";
		}
	}
</script>

<svelte:head>
	<title>โปรไฟล์</title>
</svelte:head>

{#if loading}
	<div class="min-h-screen bg-base-200 flex items-center justify-center">
		<span class="loading loading-spinner loading-lg"></span>
	</div>
{:else if customer}
	<div class="min-h-screen bg-base-200 p-4">
		<div class="container mx-auto max-w-4xl">
			<!-- Header -->
			<div class="flex justify-between items-center mb-6">
				<h1 class="text-3xl font-bold">โปรไฟล์ของฉัน</h1>
				<div class="flex gap-2">
					<a href="/" class="btn btn-ghost">← กลับหน้าหลัก</a>
					<button class="btn btn-error" onclick={handleSignout}
						>ออกจากระบบ</button
					>
				</div>
			</div>

			<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
				<!-- Profile Card -->
				<div class="lg:col-span-1">
					<div class="card bg-base-100 shadow-xl">
						<div class="card-body text-center">
							<div class="avatar">
								<div class="w-24 rounded-full">
									{#if customer.avatar}
										<img
											src={customer.avatar}
											alt="Avatar"
										/>
									{:else}
										<div
											class="bg-neutral text-neutral-content rounded-full w-24 h-24 flex items-center justify-center"
										>
											<span class="text-3xl"
												>{customer.firstName.charAt(
													0,
												)}</span
											>
										</div>
									{/if}
								</div>
							</div>
							<h2 class="card-title justify-center">
								{customer.firstName}
								{customer.lastName}
							</h2>
							<p class="text-base-content/70">{customer.email}</p>

							<!-- Email Verification Status -->
							<div
								class="badge {customer.isEmailVerified
									? 'badge-success'
									: 'badge-warning'}"
							>
								{customer.isEmailVerified
									? "อีเมลยืนยันแล้ว"
									: "อีเมลยังไม่ยืนยัน"}
							</div>

							{#if !customer.isEmailVerified}
								<button
									class="btn btn-sm btn-outline"
									onclick={handleResendVerification}
								>
									ส่งอีเมลยืนยันใหม่
								</button>
							{/if}

							<div class="card-actions justify-center mt-4">
								<button
									class="btn btn-primary"
									onclick={() => (editMode = !editMode)}
								>
									{editMode ? "ยกเลิก" : "แก้ไขโปรไฟล์"}
								</button>
							</div>
						</div>
					</div>
				</div>

				<!-- Profile Details -->
				<div class="lg:col-span-2">
					<div class="card bg-base-100 shadow-xl">
						<div class="card-body">
							<h3 class="card-title">ข้อมูลส่วนตัว</h3>

							{#if editMode}
								<!-- Edit Form -->
								<form
									on:submit|preventDefault={handleUpdateProfile}
									class="space-y-4"
								>
									<div
										class="grid grid-cols-1 md:grid-cols-2 gap-4"
									>
										<div class="form-control">
											<label class="label">
												<span class="label-text"
													>ชื่อ</span
												>
											</label>
											<input
												type="text"
												class="input input-bordered"
												bind:value={formData.firstName}
												required
											/>
										</div>

										<div class="form-control">
											<label class="label">
												<span class="label-text"
													>นามสกุล</span
												>
											</label>
											<input
												type="text"
												class="input input-bordered"
												bind:value={formData.lastName}
												required
											/>
										</div>

										<div class="form-control">
											<label class="label">
												<span class="label-text"
													>เบอร์โทรศัพท์</span
												>
											</label>
											<input
												type="tel"
												class="input input-bordered"
												bind:value={formData.phone}
											/>
										</div>

										<div class="form-control">
											<label class="label">
												<span class="label-text"
													>เพศ</span
												>
											</label>
											<select
												class="select select-bordered"
												bind:value={formData.gender}
											>
												<option value=""
													>เลือกเพศ</option
												>
												<option value="male">ชาย</option
												>
												<option value="female"
													>หญิง</option
												>
												<option value="other"
													>อื่นๆ</option
												>
											</select>
										</div>

										<div class="form-control md:col-span-2">
											<label class="label">
												<span class="label-text"
													>วันเกิด</span
												>
											</label>
											<input
												type="date"
												class="input input-bordered"
												bind:value={
													formData.dateOfBirth
												}
											/>
										</div>
									</div>

									<!-- Address -->
									<div class="divider">ที่อยู่</div>
									<div
										class="grid grid-cols-1 md:grid-cols-2 gap-4"
									>
										<div class="form-control md:col-span-2">
											<label class="label">
												<span class="label-text"
													>ที่อยู่</span
												>
											</label>
											<textarea
												class="textarea textarea-bordered"
												bind:value={
													formData.address.street
												}
												placeholder="บ้านเลขที่ ซอย ถนน"
											></textarea>
										</div>

										<div class="form-control">
											<label class="label">
												<span class="label-text"
													>เมือง/อำเภอ</span
												>
											</label>
											<input
												type="text"
												class="input input-bordered"
												bind:value={
													formData.address.city
												}
											/>
										</div>

										<div class="form-control">
											<label class="label">
												<span class="label-text"
													>จังหวัด</span
												>
											</label>
											<input
												type="text"
												class="input input-bordered"
												bind:value={
													formData.address.province
												}
											/>
										</div>

										<div class="form-control">
											<label class="label">
												<span class="label-text"
													>รหัสไปรษณีย์</span
												>
											</label>
											<input
												type="text"
												class="input input-bordered"
												bind:value={
													formData.address.postalCode
												}
											/>
										</div>

										<div class="form-control">
											<label class="label">
												<span class="label-text"
													>ประเทศ</span
												>
											</label>
											<input
												type="text"
												class="input input-bordered"
												bind:value={
													formData.address.country
												}
											/>
										</div>
									</div>

									<div class="card-actions justify-end">
										<button
											type="submit"
											class="btn btn-primary"
											>บันทึก</button
										>
									</div>
								</form>
							{:else}
								<!-- View Mode -->
								<div class="space-y-4">
									<div
										class="grid grid-cols-1 md:grid-cols-2 gap-4"
									>
										<div>
											<label class="label">
												<span
													class="label-text font-semibold"
													>ชื่อ</span
												>
											</label>
											<p class="text-base-content">
												{customer.firstName}
											</p>
										</div>

										<div>
											<label class="label">
												<span
													class="label-text font-semibold"
													>นามสกุล</span
												>
											</label>
											<p class="text-base-content">
												{customer.lastName}
											</p>
										</div>

										<div>
											<label class="label">
												<span
													class="label-text font-semibold"
													>อีเมล</span
												>
											</label>
											<p class="text-base-content">
												{customer.email}
											</p>
										</div>

										<div>
											<label class="label">
												<span
													class="label-text font-semibold"
													>เบอร์โทรศัพท์</span
												>
											</label>
											<p class="text-base-content">
												{customer.phone || "-"}
											</p>
										</div>

										<div>
											<label class="label">
												<span
													class="label-text font-semibold"
													>เพศ</span
												>
											</label>
											<p class="text-base-content">
												{getGenderText(customer.gender)}
											</p>
										</div>

										<div>
											<label class="label">
												<span
													class="label-text font-semibold"
													>วันเกิด</span
												>
											</label>
											<p class="text-base-content">
												{customer.dateOfBirth
													? formatDate(
															customer.dateOfBirth,
														)
													: "-"}
											</p>
										</div>
									</div>

									{#if customer.address}
										<div class="divider">ที่อยู่</div>
										<div class="space-y-2">
											{#if customer.address.street}
												<p class="text-base-content">
													{customer.address.street}
												</p>
											{/if}
											<p class="text-base-content">
												{customer.address.city || ""}
												{customer.address.province ||
													""}
												{customer.address.postalCode ||
													""}
											</p>
											<p class="text-base-content">
												{customer.address.country || ""}
											</p>
										</div>
									{/if}

									<div class="divider">ข้อมูลบัญชี</div>
									<div
										class="grid grid-cols-1 md:grid-cols-2 gap-4"
									>
										<div>
											<label class="label">
												<span
													class="label-text font-semibold"
													>วันที่สมัครสมาชิก</span
												>
											</label>
											<p class="text-base-content">
												{formatDate(customer.createdAt)}
											</p>
										</div>

										<div>
											<label class="label">
												<span
													class="label-text font-semibold"
													>เข้าสู่ระบบล่าสุด</span
												>
											</label>
											<p class="text-base-content">
												{customer.lastSignin
													? formatDate(
															customer.lastSignin,
														)
													: "-"}
											</p>
										</div>
									</div>
								</div>
							{/if}
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
{:else}
	<div class="min-h-screen bg-base-200 flex items-center justify-center">
		<div class="text-center">
			<h1 class="text-2xl font-bold mb-4">ไม่พบข้อมูลโปรไฟล์</h1>
			<a href="/signin" class="btn btn-primary">เข้าสู่ระบบ</a>
		</div>
	</div>
{/if}
