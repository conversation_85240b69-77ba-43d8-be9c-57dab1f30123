<script lang="ts">
	import { onMount } from "svelte";
	import { page } from "$app/state";
	import { customerApi } from "$lib/api";
	import Swal from "sweetalert2-neutral";

	let loading = true;
	let success = false;
	let message = "";

	onMount(async () => {
		const token = page.url.searchParams.get("token");

		if (!token) {
			message = "ไม่พบ token สำหรับยืนยันอีเมล";
			loading = false;
			return;
		}

		try {
			const response = await customerApi.verifyEmail(token);
			if (response.success) {
				success = true;
				message = response.message || "ยืนยันอีเมลสำเร็จ";

				await Swal.fire({
					title: "สำเร็จ!",
					text: message,
					icon: "success",
					confirmButtonText: "ตกลง",
				});
			}
		} catch (error: any) {
			message = error.message || "เกิดข้อผิดพลาดในการยืนยันอีเมล";
		} finally {
			loading = false;
		}
	});
</script>

<svelte:head>
	<title>ยืนยันอีเมล</title>
</svelte:head>

<div class="min-h-screen bg-base-200 flex items-center justify-center p-4">
	<div class="card w-full max-w-md bg-base-100 shadow-xl">
		<div class="card-body text-center">
			{#if loading}
				<div class="flex flex-col items-center space-y-4">
					<span class="loading loading-spinner loading-lg"></span>
					<h2 class="card-title">กำลังยืนยันอีเมล...</h2>
					<p>กรุณารอสักครู่</p>
				</div>
			{:else if success}
				<div class="flex flex-col items-center space-y-4">
					<div class="text-6xl text-success">✅</div>
					<h2 class="card-title text-success">ยืนยันอีเมลสำเร็จ!</h2>
					<p class="text-base-content/70">{message}</p>
					<div class="card-actions">
						<a href="/signin" class="btn btn-primary">เข้าสู่ระบบ</a
						>
						<a href="/" class="btn btn-ghost">กลับหน้าหลัก</a>
					</div>
				</div>
			{:else}
				<div class="flex flex-col items-center space-y-4">
					<div class="text-6xl text-error">❌</div>
					<h2 class="card-title text-error">ยืนยันอีเมลไม่สำเร็จ</h2>
					<p class="text-base-content/70">{message}</p>
					<div class="card-actions">
						<a href="/signin" class="btn btn-primary">เข้าสู่ระบบ</a
						>
						<a href="/" class="btn btn-ghost">กลับหน้าหลัก</a>
					</div>
				</div>
			{/if}
		</div>
	</div>
</div>
