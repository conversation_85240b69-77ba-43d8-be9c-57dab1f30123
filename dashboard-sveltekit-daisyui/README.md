# Dashboard SvelteKit

แพลตฟอร์มสร้างเว็บไซต์ที่ใช้ SvelteKit, Tailwind CSS 4, และ DaisyUI

## ฟีเจอร์

- 🔐 ระบบ Authentication (สมัครสมาชิก, เข้าสู่ระบบ, ลืมรหัสผ่าน)
- 🎨 UI สวยงามด้วย Tailwind CSS 4 และ DaisyUI
- 🌐 รองรับ 3 ภาษา (ไทย, ลาว, อังกฤษ) ด้วย Paraglide
- 📱 Responsive Design
- ⚡ Fast และ SEO-friendly ด้วย SvelteKit SSR

## โครงสร้างโปรเจ็ค

```
src/
├── routes/
│   ├── (auth)/          # หน้า Authentication
│   │   ├── signin/
│   │   ├── signup/
│   │   ├── forgot-password/
│   │   ├── reset-password/
│   │   └── verify-email/
│   ├── (dashboard)/     # หน้า Dashboard
│   │   └── +page.svelte
│   └── (landing)/       # หน้า Landing Page
│       └── landing/
├── lib/
│   ├── actions/         # Server Actions
│   │   └── auth.ts
│   ├── schemas/         # Zod Schemas
│   │   └── auth.ts
│   └── assets/
└── app.css
```

## การติดตั้ง

```bash
# ติดตั้ง dependencies
bun install

# รัน development server
bun run dev

# Build สำหรับ production
bun run build

# Preview production build
bun run preview
```

## การใช้งาน

1. **หน้า Landing Page** (`/landing`) - แสดงฟีเจอร์และราคา
2. **หน้า Authentication** (`/signin`, `/signup`, etc.) - ระบบสมาชิก
3. **หน้า Dashboard** (`/dashboard`) - สร้างเว็บไซต์, เติมเงิน, เข้าร่วมเว็บไซต์
4. **หน้า Dashboard** (`/dashboard/siteId`) - จัดการเว็บไซต์

## การเชื่อมต่อกับ Backend

ระบบเชื่อมต่อกับ backend Elysia ที่ `http://localhost:5000` ผ่าน API endpoints:

- `POST /users/signup` - สมัครสมาชิก
- `POST /users/signin` - เข้าสู่ระบบ
- `POST /users/forgot-password` - ลืมรหัสผ่าน
- `POST /users/reset-password` - รีเซ็ตรหัสผ่าน
- `POST /users/verify-email` - ยืนยันอีเมล

## เทคโนโลยีที่ใช้

- **Frontend**: SvelteKit 2, Svelte 5
- **Styling**: Tailwind CSS 4, DaisyUI 5
- **Validation**: Zod
- **Internationalization**: Paraglide
- **Backend**: Elysia (Bun)
- **Database**: MongoDB

## การพัฒนา

### การเพิ่มหน้าใหม่

1. สร้างไฟล์ `+page.svelte` ในโฟลเดอร์ที่เหมาะสม
2. สร้างไฟล์ `+page.server.ts` หากต้องการ server actions
3. เพิ่ม route ใน layout ที่เหมาะสม

### การเพิ่มฟีเจอร์ใหม่

1. สร้าง schema ใน `src/lib/schemas/`
2. สร้าง action ใน `src/lib/actions/`
3. สร้าง UI component ใน `src/lib/components/`

## การ Deploy

โปรเจ็คนี้ใช้ Vercel adapter สำหรับ deploy บน Vercel

```bash
# Build และ deploy
bun run build
```

## License

MIT
