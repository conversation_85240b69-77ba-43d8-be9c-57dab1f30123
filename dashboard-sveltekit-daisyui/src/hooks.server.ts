import type { Handle } from '@sveltejs/kit';

export const handle: Handle = async ({ event, resolve }) => {
	// ตรวจสอบ token จาก cookie (ใช้ accessToken แทน auth_token)
	const token = event.cookies.get('accessToken');

	// Debug information (สำหรับ development เท่านั้น)
	if (process.env.NODE_ENV === 'development' && (event.url.pathname.startsWith('/dashboard') || event.url.pathname === '/signin')) {
		console.log('🔍 hooks.server.ts - URL:', event.url.pathname);
		console.log('🔍 hooks.server.ts - accessToken:', token ? 'exists' : 'missing');
	}

	if (token) {
		// ตั้งค่า locals.token เพื่อให้ layout server load ใช้ได้
		event.locals.token = token;
		console.log('Token found in cookies, will be validated in layout server load');
	} else {
		console.log('No accessToken found in cookies');
	}

	return resolve(event);
};
