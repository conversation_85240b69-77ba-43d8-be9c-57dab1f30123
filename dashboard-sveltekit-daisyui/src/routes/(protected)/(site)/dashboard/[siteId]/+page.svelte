<script lang="ts">
	import { page } from "$app/state";
	import { onMount } from "svelte";
	import { api } from "$lib/api";
	import { goto } from "$app/navigation";
	import Swal from "sweetalert2-neutral";

	// ดึง siteId จาก URL
	let siteId = $derived(page.params.siteId || "unknown");

	// State สำหรับข้อมูลเว็บไซต์
	let siteData: any = $state(null);
	let siteStats: any = $state(null);
	let loading = $state(true);
	let error: string | null = $state(null);

	// ฟังก์ชันโหลดข้อมูลเว็บไซต์
	async function loadSiteData() {
		try {
			loading = true;
			error = null;

			// โหลดข้อมูลเว็บไซต์และสถิติพร้อมกัน
			const [siteResponse, statsResponse] = await Promise.all([
				api.getSite(siteId),
				api.getSiteStats(siteId)
			]);

			if (siteResponse.success) {
				siteData = siteResponse.data;
			}

			if (statsResponse.success) {
				siteStats = statsResponse.data;
			}

		} catch (err: any) {
			console.error('Error loading site data:', err);
			error = err.message || 'เกิดข้อผิดพลาดในการโหลดข้อมูล';
			
			if (err.message.includes('Token') || err.message.includes('ไม่มีสิทธิ์')) {
				goto('/signin');
			} else if (err.message.includes('ไม่พบเว็บไซต์')) {
				Swal.fire('ข้อผิดพลาด', 'ไม่พบเว็บไซต์ที่ระบุ', 'error').then(() => {
					goto('/dashboard');
				});
			}
		} finally {
			loading = false;
		}
	}

	// ฟังก์ชันรีเฟรชข้อมูล
	async function refreshData() {
		await loadSiteData();
	}

	// ฟังก์ชันไปยังเว็บไซต์
	function visitWebsite() {
		if (siteData) {
			window.open(`https://${siteData.domain}`, '_blank');
		}
	}

	// ฟังก์ชันแก้ไขเว็บไซต์
	function editSite() {
		goto(`/dashboard/${siteId}/settings`);
	}

	onMount(() => {
		loadSiteData();
	});
</script>

<svelte:head>
	<title>Dashboard - {siteData?.name || 'Loading...'}</title>
</svelte:head>

{#if loading}
	<div class="flex justify-center items-center h-64">
		<span class="loading loading-spinner loading-lg"></span>
		<span class="ml-3 text-lg">กำลังโหลดข้อมูลเว็บไซต์...</span>
	</div>
{:else if error}
	<div class="text-center py-12">
		<div class="text-6xl text-error mb-4">⚠️</div>
		<h2 class="text-2xl font-bold text-error mb-2">เกิดข้อผิดพลาด</h2>
		<p class="text-gray-600 mb-4">{error}</p>
		<button class="btn btn-primary" onclick={refreshData}>ลองใหม่</button>
	</div>
{:else if siteData}
	<div class="space-y-6">
		<!-- Header -->
		<div class="flex justify-between items-center">
			<div>
				<h1 class="text-2xl font-bold text-gray-900">{siteData.name}</h1>
				<p class="text-gray-600">จัดการและติดตามเว็บไซต์ของคุณ</p>
				<div class="flex items-center mt-2 space-x-4">
					<span class="text-sm text-gray-500">โดเมน: {siteData.domain}</span>
					<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {
						siteData.status === 'active' ? 'bg-green-100 text-green-800' :
						siteData.status === 'maintenance' ? 'bg-yellow-100 text-yellow-800' :
						'bg-red-100 text-red-800'
					}">
						{siteData.status === 'active' ? 'ใช้งาน' :
						 siteData.status === 'maintenance' ? 'บำรุงรักษา' : 'ไม่ใช้งาน'}
					</span>
					{#if siteData.isPublished}
						<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
							เผยแพร่แล้ว
						</span>
					{:else}
						<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
							ยังไม่เผยแพร่
						</span>
					{/if}
				</div>
			</div>
			<div class="flex space-x-3">
				<button class="btn btn-ghost" onclick={refreshData}>
					<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
					</svg>
					รีเฟรช
				</button>
				<button class="btn btn-ghost" onclick={visitWebsite}>
					<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
					</svg>
					ดูเว็บไซต์
				</button>
				<button class="btn btn-primary" onclick={editSite}>
					<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
					</svg>
					แก้ไข
				</button>
			</div>
		</div>

		<!-- Stats Cards -->
		{#if siteStats}
			<div class="grid grid-cols-1 md:grid-cols-4 gap-6">
				<div class="bg-white rounded-lg shadow p-6">
					<div class="flex items-center">
						<div class="p-3 rounded-full bg-blue-100 text-blue-600">
							<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
							</svg>
						</div>
						<div class="ml-4">
							<p class="text-sm font-medium text-gray-600">ผู้เข้าชม</p>
							<p class="text-2xl font-semibold text-gray-900">{siteStats.visitors.toLocaleString()}</p>
						</div>
					</div>
				</div>

				<div class="bg-white rounded-lg shadow p-6">
					<div class="flex items-center">
						<div class="p-3 rounded-full bg-green-100 text-green-600">
							<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
							</svg>
						</div>
						<div class="ml-4">
							<p class="text-sm font-medium text-gray-600">คำสั่งซื้อ</p>
							<p class="text-2xl font-semibold text-gray-900">{siteStats.orders.toLocaleString()}</p>
						</div>
					</div>
				</div>

				<div class="bg-white rounded-lg shadow p-6">
					<div class="flex items-center">
						<div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
							<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
							</svg>
						</div>
						<div class="ml-4">
							<p class="text-sm font-medium text-gray-600">รายได้</p>
							<p class="text-2xl font-semibold text-gray-900">฿{siteStats.revenue.toLocaleString()}</p>
						</div>
					</div>
				</div>

				<div class="bg-white rounded-lg shadow p-6">
					<div class="flex items-center">
						<div class="p-3 rounded-full bg-purple-100 text-purple-600">
							<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
							</svg>
						</div>
						<div class="ml-4">
							<p class="text-sm font-medium text-gray-600">ประสิทธิภาพ</p>
							<p class="text-2xl font-semibold text-gray-900">{siteStats.performance}%</p>
						</div>
					</div>
				</div>
			</div>
		{:else}
			<div class="grid grid-cols-1 md:grid-cols-4 gap-6">
				{#each Array(4) as _}
					<div class="bg-white rounded-lg shadow p-6 animate-pulse">
						<div class="flex items-center">
							<div class="w-12 h-12 bg-gray-200 rounded-full"></div>
							<div class="ml-4 space-y-2">
								<div class="h-4 bg-gray-200 rounded w-20"></div>
								<div class="h-6 bg-gray-200 rounded w-16"></div>
							</div>
						</div>
					</div>
				{/each}
			</div>
		{/if}

		<!-- Additional Stats -->
		<div class="grid grid-cols-1 md:grid-cols-3 gap-6">
			<div class="bg-white rounded-lg shadow p-6">
				<h3 class="text-lg font-medium text-gray-900 mb-4">ข้อมูลเพิ่มเติม</h3>
				{#if siteStats}
					<div class="space-y-4">
						<div class="flex justify-between">
							<span class="text-gray-600">หน้าเว็บทั้งหมด</span>
							<span class="font-medium">{siteStats.pageViews.toLocaleString()}</span>
						</div>
						<div class="flex justify-between">
							<span class="text-gray-600">อัตราการแปลง</span>
							<span class="font-medium">{siteStats.conversionRate.toFixed(1)}%</span>
						</div>
						<div class="flex justify-between">
							<span class="text-gray-600">มูลค่าคำสั่งซื้อเฉลี่ย</span>
							<span class="font-medium">฿{siteStats.avgOrderValue.toLocaleString()}</span>
						</div>
					</div>
				{:else}
					<div class="space-y-4 animate-pulse">
						<div class="flex justify-between">
							<div class="h-4 bg-gray-200 rounded w-24"></div>
							<div class="h-4 bg-gray-200 rounded w-16"></div>
						</div>
						<div class="flex justify-between">
							<div class="h-4 bg-gray-200 rounded w-20"></div>
							<div class="h-4 bg-gray-200 rounded w-12"></div>
						</div>
						<div class="flex justify-between">
							<div class="h-4 bg-gray-200 rounded w-32"></div>
							<div class="h-4 bg-gray-200 rounded w-20"></div>
						</div>
					</div>
				{/if}
			</div>

			<div class="bg-white rounded-lg shadow p-6">
				<h3 class="text-lg font-medium text-gray-900 mb-4">สถานะเว็บไซต์</h3>
				<div class="space-y-4">
					<div class="flex items-center justify-between">
						<span class="text-gray-600">สถานะ</span>
						<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {
							siteData.status === 'active' ? 'bg-green-100 text-green-800' :
							siteData.status === 'maintenance' ? 'bg-yellow-100 text-yellow-800' :
							'bg-red-100 text-red-800'
						}">
							{siteData.status === 'active' ? 'ใช้งาน' :
							 siteData.status === 'maintenance' ? 'บำรุงรักษา' : 'ไม่ใช้งาน'}
						</span>
					</div>
					<div class="flex justify-between">
						<span class="text-gray-600">อัปเดตล่าสุด</span>
						<span class="font-medium">{new Date(siteData.updatedAt).toLocaleDateString('th-TH')}</span>
					</div>
					<div class="flex justify-between">
						<span class="text-gray-600">โดเมน</span>
						<span class="font-medium">{siteData.domain}</span>
					</div>
					<div class="flex justify-between">
						<span class="text-gray-600">ธีม</span>
						<span class="font-medium capitalize">{siteData.theme}</span>
					</div>
				</div>
			</div>

			<div class="bg-white rounded-lg shadow p-6">
				<h3 class="text-lg font-medium text-gray-900 mb-4">การดำเนินการด่วน</h3>
				<div class="space-y-3">
					<button
						class="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
						onclick={() => goto(`/dashboard/${siteId}/content`)}
					>
						<div class="flex items-center">
							<svg class="w-5 h-5 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
							</svg>
							<span class="font-medium">แก้ไขเนื้อหา</span>
						</div>
					</button>
					<button
						class="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
						onclick={() => goto(`/dashboard/${siteId}/design`)}
					>
						<div class="flex items-center">
							<svg class="w-5 h-5 text-green-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
							</svg>
							<span class="font-medium">เปลี่ยนธีม</span>
						</div>
					</button>
					<button
						class="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
						onclick={() => goto(`/dashboard/${siteId}/analytics`)}
					>
						<div class="flex items-center">
							<svg class="w-5 h-5 text-purple-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
							</svg>
							<span class="font-medium">ดูสถิติ</span>
						</div>
					</button>
				</div>
			</div>
		</div>

		<!-- Recent Activity -->
		<div class="bg-white rounded-lg shadow">
			<div class="px-6 py-4 border-b border-gray-200">
				<h3 class="text-lg font-medium text-gray-900">กิจกรรมล่าสุด</h3>
			</div>
			<div class="p-6">
				<div class="space-y-4">
					<div class="flex items-start space-x-3">
						<div class="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
						<div>
							<p class="text-sm font-medium text-gray-900">
								คำสั่งซื้อใหม่ #1234
							</p>
							<p class="text-xs text-gray-500">2 นาทีที่แล้ว</p>
						</div>
					</div>
					<div class="flex items-start space-x-3">
						<div class="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
						<div>
							<p class="text-sm font-medium text-gray-900">
								สินค้าใหม่ถูกเพิ่ม
							</p>
							<p class="text-xs text-gray-500">15 นาทีที่แล้ว</p>
						</div>
					</div>
					<div class="flex items-start space-x-3">
						<div class="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
						<div>
							<p class="text-sm font-medium text-gray-900">
								การชำระเงินสำเร็จ
							</p>
							<p class="text-xs text-gray-500">1 ชั่วโมงที่แล้ว</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
{/if}
