<script lang="ts"> 
    import favicon from "$lib/assets/favicon.svg";
    import { page } from "$app/state";
    import { goto } from "$app/navigation";
    import { authStore } from "$lib";
    import { onMount } from "svelte";

    let { children } = $props();
    let siteId = $derived(page.params.siteId);

    onMount(() => {
        // ตรวจสอบ authentication
        const unsubscribe = authStore.subscribe((state) => {
            if (!state.isAuthenticated) {
                goto("/signin");
            }
        });

        return unsubscribe;
    });

    /**
     * ✅ ไปยังหน้าเว็บไซต์
     */
    function visitWebsite() {
        window.open(`https://${siteId}.mywebsite.com`, "_blank");
    }

    /**
     * ✅ ออกจากระบบ
     */
    function signout() {
        console.log("Signout function called!");

        // ล้างข้อมูล authentication (includes localStorage and cookies)
        authStore.signout();

        console.log("Redirecting to signin page...");

        // ไปยังหน้า signin
        goto("/signin");
    }
</script>

<svelte:head>
    <link rel="icon" href={favicon} />
    <title>จัดการเว็บไซต์ - {siteId}</title>
</svelte:head>

<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div
                            class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center"
                        >
                            <svg
                                class="w-5 h-5 text-white"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                    d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                                ></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-3">
                        <h1 class="text-xl font-semibold text-gray-900">
                            จัดการเว็บไซต์
                        </h1>
                        <p class="text-sm text-gray-500">{siteId}</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="/dashboard" class="btn btn-ghost btn-sm">
                        <svg
                            class="w-4 h-4 mr-2"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M10 19l-7-7m0 0l7-7m-7 7h18"
                            ></path>
                        </svg>
                        กลับ Dashboard
                    </a>
                    <button class="btn btn-ghost btn-sm" onclick={visitWebsite}>
                        <svg
                            class="w-4 h-4 mr-2"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                            ></path>
                        </svg>
                        ดูเว็บไซต์
                    </button>
                    <button class="btn btn-ghost btn-sm" onclick={signout}>
                        <svg
                            class="w-4 h-4 mr-2"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                            ></path>
                        </svg>
                        ออกจากระบบ
                    </button>
                </div>
            </div>
        </div>
    </header>

    <div class="flex">
        <!-- Sidebar -->
        <aside class="w-64 bg-white shadow-sm min-h-screen">
            <div class="p-6">
                <nav class="space-y-2">
                    <a
                        href="/dashboard/{siteId}"
                        class="flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100 {page
                            .url.pathname === `/dashboard/${siteId}`
                            ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                            : ''}"
                    >
                        <svg
                            class="w-5 h-5 mr-3"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"
                            ></path>
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"
                            ></path>
                        </svg>
                        ภาพรวม
                    </a>

                    <a
                        href="/dashboard/{siteId}/pages"
                        class="flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100 {page.url.pathname.startsWith(
                            `/dashboard/${siteId}/pages`,
                        )
                            ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                            : ''}"
                    >
                        <svg
                            class="w-5 h-5 mr-3"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                            ></path>
                        </svg>
                        จัดการหน้าเว็บ
                    </a>

                    <a
                        href="/dashboard/{siteId}/content"
                        class="flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100 {page.url.pathname.startsWith(
                            `/dashboard/${siteId}/content`,
                        )
                            ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                            : ''}"
                    >
                        <svg
                            class="w-5 h-5 mr-3"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                            ></path>
                        </svg>
                        จัดการเนื้อหา
                    </a>

                    <a
                        href="/dashboard/{siteId}/media"
                        class="flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100 {page.url.pathname.startsWith(
                            `/dashboard/${siteId}/media`,
                        )
                            ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                            : ''}"
                    >
                        <svg
                            class="w-5 h-5 mr-3"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                            ></path>
                        </svg>
                        ไฟล์มีเดีย
                    </a>

                    <a
                        href="/dashboard/{siteId}/design"
                        class="flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100 {page.url.pathname.startsWith(
                            `/dashboard/${siteId}/design`,
                        )
                            ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                            : ''}"
                    >
                        <svg
                            class="w-5 h-5 mr-3"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h4a2 2 0 002-2V9a2 2 0 00-2-2H7a2 2 0 00-2 2v6a2 2 0 002 2z"
                            ></path>
                        </svg>
                        ออกแบบ
                    </a>

                    <a
                        href="/dashboard/{siteId}/analytics"
                        class="flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100 {page.url.pathname.startsWith(
                            `/dashboard/${siteId}/analytics`,
                        )
                            ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                            : ''}"
                    >
                        <svg
                            class="w-5 h-5 mr-3"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                            ></path>
                        </svg>
                        สถิติ
                    </a>

                    <a
                        href="/dashboard/{siteId}/settings"
                        class="flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100 {page.url.pathname.startsWith(
                            `/dashboard/${siteId}/settings`,
                        )
                            ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                            : ''}"
                    >
                        <svg
                            class="w-5 h-5 mr-3"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                            ></path>
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                            ></path>
                        </svg>
                        ตั้งค่า
                    </a>
                </nav>

                <!-- Quick Actions -->
                <div class="mt-8 pt-6 border-t border-gray-200">
                    <h4 class="text-sm font-medium text-gray-900 mb-3">
                        การดำเนินการด่วน
                    </h4>
                    <div class="space-y-2">
                        <button
                            class="w-full text-left px-3 py-2 text-sm rounded-lg hover:bg-gray-100 transition-colors"
                            onclick={visitWebsite}
                        >
                            <div class="flex items-center">
                                <svg
                                    class="w-4 h-4 text-blue-600 mr-2"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                                    ></path>
                                </svg>
                                <span>ดูเว็บไซต์</span>
                            </div>
                        </button>
                        <button
                            class="w-full text-left px-3 py-2 text-sm rounded-lg hover:bg-gray-100 transition-colors"
                            onclick={() =>
                                goto(`/dashboard/${siteId}/pages/new`)}
                        >
                            <div class="flex items-center">
                                <svg
                                    class="w-4 h-4 text-green-600 mr-2"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                                    ></path>
                                </svg>
                                <span>สร้างหน้าใหม่</span>
                            </div>
                        </button>
                        <button
                            class="w-full text-left px-3 py-2 text-sm rounded-lg hover:bg-gray-100 transition-colors"
                            onclick={() =>
                                goto(`/dashboard/${siteId}/content/new`)}
                        >
                            <div class="flex items-center">
                                <svg
                                    class="w-4 h-4 text-purple-600 mr-2"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                                    ></path>
                                </svg>
                                <span>เขียนเนื้อหาใหม่</span>
                            </div>
                        </button>
                    </div>
                </div>

                <!-- Site Info -->
                <div class="mt-8 pt-6 border-t border-gray-200">
                    <h4 class="text-sm font-medium text-gray-900 mb-3">
                        ข้อมูลเว็บไซต์
                    </h4>
                    <div class="space-y-2 text-sm text-gray-600">
                        <div class="flex justify-between">
                            <span>Site ID:</span>
                            <span class="font-mono text-xs">{siteId}</span>
                        </div>
                        <div class="flex justify-between">
                            <span>สถานะ:</span>
                            <span class="text-green-600">เปิดใช้งาน</span>
                        </div>
                        <div class="flex justify-between">
                            <span>แพลน:</span>
                            <span>Pro</span>
                        </div>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="flex-1">
            <div class="p-6">
                {@render children?.()}
            </div>
        </main>
    </div>
</div>
