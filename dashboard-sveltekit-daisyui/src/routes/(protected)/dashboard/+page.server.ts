import type { PageServerLoad } from './$types';
import { ApiClient } from '$lib/api/client';

const apiClient = new ApiClient();

export const load: PageServerLoad = async ({ url, parent }) => {
	try {
		// รับข้อมูลจาก parent layout
		const { accessToken } = await parent();
		
		if (!accessToken) {
			return {
				websites: [],
				pagination: null,
				error: 'ไม่พบ token การเข้าสู่ระบบ'
			};
		}

		// Get pagination parameters from URL
		const page = url.searchParams.get('page') || '1';
		const limit = url.searchParams.get('limit') || '10';

		const response = await apiClient.makeAuthenticatedRequest<{
			success: boolean;
			data: any[];
			pagination: {
				total: number;
				page: number;
				limit: number;
				totalPages: number;
				hasNext: boolean;
				hasPrev: boolean;
			};
		}>('/sites', accessToken, {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json'
			}
		});

		return {
			websites: response.data || [],
			pagination: response.pagination,
			error: null
		};
	} catch (error: any) {
		console.error('Error loading websites:', error);
		
		return {
			websites: [],
			pagination: null,
			error: error.message || 'เกิดข้อผิดพลาดในการโหลดข้อมูล'
		};
	}
}; 