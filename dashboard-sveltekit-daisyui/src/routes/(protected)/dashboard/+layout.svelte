<script lang="ts">
	import Icon from '@iconify/svelte';
	import { page } from '$app/state';
	import UserMenu from '$lib/layouts/UserMenu.svelte';
	import ThemeSwitcher from '$lib/components/ThemeSwitcher.svelte';
	import LanguageSwitcher from '$lib/components/LanguageSwitcher.svelte';

	let { children } = $props();
</script>

<div class="min-h-screen">
	<!-- Header -->
	<header class="navbar">
		<div class="navbar-start flex-1 flex flex-row gap-1">
			<a
				href="/dashboard"
				class="btn {page.url.pathname === '/dashboard' ? 'btn-primary' : 'btn-ghost'}"
			>
				<Icon icon="solar:home-smile-angle-bold" class="size-6" /> จัดการเว็บไซต์
			</a>

			<a
				href="/dashboard/create"
				class="btn {page.url.pathname === '/dashboard/create' ? 'btn-primary' : 'btn-ghost'}"
			>
				<Icon icon="solar:home-add-angle-bold" class="size-6" /> สร้างเว็บไซต์
			</a>

			<a
				href="/dashboard/join"
				class="btn {page.url.pathname === '/dashboard/join' ? 'btn-primary' : 'btn-ghost'}"
			>
				<Icon icon="solar:users-group-rounded-bold" class="size-6" /> เข้าร่วมเว็บไซต์
			</a>
		</div>

		<!-- Actions -->
		<div class="navbar-end w-fit">
			<ThemeSwitcher />
			<LanguageSwitcher />
			<UserMenu />
		</div>
	</header>

	<!-- Main Content (Full Width - No Sidebar) -->
	<main class="min-h-screen">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
			{@render children?.()}
		</div>
	</main>
</div>
