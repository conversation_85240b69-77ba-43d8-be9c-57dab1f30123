<script lang="ts">
	import { onMount } from 'svelte';
	import { api } from '$lib/api';
	import Swal from 'sweetalert2-neutral';

	let stats = {
		totalUsers: 0,
		totalCustomers: 0,
		activeUsers: 0,
		activeCustomers: 0,
		inactiveUsers: 0,
		inactiveCustomers: 0,
	};
	let loading = true;

	onMount(async () => {
		try {
			const response = await api.getDashboardStats();
			if (response.success) {
				stats = response.data;
			}
		} catch (error) {
			console.error('Error loading stats:', error);
			Swal.fire('ข้อผิดพลาด', 'ไม่สามารถโหลดข้อมูลได้', 'error');
		} finally {
			loading = false;
		}
	});
</script>

<div class="container mx-auto p-6">
	<h1 class="text-3xl font-bold mb-8">Admin Dashboard</h1>

	{#if loading}
		<div class="flex justify-center items-center h-64">
			<span class="loading loading-spinner loading-lg"></span>
		</div>
	{:else}
		<!-- Stats Cards -->
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
			<div class="stat bg-base-100 shadow rounded-lg">
				<div class="stat-figure text-primary">
					<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="inline-block w-8 h-8 stroke-current">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
					</svg>
				</div>
				<div class="stat-title">ผู้ใช้ทั้งหมด</div>
				<div class="stat-value text-primary">{stats.totalUsers}</div>
				<div class="stat-desc">ใช้งาน: {stats.activeUsers} | ระงับ: {stats.inactiveUsers}</div>
			</div>

			<div class="stat bg-base-100 shadow rounded-lg">
				<div class="stat-figure text-secondary">
					<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="inline-block w-8 h-8 stroke-current">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
					</svg>
				</div>
				<div class="stat-title">ลูกค้าทั้งหมด</div>
				<div class="stat-value text-secondary">{stats.totalCustomers}</div>
				<div class="stat-desc">ใช้งาน: {stats.activeCustomers} | ระงับ: {stats.inactiveCustomers}</div>
			</div>

			<div class="stat bg-base-100 shadow rounded-lg">
				<div class="stat-figure text-accent">
					<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="inline-block w-8 h-8 stroke-current">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"></path>
					</svg>
				</div>
				<div class="stat-title">รวมทั้งหมด</div>
				<div class="stat-value text-accent">{stats.totalUsers + stats.totalCustomers}</div>
				<div class="stat-desc">บัญชีในระบบ</div>
			</div>
		</div>

		<!-- Quick Actions -->
		<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
			<div class="card bg-base-100 shadow-xl">
				<div class="card-body">
					<h2 class="card-title">จัดการผู้ใช้</h2>
					<p>จัดการบัญชีผู้ใช้งานระบบ (Admin)</p>
					<div class="card-actions justify-end">
						<a href="/dashboard/admin/users" class="btn btn-primary">จัดการผู้ใช้</a>
					</div>
				</div>
			</div>

			<div class="card bg-base-100 shadow-xl">
				<div class="card-body">
					<h2 class="card-title">จัดการลูกค้า</h2>
					<p>จัดการบัญชีลูกค้าที่สมัครสมาชิกเว็บไซต์</p>
					<div class="card-actions justify-end">
						<a href="/dashboard/admin/customers" class="btn btn-secondary">จัดการลูกค้า</a>
					</div>
				</div>
			</div>
		</div>
	{/if}
</div>