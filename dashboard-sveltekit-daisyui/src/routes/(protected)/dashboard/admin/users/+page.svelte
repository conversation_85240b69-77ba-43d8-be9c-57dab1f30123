<script lang="ts">
	import { onMount } from 'svelte';
	import { api } from '$lib/api';
	import Swal from 'sweetalert2-neutral';

	interface User {
		_id: string;
		email: string;
		firstName: string;
		lastName: string;
		phone?: string;
		isActive: boolean;
		isEmailVerified: boolean;
		lastSignin?: string;
		createdAt: string;
	}

	let users: User[] = [];
	let loading = true;
	let currentPage = 1;
	let totalPages = 1;
	let searchQuery = '';
	let searchTimeout: NodeJS.Timeout;

	async function loadUsers() {
		try {
			loading = true;
			const response = await api.getUsers(currentPage, 10, searchQuery);
			if (response.success) {
				users = response.data.users;
				totalPages = response.data.totalPages;
			}
		} catch (error) {
			console.error('Error loading users:', error);
			Swal.fire('ข้อผิดพลาด', 'ไม่สามารถโหลดข้อมูลผู้ใช้ได้', 'error');
		} finally {
			loading = false;
		}
	}

	function handleSearch() {
		clearTimeout(searchTimeout);
		searchTimeout = setTimeout(() => {
			currentPage = 1;
			loadUsers();
		}, 500);
	}

	async function toggleUserStatus(user: User) {
		try {
			const result = await Swal.fire({
				title: 'ยืนยันการเปลี่ยนสถานะ',
				text: `คุณต้องการ${user.isActive ? 'ระงับ' : 'เปิดใช้งาน'}บัญชี ${user.email} หรือไม่?`,
				icon: 'question',
				showCancelButton: true,
				confirmButtonText: 'ยืนยัน',
				cancelButtonText: 'ยกเลิก'
			});

			if (result.isConfirmed) {
				const response = await api.toggleUserStatus(user._id);
				if (response.success) {
					Swal.fire('สำเร็จ', response.message, 'success');
					loadUsers();
				}
			}
		} catch (error: any) {
			Swal.fire('ข้อผิดพลาด', error.message || 'เกิดข้อผิดพลาด', 'error');
		}
	}

	async function deleteUser(user: User) {
		try {
			const result = await Swal.fire({
				title: 'ยืนยันการลบ',
				text: `คุณต้องการลบบัญชี ${user.email} หรือไม่? การดำเนินการนี้ไม่สามารถย้อนกลับได้`,
				icon: 'warning',
				showCancelButton: true,
				confirmButtonText: 'ลบ',
				cancelButtonText: 'ยกเลิก',
				confirmButtonColor: '#d33'
			});

			if (result.isConfirmed) {
				const response = await api.deleteUser(user._id);
				if (response.success) {
					Swal.fire('สำเร็จ', response.message, 'success');
					loadUsers();
				}
			}
		} catch (error: any) {
			Swal.fire('ข้อผิดพลาด', error.message || 'เกิดข้อผิดพลาด', 'error');
		}
	}

	function formatDate(dateString: string) {
		return new Date(dateString).toLocaleDateString('th-TH', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit'
		});
	}

	onMount(() => {
		loadUsers();
	});
</script>

<div class="container mx-auto p-6">
	<div class="flex justify-between items-center mb-6">
		<h1 class="text-3xl font-bold">จัดการผู้ใช้</h1>
		<a href="/dashboard/admin" class="btn btn-ghost">← กลับ</a>
	</div>

	<!-- Search -->
	<div class="mb-6">
		<div class="form-control w-full max-w-xs">
			<input
				type="text"
				placeholder="ค้นหาผู้ใช้..."
				class="input input-bordered w-full"
				bind:value={searchQuery}
				oninput={handleSearch}
			/>
		</div>
	</div>

	{#if loading}
		<div class="flex justify-center items-center h-64">
			<span class="loading loading-spinner loading-lg"></span>
		</div>
	{:else}
		<!-- Users Table -->
		<div class="overflow-x-auto">
			<table class="table table-zebra w-full">
				<thead>
					<tr>
						<th>อีเมล</th>
						<th>ชื่อ-นามสกุล</th>
						<th>โทรศัพท์</th>
						<th>สถานะ</th>
						<th>ยืนยันอีเมล</th>
						<th>เข้าสู่ระบบล่าสุด</th>
						<th>วันที่สมัคร</th>
						<th>จัดการ</th>
					</tr>
				</thead>
				<tbody>
					{#each users as user}
						<tr>
							<td>{user.email}</td>
							<td>{user.firstName} {user.lastName}</td>
							<td>{user.phone || '-'}</td>
							<td>
								<div class="badge {user.isActive ? 'badge-success' : 'badge-error'}">
									{user.isActive ? 'ใช้งาน' : 'ระงับ'}
								</div>
							</td>
							<td>
								<div class="badge {user.isEmailVerified ? 'badge-success' : 'badge-warning'}">
									{user.isEmailVerified ? 'ยืนยันแล้ว' : 'ยังไม่ยืนยัน'}
								</div>
							</td>
							<td>{user.lastSignin ? formatDate(user.lastSignin) : '-'}</td>
							<td>{formatDate(user.createdAt)}</td>
							<td>
								<div class="flex gap-2">
									<button
										class="btn btn-sm {user.isActive ? 'btn-warning' : 'btn-success'}"
										onclick={() => toggleUserStatus(user)}
									>
										{user.isActive ? 'ระงับ' : 'เปิดใช้'}
									</button>
									<button class="btn btn-sm btn-error" onclick={() => deleteUser(user)}>
										ลบ
									</button>
								</div>
							</td>
						</tr>
					{/each}
				</tbody>
			</table>
		</div>

		<!-- Pagination -->
		{#if totalPages > 1}
			<div class="flex justify-center mt-6">
				<div class="join">
					<button
						class="join-item btn"
						class:btn-disabled={currentPage === 1}
						onclick={() => {
							if (currentPage > 1) {
								currentPage--;
								loadUsers();
							}
						}}
					>
						«
					</button>

					{#each Array(totalPages) as _, i}
						<button
							class="join-item btn"
							class:btn-active={currentPage === i + 1}
							onclick={() => {
								currentPage = i + 1;
								loadUsers();
							}}
						>
							{i + 1}
						</button>
					{/each}

					<button
						class="join-item btn"
						class:btn-disabled={currentPage === totalPages}
						onclick={() => {
							if (currentPage < totalPages) {
								currentPage++;
								loadUsers();
							}
						}}
					>
						»
					</button>
				</div>
			</div>
		{/if}
	{/if}
</div>
