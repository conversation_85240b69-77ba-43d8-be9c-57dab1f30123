<script lang="ts">
	import { onMount } from 'svelte';
	import { api } from '$lib/api';
	import Swal from 'sweetalert2-neutral';

	interface Customer {
		_id: string;
		email: string;
		firstName: string;
		lastName: string;
		phone?: string;
		isActive: boolean;
		isEmailVerified: boolean;
		lastSignin?: string;
		createdAt: string;
		address?: {
			city?: string;
			province?: string;
		};
		gender?: string;
	}

	let customers: Customer[] = [];
	let loading = true;
	let currentPage = 1;
	let totalPages = 1;
	let searchQuery = '';
	let searchTimeout: NodeJS.Timeout;

	async function loadCustomers() {
		try {
			loading = true;
			const response = await api.getCustomers(currentPage, 10, searchQuery);
			if (response.success) {
				customers = response.data.customers;
				totalPages = response.data.totalPages;
			}
		} catch (error) {
			console.error('Error loading customers:', error);
			Swal.fire('ข้อผิดพลาด', 'ไม่สามารถโหลดข้อมูลลูกค้าได้', 'error');
		} finally {
			loading = false;
		}
	}

	function handleSearch() {
		clearTimeout(searchTimeout);
		searchTimeout = setTimeout(() => {
			currentPage = 1;
			loadCustomers();
		}, 500);
	}

	async function toggleCustomerStatus(customer: Customer) {
		try {
			const result = await Swal.fire({
				title: 'ยืนยันการเปลี่ยนสถานะ',
				text: `คุณต้องการ${customer.isActive ? 'ระงับ' : 'เปิดใช้งาน'}บัญชี ${customer.email} หรือไม่?`,
				icon: 'question',
				showCancelButton: true,
				confirmButtonText: 'ยืนยัน',
				cancelButtonText: 'ยกเลิก'
			});

			if (result.isConfirmed) {
				const response = await api.toggleCustomerStatus(customer._id);
				if (response.success) {
					Swal.fire('สำเร็จ', response.message, 'success');
					loadCustomers();
				}
			}
		} catch (error: any) {
			Swal.fire('ข้อผิดพลาด', error.message || 'เกิดข้อผิดพลาด', 'error');
		}
	}

	async function deleteCustomer(customer: Customer) {
		try {
			const result = await Swal.fire({
				title: 'ยืนยันการลบ',
				text: `คุณต้องการลบบัญชี ${customer.email} หรือไม่? การดำเนินการนี้ไม่สามารถย้อนกลับได้`,
				icon: 'warning',
				showCancelButton: true,
				confirmButtonText: 'ลบ',
				cancelButtonText: 'ยกเลิก',
				confirmButtonColor: '#d33'
			});

			if (result.isConfirmed) {
				const response = await api.deleteCustomer(customer._id);
				if (response.success) {
					Swal.fire('สำเร็จ', response.message, 'success');
					loadCustomers();
				}
			}
		} catch (error: any) {
			Swal.fire('ข้อผิดพลาด', error.message || 'เกิดข้อผิดพลาด', 'error');
		}
	}

	function formatDate(dateString: string) {
		return new Date(dateString).toLocaleDateString('th-TH', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit'
		});
	}

	function getGenderText(gender?: string) {
		switch (gender) {
			case 'male':
				return 'ชาย';
			case 'female':
				return 'หญิง';
			case 'other':
				return 'อื่นๆ';
			default:
				return '-';
		}
	}

	onMount(() => {
		loadCustomers();
	});
</script>

<div class="container mx-auto p-6">
	<div class="flex justify-between items-center mb-6">
		<h1 class="text-3xl font-bold">จัดการลูกค้า</h1>
		<a href="/dashboard/admin" class="btn btn-ghost">← กลับ</a>
	</div>

	<!-- Search -->
	<div class="mb-6">
		<div class="form-control w-full max-w-xs">
			<input
				type="text"
				placeholder="ค้นหาลูกค้า..."
				class="input input-bordered w-full"
				bind:value={searchQuery}
				oninput={handleSearch}
			/>
		</div>
	</div>

	{#if loading}
		<div class="flex justify-center items-center h-64">
			<span class="loading loading-spinner loading-lg"></span>
		</div>
	{:else}
		<!-- Customers Table -->
		<div class="overflow-x-auto">
			<table class="table table-zebra w-full">
				<thead>
					<tr>
						<th>อีเมล</th>
						<th>ชื่อ-นามสกุล</th>
						<th>โทรศัพท์</th>
						<th>เพศ</th>
						<th>ที่อยู่</th>
						<th>สถานะ</th>
						<th>ยืนยันอีเมล</th>
						<th>เข้าสู่ระบบล่าสุด</th>
						<th>วันที่สมัคร</th>
						<th>จัดการ</th>
					</tr>
				</thead>
				<tbody>
					{#each customers as customer}
						<tr>
							<td>{customer.email}</td>
							<td>{customer.firstName} {customer.lastName}</td>
							<td>{customer.phone || '-'}</td>
							<td>{getGenderText(customer.gender)}</td>
							<td>
								{#if customer.address?.city || customer.address?.province}
									{customer.address.city || ''}
									{customer.address.province || ''}
								{:else}
									-
								{/if}
							</td>
							<td>
								<div class="badge {customer.isActive ? 'badge-success' : 'badge-error'}">
									{customer.isActive ? 'ใช้งาน' : 'ระงับ'}
								</div>
							</td>
							<td>
								<div class="badge {customer.isEmailVerified ? 'badge-success' : 'badge-warning'}">
									{customer.isEmailVerified ? 'ยืนยันแล้ว' : 'ยังไม่ยืนยัน'}
								</div>
							</td>
							<td>{customer.lastSignin ? formatDate(customer.lastSignin) : '-'}</td>
							<td>{formatDate(customer.createdAt)}</td>
							<td>
								<div class="flex gap-2">
									<button
										class="btn btn-sm {customer.isActive ? 'btn-warning' : 'btn-success'}"
										onclick={() => toggleCustomerStatus(customer)}
									>
										{customer.isActive ? 'ระงับ' : 'เปิดใช้'}
									</button>
									<button class="btn btn-sm btn-error" onclick={() => deleteCustomer(customer)}>
										ลบ
									</button>
								</div>
							</td>
						</tr>
					{/each}
				</tbody>
			</table>
		</div>

		<!-- Pagination -->
		{#if totalPages > 1}
			<div class="flex justify-center mt-6">
				<div class="join">
					<button
						class="join-item btn"
						class:btn-disabled={currentPage === 1}
						onclick={() => {
							if (currentPage > 1) {
								currentPage--;
								loadCustomers();
							}
						}}
					>
						«
					</button>

					{#each Array(totalPages) as _, i}
						<button
							class="join-item btn"
							class:btn-active={currentPage === i + 1}
							onclick={() => {
								currentPage = i + 1;
								loadCustomers();
							}}
						>
							{i + 1}
						</button>
					{/each}

					<button
						class="join-item btn"
						class:btn-disabled={currentPage === totalPages}
						onclick={() => {
							if (currentPage < totalPages) {
								currentPage++;
								loadCustomers();
							}
						}}
					>
						»
					</button>
				</div>
			</div>
		{/if}
	{/if}
</div>
