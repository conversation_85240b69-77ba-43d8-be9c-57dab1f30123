<script lang="ts">
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { authStore } from '$lib/stores/auth';
	import { onMount } from 'svelte';
	import Swal from 'sweetalert2-neutral';
	import Icon from '@iconify/svelte';
	import { Card } from '$lib/components/ui';
	import Label from '$lib/components/ui/Label.svelte';
	let user = $state(null);
	let inviteCode = $state('');
	let loading = $state(false);
	let inviteInfo = $state(null);
	let step = $state('enter-code'); // 'enter-code', 'confirm-join', 'success'

	// Mock team data
	let mockTeams = [
		{
			code: 'TEAM001',
			name: 'Digital Marketing Agency',
			owner: '<EMAIL>',
			members: 5,
			description: 'เราเป็นทีมที่เชี่ยวชาญด้านการตลาดดิจิทัล',
			avatar: null,
			permissions: ['view', 'edit', 'publish']
		},
		{
			code: 'TEAM002',
			name: 'Web Development Studio',
			owner: '<EMAIL>',
			members: 8,
			description: 'สตูดิโอพัฒนาเว็บไซต์และแอปพลิเคชัน',
			avatar: null,
			permissions: ['view', 'edit']
		}
	];

	// Check authentication
	onMount(() => {
		const unsubscribe = authStore.subscribe((state) => {
			if (!state.isAuthenticated) {
				goto('/signin');
			} else {
				user = state.user;
			}
		});

		// Check if invite code is in URL
		const urlInviteCode = page.url.searchParams.get('code');
		if (urlInviteCode) {
			inviteCode = urlInviteCode;
			handleCheckInvite();
		}

		return unsubscribe;
	});

	async function handleCheckInvite() {
		if (!inviteCode.trim()) {
			Swal.fire('ข้อผิดพลาด', 'กรุณากรอกรหัสเชิญ', 'error');
			return;
		}

		try {
			loading = true;

			// Simulate API call to check invite code
			await new Promise((resolve) => setTimeout(resolve, 1000));

			const team = mockTeams.find((t) => t.code === inviteCode.toUpperCase());

			if (team) {
				inviteInfo = team;
				step = 'confirm-join';
			} else {
				Swal.fire('ข้อผิดพลาด', 'รหัสเชิญไม่ถูกต้องหรือหมดอายุ', 'error');
			}
		} catch (error) {
			console.error('Error checking invite:', error);
			Swal.fire('ข้อผิดพลาด', 'เกิดข้อผิดพลาดในการตรวจสอบรหัสเชิญ', 'error');
		} finally {
			loading = false;
		}
	}

	async function handleJoinTeam() {
		try {
			loading = true;

			// Simulate API call to join team
			await new Promise((resolve) => setTimeout(resolve, 1500));

			step = 'success';

			setTimeout(() => {
				goto('/dashboard');
			}, 3000);
		} catch (error) {
			console.error('Error joining team:', error);
			Swal.fire('ข้อผิดพลาด', 'เกิดข้อผิดพลาดในการเข้าร่วมทีม', 'error');
		} finally {
			loading = false;
		}
	}

	function handleBack() {
		step = 'enter-code';
		inviteInfo = null;
	}

	function getPermissionText(permissions: string[]) {
		const permissionMap = {
			view: 'ดูข้อมูล',
			edit: 'แก้ไข',
			publish: 'เผยแพร่',
			admin: 'จัดการ'
		};

		return permissions.map((p) => permissionMap[p] || p).join(', ');
	}
</script>

<svelte:head>
	<title>เข้าร่วมทีม</title>
</svelte:head>

 
		<div class="max-w-2xl mx-auto">
			{#if step === 'enter-code'}
				<!-- Enter Invite Code -->
				<Card size="full" title="เข้าร่วมทีม" subtitle="กรอกรหัสเชิญที่ได้รับจากเจ้าของทีม"> 
				 
						<div class="flex flex-col gap-2 text-center items-center">
							<Label for="inviteCode" text="รหัสเชิญ" />
							<input
								id="inviteCode"
								type="text"
								class="input input-bordered input-lg text-center text-2xl font-mono tracking-wider"
								placeholder="TEAM001"
								bind:value={inviteCode}
								oninput={(e) => (inviteCode = (e.target as HTMLInputElement).value.toUpperCase())}
								disabled={loading}
								maxlength="10"
							/>
							<Label for="inviteCode" text="รหัสเชิญประกอบด้วยตัวอักษรและตัวเลข" />
						</div>

						<div class="card-actions justify-center">
							<button
								class="btn btn-primary btn-lg"
								class:loading
								disabled={loading || !inviteCode.trim()}
								onclick={handleCheckInvite}
							>
								{#if loading}
									<span class="loading loading-spinner loading-sm"></span>
									กำลังตรวจสอบ...
								{:else}
									<Icon icon="solar:magnifer-line-duotone" class="w-5 h-5" />
									ตรวจสอบรหัสเชิญ
								{/if}
							</button>
						</div> 
				</Card>
			{:else if step === 'confirm-join' && inviteInfo}
				<!-- Confirm Join Team -->
				<Card title="ยืนยันการเข้าร่วมทีม" subtitle="ตรวจสอบข้อมูลทีมก่อนเข้าร่วม"> 
					<div class="card-body">
						<div class="text-center mb-6">
							<div
								class="w-20 h-20 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center mx-auto mb-4"
							>
								<span class="text-2xl font-bold text-white">
									{inviteInfo.name.charAt(0)}
								</span>
							</div>
							<h2 class="text-2xl font-bold mb-2">ยืนยันการเข้าร่วมทีม</h2>
							<p class="text-base-content/70">ตรวจสอบข้อมูลทีมก่อนเข้าร่วม</p>
						</div>

						<!-- Team Info -->
						<div class="bg-base-200 rounded-lg p-6 mb-6">
							<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
								<div>
									<h3 class="font-semibold text-lg mb-2">
										{inviteInfo.name}
									</h3>
									<p class="text-base-content/70 mb-4">
										{inviteInfo.description}
									</p>

									<div class="space-y-2">
										<div class="flex items-center text-sm">
											<Icon icon="solar:user-line-duotone" class="w-4 h-4 mr-2" />
											<span>เจ้าของทีม: {inviteInfo.owner}</span>
										</div>
										<div class="flex items-center text-sm">
											<Icon icon="solar:users-group-rounded-line-duotone" class="w-4 h-4 mr-2" />
											<span>สมาชิก: {inviteInfo.members} คน</span>
										</div>
										<div class="flex items-center text-sm">
											<Icon icon="solar:key-line-duotone" class="w-4 h-4 mr-2" />
											<span>รหัสทีม: {inviteInfo.code}</span>
										</div>
									</div>
								</div>

								<div>
									<h4 class="font-semibold mb-3">สิทธิ์ที่จะได้รับ</h4>
									<div class="space-y-2">
										{#each inviteInfo.permissions as permission}
											<div class="flex items-center text-sm">
												<Icon
													icon="solar:check-circle-line-duotone"
													class="w-4 h-4 text-success mr-2"
												/>
												<span>{getPermissionText([permission])}</span>
											</div>
										{/each}
									</div>
								</div>
							</div>
						</div>

						<!-- User Info -->
						<div class="bg-info/10 rounded-lg p-4 mb-6">
							<div class="flex items-start space-x-3">
								<Icon icon="solar:info-circle-line-duotone" class="w-5 h-5 text-info mt-0.5" />
								<div>
									<h4 class="font-semibold text-info mb-1">ข้อมูลของคุณ</h4>
									<p class="text-sm text-base-content/70">
										คุณจะเข้าร่วมทีมในนาม <strong>{user?.email}</strong>
									</p>
								</div>
							</div>
						</div>

						<div class="card-actions justify-between">
							<button class="btn btn-ghost" onclick={handleBack} disabled={loading}>
								<Icon icon="solar:arrow-left-line-duotone" class="w-4 h-4" />
								กลับ
							</button>

							<button
								class="btn btn-success btn-lg"
								class:loading
								disabled={loading}
								onclick={handleJoinTeam}
							>
								{#if loading}
									<span class="loading loading-spinner loading-sm"></span>
									กำลังเข้าร่วม...
								{:else}
									<Icon icon="solar:users-group-two-rounded-line-duotone" class="w-5 h-5" />
									เข้าร่วมทีม
								{/if}
							</button>
						</div>
					</div>
				</Card>
			{:else if step === 'success'}
				<!-- Success -->
				<Card title="เข้าร่วมทีมสำเร็จ!" subtitle="คุณได้เข้าร่วมทีม '{inviteInfo?.name}' เรียบร้อยแล้ว"> 
					<div class="text-center">
						<div class="mb-6">
							<div
								class="w-20 h-20 bg-success/10 rounded-full flex items-center justify-center mx-auto mb-4"
							>
								<Icon icon="solar:check-circle-bold" class="w-10 h-10 text-success" />
							</div>
							<h2 class="text-2xl font-bold text-success mb-2">เข้าร่วมทีมสำเร็จ!</h2>
							<p class="text-base-content/70">
								คุณได้เข้าร่วมทีม "{inviteInfo?.name}" เรียบร้อยแล้ว
							</p>
						</div>

						<div class="bg-success/10 rounded-lg p-6 mb-6">
							<h3 class="font-semibold mb-3">สิ่งที่คุณสามารถทำได้</h3>
							<div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
								<div class="flex items-center">
									<Icon icon="solar:eye-line-duotone" class="w-4 h-4 text-success mr-2" />
									<span>ดูโปรเจ็กต์ของทีม</span>
								</div>
								<div class="flex items-center">
									<Icon
										icon="solar:users-group-rounded-line-duotone"
										class="w-4 h-4 text-success mr-2"
									/>
									<span>ทำงานร่วมกับสมาชิก</span>
								</div>
								<div class="flex items-center">
									<Icon
										icon="solar:chat-round-line-line-duotone"
										class="w-4 h-4 text-success mr-2"
									/>
									<span>แชทกับทีม</span>
								</div>
								<div class="flex items-center">
									<Icon icon="solar:document-text-line-duotone" class="w-4 h-4 text-success mr-2" />
									<span>เข้าถึงไฟล์ร่วม</span>
								</div>
							</div>
						</div>

						<div class="countdown">
							<span style="--value:3;"></span>
						</div>
						<p class="text-sm text-base-content/70 mb-4">กำลังนำคุณไปยัง Dashboard...</p>

						<div class="card-actions justify-center">
							<a href="/dashboard" class="btn btn-primary">
								<Icon icon="solar:home-line-duotone" class="w-4 h-4" />
								ไป Dashboard ทันที
							</a>
						</div>
					</div>
				</Card>
			{/if}

			<!-- Help Section -->
			<div class="card bg-base-100 shadow-xl mt-8">
				<div class="card-body">
					<h3 class="card-title text-lg mb-4">
						<Icon icon="solar:question-circle-line-duotone" class="w-5 h-5" />
						ต้องการความช่วยเหลือ?
					</h3>

					<div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
						<div>
							<h4 class="font-semibold mb-2">รหัสเชิญไม่ทำงาน?</h4>
							<ul class="space-y-1 text-base-content/70">
								<li>• ตรวจสอบการพิมพ์ให้ถูกต้อง</li>
								<li>• รหัสอาจหมดอายุแล้ว</li>
								<li>• ติดต่อเจ้าของทีมเพื่อขอรหัสใหม่</li>
							</ul>
						</div>

						<div>
							<h4 class="font-semibold mb-2">หลังเข้าร่วมทีมแล้ว</h4>
							<ul class="space-y-1 text-base-content/70">
								<li>• คุณจะเห็นโปรเจ็กต์ของทีม</li>
								<li>• สามารถทำงานร่วมกันได้</li>
								<li>• ได้รับการแจ้งเตือนจากทีม</li>
							</ul>
						</div>
					</div>
				</div>
			</div>
		</div>
 