<script lang="ts">
    import { goto } from "$app/navigation";
    import { authStore } from "$lib/stores/auth";
    import { onMount } from "svelte";
    import Swal from "sweetalert2-neutral";
    import Icon from "@iconify/svelte";

    let user: any = $state(null);
    let selectedPackage: any = $state(null);
    let customAmount = $state("");
    let loading = $state(false);

    // Topup packages
    let packages = $state([
        {
            id: "starter",
            name: "Starter",
            amount: 500,
            bonus: 0,
            popular: false,
            features: ["เว็บไซต์ 1 เว็บ", "พื้นที่ 1GB", "แบนด์วิธ 10GB/เดือน"],
        },
        {
            id: "basic",
            name: "Basic",
            amount: 1000,
            bonus: 100,
            popular: false,
            features: [
                "เว็บไซต์ 3 เว็บ",
                "พื้นที่ 5GB",
                "แบนด์วิธ 50GB/เดือน",
                "โบนัส ฿100",
            ],
        },
        {
            id: "pro",
            name: "Pro",
            amount: 2000,
            bonus: 300,
            popular: true,
            features: [
                "เว็บไซต์ 10 เว็บ",
                "พื้นที่ 20GB",
                "แบนด์วิธ 200GB/เดือน",
                "โบนัส ฿300",
            ],
        },
        {
            id: "business",
            name: "Business",
            amount: 5000,
            bonus: 1000,
            popular: false,
            features: [
                "เว็บไซต์ไม่จำกัด",
                "พื้นที่ 100GB",
                "แบนด์วิธไม่จำกัด",
                "โบนัส ฿1,000",
            ],
        },
    ]);

    // Payment methods
    let paymentMethods = $state([
        {
            id: "promptpay",
            name: "PromptPay",
            icon: "solar:wallet-money-line-duotone",
            description: "โอนผ่าน QR Code",
            available: true,
        },
        {
            id: "bank",
            name: "โอนเงินผ่านธนาคาร",
            icon: "solar:card-line-duotone",
            description: "โอนเงินผ่านบัญชีธนาคาร",
            available: true,
        },
        {
            id: "credit",
            name: "บัตรเครดิต/เดบิต",
            icon: "solar:card-2-line-duotone",
            description: "ชำระผ่านบัตรเครดิต",
            available: false,
        },
        {
            id: "truemoney",
            name: "TrueMoney Wallet",
            icon: "solar:smartphone-line-duotone",
            description: "ชำระผ่าน TrueMoney",
            available: false,
        },
    ]);

    let selectedPayment = $state("promptpay");

    // Check authentication
    onMount(() => {
        const unsubscribe = authStore.subscribe((state) => {
            if (!state.isAuthenticated) {
                goto("/signin");
            } else {
                user = state.user;
            }
        });
        return unsubscribe;
    });

    function selectPackage(pkg: any) {
        selectedPackage = pkg;
        customAmount = "";
    }

    function selectCustomAmount() {
        selectedPackage = null;
    }

    function getTotalAmount() {
        if (selectedPackage) {
            return selectedPackage.amount + selectedPackage.bonus;
        }
        return parseInt(customAmount) || 0;
    }

    function getPayAmount() {
        if (selectedPackage) {
            return selectedPackage.amount;
        }
        return parseInt(customAmount) || 0;
    }

    async function handleTopup() {
        const amount = getPayAmount();

        if (amount < 100) {
            Swal.fire("ข้อผิดพลาด", "จำนวนเงินขั้นต่ำ 100 บาท", "error");
            return;
        }

        try {
            loading = true;

            // Simulate payment process
            const result = await Swal.fire({
                title: "ยืนยันการเติมเงิน",
                html: `
					<div class="text-left space-y-2">
						<p><strong>จำนวนที่ชำระ:</strong> ฿${amount.toLocaleString()}</p>
						<p><strong>จำนวนที่ได้รับ:</strong> ฿${getTotalAmount().toLocaleString()}</p>
						<p><strong>วิธีการชำระ:</strong> ${paymentMethods.find((p) => p.id === selectedPayment)?.name}</p>
					</div>
				`,
                icon: "question",
                showCancelButton: true,
                confirmButtonText: "ยืนยัน",
                cancelButtonText: "ยกเลิก",
                confirmButtonColor: "#10b981",
            });

            if (result.isConfirmed) {
                // Simulate payment processing
                await new Promise((resolve) => setTimeout(resolve, 2000));

                await Swal.fire({
                    title: "เติมเงินสำเร็จ!",
                    html: `
						<div class="text-center">
							<div class="text-6xl mb-4">💰</div>
							<p class="text-lg mb-2">เติมเงินจำนวน ฿${getTotalAmount().toLocaleString()} สำเร็จ</p>
							<p class="text-sm text-gray-600">ยอดเงินใหม่: ฿${((user?.moneyPoint || 0) + getTotalAmount()).toLocaleString()}</p>
						</div>
					`,
                    icon: "success",
                    confirmButtonText: "ตกลง",
                });

                // Update user balance (in real app, this would come from API)
                if (user) {
                    user.moneyPoint = (user.moneyPoint || 0) + getTotalAmount();
                    authStore.setUser(user);
                }

                goto("/dashboard");
            }
        } catch (error) {
            console.error("Topup error:", error);
            Swal.fire("ข้อผิดพลาด", "เกิดข้อผิดพลาดในการเติมเงิน", "error");
        } finally {
            loading = false;
        }
    }
</script>

<svelte:head>
    <title>เติมเงิน</title>
</svelte:head>

<div class="min-h-screen bg-base-200">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="flex items-center justify-between mb-8">
            <div>
                <h1 class="text-3xl font-bold text-base-content">เติมเงิน</h1>
                <p class="text-base-content/70 mt-2">
                    เติมเงินเข้าบัญชีเพื่อใช้บริการต่างๆ
                </p>
            </div>
            <a href="/dashboard" class="btn btn-ghost">
                <Icon icon="solar:arrow-left-line-duotone" class="w-5 h-5" />
                กลับ Dashboard
            </a>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Package Selection -->
            <div class="lg:col-span-2">
                <!-- Current Balance -->
                <div
                    class="card bg-gradient-to-r from-success to-success/80 text-success-content shadow-xl mb-6"
                >
                    <div class="card-body">
                        <div class="flex items-center justify-between">
                            <div>
                                <h2 class="text-2xl font-bold">
                                    ยอดเงินปัจจุบัน
                                </h2>
                                <p class="text-success-content/80">
                                    บัญชีของ {user?.email || "User"}
                                </p>
                            </div>
                            <div class="text-right">
                                <div class="text-4xl font-bold">
                                    ฿{(user?.moneyPoint || 0).toLocaleString()}
                                </div>
                                <p class="text-success-content/80">บาท</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Package Cards -->
                <div class="card bg-base-100 shadow-xl mb-6">
                    <div class="card-body">
                        <h2 class="card-title text-2xl mb-6">เลือกแพ็กเกจ</h2>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                            {#each packages as pkg}
                                <div
                                    class="border-2 rounded-lg p-4 cursor-pointer transition-all relative {selectedPackage?.id ===
                                    pkg.id
                                        ? 'border-primary bg-primary/10'
                                        : 'border-base-300 hover:border-primary/50'}"
                                    onclick={() => selectPackage(pkg)}
                                    role="button"
                                    tabindex="0"
                                    onkeydown={(e) =>
                                        e.key === "Enter" && selectPackage(pkg)}
                                >
                                    {#if pkg.popular}
                                        <div
                                            class="absolute -top-3 left-4 bg-primary text-primary-content px-3 py-1 rounded-full text-sm font-semibold"
                                        >
                                            แนะนำ
                                        </div>
                                    {/if}

                                    <div class="text-center mb-4">
                                        <h3 class="text-xl font-bold">
                                            {pkg.name}
                                        </h3>
                                        <div
                                            class="text-3xl font-bold text-primary mt-2"
                                        >
                                            ฿{pkg.amount.toLocaleString()}
                                        </div>
                                        {#if pkg.bonus > 0}
                                            <div
                                                class="text-success font-semibold"
                                            >
                                                + โบนัส ฿{pkg.bonus.toLocaleString()}
                                            </div>
                                            <div
                                                class="text-lg font-semibold text-accent"
                                            >
                                                = ฿{(
                                                    pkg.amount + pkg.bonus
                                                ).toLocaleString()}
                                            </div>
                                        {/if}
                                    </div>

                                    <ul class="space-y-2">
                                        {#each pkg.features as feature}
                                            <li
                                                class="flex items-center text-sm"
                                            >
                                                <Icon
                                                    icon="solar:check-circle-bold"
                                                    class="w-4 h-4 text-success mr-2"
                                                />
                                                {feature}
                                            </li>
                                        {/each}
                                    </ul>

                                    {#if selectedPackage?.id === pkg.id}
                                        <div class="absolute top-4 right-4">
                                            <Icon
                                                icon="solar:check-circle-bold"
                                                class="w-6 h-6 text-primary"
                                            />
                                        </div>
                                    {/if}
                                </div>
                            {/each}
                        </div>

                        <!-- Custom Amount -->
                        <div class="divider">หรือ</div>

                        <div class="form-control">
                            <label for="customAmount" class="label">
                                <span class="label-text font-semibold"
                                    >จำนวนเงินที่ต้องการเติม</span
                                >
                            </label>
                            <div class="join">
                                <span class="btn btn-outline join-item">฿</span>
                                <input
                                    type="number"
                                    class="input input-bordered join-item flex-1"
                                    placeholder="ใส่จำนวนเงิน (ขั้นต่ำ 100 บาท)"
                                    bind:value={customAmount}
                                    oninput={selectCustomAmount}
                                    min="100"
                                    step="10"
                                />
                            </div>
                            {#if customAmount && parseInt(customAmount) < 100}
                                <label for="customAmount" class="label">
                                    <span class="label-text-alt text-error"
                                        >จำนวนเงินขั้นต่ำ 100 บาท</span
                                    >
                                </label>
                            {/if}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment & Summary -->
            <div class="lg:col-span-1">
                <!-- Payment Methods -->
                <div class="card bg-base-100 shadow-xl mb-6">
                    <div class="card-body">
                        <h3 class="card-title mb-4">วิธีการชำระเงิน</h3>

                        <div class="space-y-3">
                            {#each paymentMethods as method}
                                <div
                                    class="border-2 rounded-lg p-3 cursor-pointer transition-all {selectedPayment ===
                                    method.id
                                        ? 'border-primary bg-primary/10'
                                        : method.available
                                          ? 'border-base-300 hover:border-primary/50'
                                          : 'border-base-300 opacity-50 cursor-not-allowed'}"
                                    onclick={() =>
                                        method.available &&
                                        (selectedPayment = method.id)}
                                    role="button"
                                    tabindex="0"
                                    onkeydown={(e) =>
                                        e.key === "Enter" &&
                                        method.available &&
                                        (selectedPayment = method.id)}
                                >
                                    <div class="flex items-center space-x-3">
                                        <Icon
                                            icon={method.icon}
                                            class="w-6 h-6 text-primary"
                                        />
                                        <div class="flex-1">
                                            <h4 class="font-semibold">
                                                {method.name}
                                            </h4>
                                            <p
                                                class="text-sm text-base-content/70"
                                            >
                                                {method.description}
                                            </p>
                                        </div>
                                        {#if !method.available}
                                            <span class="badge badge-ghost"
                                                >เร็วๆ นี้</span
                                            >
                                        {:else if selectedPayment === method.id}
                                            <Icon
                                                icon="solar:check-circle-bold"
                                                class="w-5 h-5 text-primary"
                                            />
                                        {/if}
                                    </div>
                                </div>
                            {/each}
                        </div>
                    </div>
                </div>

                <!-- Summary -->
                <div class="card bg-base-100 shadow-xl">
                    <div class="card-body">
                        <h3 class="card-title mb-4">สรุปการเติมเงิน</h3>

                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span>ยอดเงินปัจจุบัน:</span>
                                <span class="font-semibold"
                                    >฿{(
                                        user?.moneyPoint || 0
                                    ).toLocaleString()}</span
                                >
                            </div>

                            <div class="flex justify-between">
                                <span>จำนวนที่ชำระ:</span>
                                <span class="font-semibold text-primary"
                                    >฿{getPayAmount().toLocaleString()}</span
                                >
                            </div>

                            {#if selectedPackage?.bonus > 0}
                                <div class="flex justify-between">
                                    <span>โบนัส:</span>
                                    <span class="font-semibold text-success"
                                        >+฿{selectedPackage.bonus.toLocaleString()}</span
                                    >
                                </div>
                            {/if}

                            <div class="divider my-2"></div>

                            <div class="flex justify-between text-lg font-bold">
                                <span>ยอดเงินหลังเติม:</span>
                                <span class="text-success"
                                    >฿{(
                                        (user?.moneyPoint || 0) +
                                        getTotalAmount()
                                    ).toLocaleString()}</span
                                >
                            </div>
                        </div>

                        <div class="card-actions justify-stretch mt-6">
                            <button
                                class="btn btn-success btn-lg w-full"
                                class:loading
                                disabled={loading || getPayAmount() < 100}
                                onclick={handleTopup}
                            >
                                {#if loading}
                                    <span
                                        class="loading loading-spinner loading-sm"
                                    ></span>
                                    กำลังดำเนินการ...
                                {:else}
                                    <Icon
                                        icon="solar:wallet-money-line-duotone"
                                        class="w-5 h-5"
                                    />
                                    เติมเงิน ฿{getPayAmount().toLocaleString()}
                                {/if}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
