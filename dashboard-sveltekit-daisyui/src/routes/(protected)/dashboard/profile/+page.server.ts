import { fail, redirect } from '@sveltejs/kit';
import { z } from 'zod';
import type { Actions, PageServerLoad } from './$types';

const API_BASE_URL = 'http://localhost:5000/v1';

// Schema สำหรับการอัปเดตโปรไฟล์
const updateProfileSchema = z.object({
    firstName: z.string().min(1, 'กรุณากรอกชื่อ').max(50, 'ชื่อต้องไม่เกิน 50 ตัวอักษร'),
    lastName: z.string().min(1, 'กรุณากรอกนามสกุล').max(50, 'นามสกุลต้องไม่เกิน 50 ตัวอักษร'),
    email: z.string().email('รูปแบบอีเมลไม่ถูกต้อง'),
    phone: z.string().optional(),
    avatar: z.string().optional(),
});

// Schema สำหรับการเปลี่ยนรหัสผ่าน
const changePasswordSchema = z.object({
    currentPassword: z.string().min(1, 'กรุณากรอกรหัสผ่านปัจจุบัน'),
    newPassword: z.string().min(8, 'รหัสผ่านใหม่ต้องมีอย่างน้อย 8 ตัวอักษร'),
    confirmPassword: z.string().min(1, 'กรุณายืนยันรหัสผ่านใหม่'),
}).refine((data) => data.newPassword === data.confirmPassword, {
    message: "รหัสผ่านใหม่ไม่ตรงกัน",
    path: ["confirmPassword"],
});

export const load: PageServerLoad = async ({ cookies }) => {
    const accessToken = cookies.get('accessToken');

    if (!accessToken) {
        throw redirect(302, '/signin');
    }

    try {
        // ดึงข้อมูลผู้ใช้จาก API
        const response = await fetch(`${API_BASE_URL}/users/me`, {
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json',
            },
        });

        if (!response.ok) {
            // Token ไม่ถูกต้อง ลบ cookies และ redirect
            cookies.delete('accessToken', { path: '/' });
            cookies.delete('refreshToken', { path: '/' });
            throw redirect(302, '/signin');
        }

        const result = await response.json();

        return {
            user: result.data,
            accessToken
        };
    } catch (error) {
        console.error('Error loading user profile:', error);
        cookies.delete('accessToken', { path: '/' });
        cookies.delete('refreshToken', { path: '/' });
        throw redirect(302, '/signin');
    }
};

export const actions: Actions = {
    updateProfile: async ({ request, cookies }) => {
        const accessToken = cookies.get('accessToken');

        if (!accessToken) {
            return fail(401, {
                message: 'ไม่พบ access token',
            });
        }

        try {
            const formData = await request.formData();
            const data = {
                firstName: formData.get('firstName') as string,
                lastName: formData.get('lastName') as string,
                email: formData.get('email') as string,
                phone: formData.get('phone') as string,
                avatar: formData.get('avatar') as string,
            };

            // Validate data
            const validatedData = updateProfileSchema.parse(data);

            // Call backend API
            const response = await fetch(`${API_BASE_URL}/users/me`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${accessToken}`,
                },
                body: JSON.stringify(validatedData),
            });

            const result = await response.json();

            if (!response.ok) {
                return fail(response.status, {
                    message: result.message || 'เกิดข้อผิดพลาดในการอัปเดตโปรไฟล์',
                    data,
                });
            }

            return {
                success: true,
                message: 'อัปเดตโปรไฟล์สำเร็จ',
                data: result.data,
            };
        } catch (error) {
            if (error instanceof z.ZodError) {
                const errors = error.issues.reduce((acc: Record<string, string>, err) => {
                    acc[err.path[0] as string] = err.message;
                    return acc;
                }, {} as Record<string, string>);

                return fail(400, {
                    message: 'ข้อมูลไม่ถูกต้อง',
                    errors,
                    data: Object.fromEntries(await request.formData()),
                });
            }

            return fail(500, {
                message: 'เกิดข้อผิดพลาดในระบบ',
                data: Object.fromEntries(await request.formData()),
            });
        }
    },

    changePassword: async ({ request, cookies }) => {
        const accessToken = cookies.get('accessToken');

        if (!accessToken) {
            return fail(401, {
                message: 'ไม่พบ access token',
            });
        }

        try {
            const formData = await request.formData();
            const data = {
                currentPassword: formData.get('currentPassword') as string,
                newPassword: formData.get('newPassword') as string,
                confirmPassword: formData.get('confirmPassword') as string,
            };

            // Validate data
            const validatedData = changePasswordSchema.parse(data);

            // Call backend API
            const response = await fetch(`${API_BASE_URL}/users/me/change-password`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${accessToken}`,
                },
                body: JSON.stringify({
                    currentPassword: validatedData.currentPassword,
                    newPassword: validatedData.newPassword,
                }),
            });

            const result = await response.json();

            if (!response.ok) {
                return fail(response.status, {
                    message: result.message || 'เกิดข้อผิดพลาดในการเปลี่ยนรหัสผ่าน',
                    data,
                });
            }

            return {
                success: true,
                message: 'เปลี่ยนรหัสผ่านสำเร็จ',
            };
        } catch (error) {
            if (error instanceof z.ZodError) {
                const errors = error.issues.reduce((acc: Record<string, string>, err) => {
                    acc[err.path[0] as string] = err.message;
                    return acc;
                }, {} as Record<string, string>);

                return fail(400, {
                    message: 'ข้อมูลไม่ถูกต้อง',
                    errors,
                    data: Object.fromEntries(await request.formData()),
                });
            }

            return fail(500, {
                message: 'เกิดข้อผิดพลาดในระบบ',
                data: Object.fromEntries(await request.formData()),
            });
        }
    },

    refreshToken: async ({ cookies }) => {
        const refreshToken = cookies.get('refreshToken');

        if (!refreshToken) {
            return fail(401, {
                message: 'ไม่พบ refresh token',
            });
        }

        try {
            // Call backend API to refresh token
            const response = await fetch(`${API_BASE_URL}/users/refresh-token`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ refreshToken }),
            });

            const result = await response.json();

            if (!response.ok) {
                // Refresh token ไม่ถูกต้อง ลบ cookies
                cookies.delete('accessToken', { path: '/' });
                cookies.delete('refreshToken', { path: '/' });
                return fail(401, {
                    message: result.message || 'Token หมดอายุ กรุณาเข้าสู่ระบบใหม่',
                });
            }

            // Set new tokens
            cookies.set('accessToken', result.data.accessToken, {
                path: '/',
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                sameSite: 'strict',
                maxAge: 60 * 60 * 24 * 7, // 7 days
            });

            cookies.set('refreshToken', result.data.refreshToken, {
                path: '/',
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                sameSite: 'strict',
                maxAge: 60 * 60 * 24 * 30, // 30 days
            });

            return {
                success: true,
                message: 'รีเฟรชโทเคนสำเร็จ',
            };
        } catch (error) {
            return fail(500, {
                message: 'เกิดข้อผิดพลาดในการรีเฟรชโทเคน',
            });
        }
    },
}; 