<script lang="ts">
    import { goto } from "$app/navigation";
    import { authStore } from "$lib/stores/auth";
    import { enhance } from "$app/forms";
    import Swal from "sweetalert2-neutral";
    import Icon from "@iconify/svelte";
    import Seo from "$lib/layouts/Seo.svelte";
    import { showSuccess, showError, showWarning, showInfo, showConfirm, showLoading, showCustom, showToast, showSuccessToast, showErrorToast, showWarningToast, showInfoToast } from "$lib/utils/sweetalert";

    let { data, form } = $props();

    let activeTab = $state('profile');
    let loading = $state(false);
    let errors: Record<string, string> = $state({});

    let profileData = $state({
        firstName: data.user?.firstName || "",
        lastName: data.user?.lastName || "",
        email: data.user?.email || "",
        phone: data.user?.phone || "",
        avatar: data.user?.avatar || "",
    });

    let passwordData = $state({
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
    });

    // Handle form submission
    function handleProfileSuccess() {
        loading = false;
        if (form?.success) {
            Swal.fire({
                title: "อัปเดตสำเร็จ!",
                text: form.message || "อัปเดตโปรไฟล์เรียบร้อยแล้ว",
                icon: "success",
                timer: 2000,
                showConfirmButton: false,
            });
            
            // อัปเดต auth store
            if (form?.data) {
                authStore.setUser(form.data);
            }
        }
    }

    function handleProfileError() {
        loading = false;
        if (form && 'error' in form && form.error) {
            Swal.fire("ข้อผิดพลาด", String(form.error), "error");
        }
        if (form && 'errors' in form && form.errors) {
            errors = form.errors as Record<string, string>;
        }
    }

    function handlePasswordSuccess() {
        loading = false;
        if (form?.success) {
            Swal.fire({
                title: "เปลี่ยนรหัสผ่านสำเร็จ!",
                text: form.message || "เปลี่ยนรหัสผ่านเรียบร้อยแล้ว",
                icon: "success",
                timer: 2000,
                showConfirmButton: false,
            });
            
            // ล้างฟอร์ม
            passwordData = {
                currentPassword: "",
                newPassword: "",
                confirmPassword: "",
            };
        }
    }

    function handlePasswordError() {
        loading = false;
        if (form && 'error' in form && form.error) {
            Swal.fire("ข้อผิดพลาด", String(form.error), "error");
        }
        if (form && 'errors' in form && form.errors) {
            errors = form.errors as Record<string, string>;
        }
    }

   
 

    function selectTab(tab: string) {
        activeTab = tab;
        errors = {};
    }

    function getUserInitials(user: any): string {
        if (!user) return "U";

        if (user.firstName && user.lastName) {
            return (
                user.firstName.charAt(0) + user.lastName.charAt(0)
            ).toUpperCase();
        }

        if (user.email) {
            return user.email.charAt(0).toUpperCase();
        }

        return "U";
    }
</script>

<Seo
    title="โปรไฟล์ - Dashboard"
    description="จัดการข้อมูลส่วนตัว การตั้งค่าบัญชี และความปลอดภัย"
    keywords="โปรไฟล์, ตั้งค่า, บัญชี, ความปลอดภัย"
    url="/dashboard/profile"
    noindex={true}
/>

<div class="min-h-screen bg-base-200">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="flex items-center justify-between mb-8">
            <div>
                <h1 class="text-3xl font-bold text-base-content">
                    โปรไฟล์
                </h1>
                <p class="text-base-content/70 mt-2">
                    จัดการข้อมูลส่วนตัวและการตั้งค่าบัญชี
                </p>
            </div>
            <a href="/dashboard" class="btn btn-ghost">
                <Icon icon="solar:arrow-left-line-duotone" class="w-5 h-5" />
                กลับ Dashboard
            </a>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <div class="card bg-base-100 shadow-xl">
                    <div class="card-body">
                        <h3 class="card-title text-xl mb-4">เมนู</h3>
                        <div class="space-y-2">
                            <button
                                class="btn btn-ghost w-full justify-start {activeTab === 'profile' ? 'btn-primary' : ''}"
                                onclick={() => selectTab('profile')}
                            >
                                <Icon icon="solar:user-line-duotone" class="w-5 h-5" />
                                ข้อมูลส่วนตัว
                            </button>
                            <button
                                class="btn btn-ghost w-full justify-start {activeTab === 'password' ? 'btn-primary' : ''}"
                                onclick={() => selectTab('password')}
                            >
                                <Icon icon="solar:lock-line-duotone" class="w-5 h-5" />
                                เปลี่ยนรหัสผ่าน
                            </button>
                            <button
                                class="btn btn-ghost w-full justify-start {activeTab === 'security' ? 'btn-primary' : ''}"
                                onclick={() => selectTab('security')}
                            >
                                <Icon icon="solar:shield-line-duotone" class="w-5 h-5" />
                                ความปลอดภัย
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="lg:col-span-3">
                <!-- Profile Tab -->
                {#if activeTab === 'profile'}
                    <div class="card bg-base-100 shadow-xl">
                        <div class="card-body">
                            <h2 class="card-title text-2xl mb-6">ข้อมูลส่วนตัว</h2>

                            <form 
                                method="POST" 
                                action="?/updateProfile"
                                use:enhance={() => {
                                    return async ({ result }) => {
                                        if (result.type === 'success') {
                                            handleProfileSuccess();
                                        } else if (result.type === 'failure') {
                                            handleProfileError();
                                        }
                                    };
                                }}
                                class="space-y-6"
                            >
                                <!-- Avatar Section -->
                                <div class="flex items-center space-x-6">
                                    <div class="avatar">
                                        <div class="w-24 rounded-full">
                                            {#if profileData.avatar}
                                                <img src={profileData.avatar} alt="Avatar" />
                                            {:else}
                                                <div class="w-24 h-24 bg-primary rounded-full flex items-center justify-center">
                                                    <span class="text-primary-content font-bold text-2xl">
                                                        {getUserInitials(data.user)}
                                                    </span>
                                                </div>
                                            {/if}
                                        </div>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-semibold">{data.user?.firstName} {data.user?.lastName}</h3>
                                        <p class="text-base-content/70">{data.user?.email}</p>
                                        <button type="button" class="btn btn-sm btn-outline mt-2">
                                            <Icon icon="solar:camera-line-duotone" class="w-4 h-4" />
                                            เปลี่ยนรูปโปรไฟล์
                                        </button>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <!-- First Name -->
                                    <div class="form-control">
                                        <label for="firstName" class="label">
                                            <span class="label-text font-semibold">ชื่อ *</span>
                                        </label>
                                        <input
                                            id="firstName"
                                            name="firstName"
                                            type="text"
                                            class="input input-bordered {errors.firstName ? 'input-error' : ''}"
                                            bind:value={profileData.firstName}
                                            disabled={loading}
                                            required
                                        />
                                        {#if errors.firstName}
                                            <label for="firstName" class="label">
                                                <span class="label-text-alt text-error">{errors.firstName}</span>
                                            </label>
                                        {/if}
                                    </div>

                                    <!-- Last Name -->
                                    <div class="form-control">
                                        <label for="lastName" class="label">
                                            <span class="label-text font-semibold">นามสกุล *</span>
                                        </label>
                                        <input
                                            id="lastName"
                                            name="lastName"
                                            type="text"
                                            class="input input-bordered {errors.lastName ? 'input-error' : ''}"
                                            bind:value={profileData.lastName}
                                            disabled={loading}
                                            required
                                        />
                                        {#if errors.lastName}
                                            <label for="lastName" class="label">
                                                <span class="label-text-alt text-error">{errors.lastName}</span>
                                            </label>
                                        {/if}
                                    </div>

                                    <!-- Email -->
                                    <div class="form-control">
                                        <label for="email" class="label">
                                            <span class="label-text font-semibold">อีเมล *</span>
                                        </label>
                                        <input
                                            id="email"
                                            name="email"
                                            type="email"
                                            class="input input-bordered {errors.email ? 'input-error' : ''}"
                                            bind:value={profileData.email}
                                            disabled={loading}
                                            required
                                        />
                                        {#if errors.email}
                                            <label for="email" class="label">
                                                <span class="label-text-alt text-error">{errors.email}</span>
                                            </label>
                                        {/if}
                                    </div>

                                    <!-- Phone -->
                                    <div class="form-control">
                                        <label for="phone" class="label">
                                            <span class="label-text font-semibold">เบอร์โทรศัพท์</span>
                                        </label>
                                        <input
                                            id="phone"
                                            name="phone"
                                            type="tel"
                                            class="input input-bordered {errors.phone ? 'input-error' : ''}"
                                            bind:value={profileData.phone}
                                            disabled={loading}
                                        />
                                        {#if errors.phone}
                                            <label for="phone" class="label">
                                                <span class="label-text-alt text-error">{errors.phone}</span>
                                            </label>
                                        {/if}
                                    </div>
                                </div>

                                <!-- Hidden Avatar Input -->
                                <input type="hidden" name="avatar" bind:value={profileData.avatar} />

                                <!-- Submit Button -->
                                <div class="card-actions justify-end pt-4">
                                    <button
                                        type="submit"
                                        class="btn btn-primary"
                                        class:loading
                                        disabled={loading}
                                    >
                                        {#if loading}
                                            <span class="loading loading-spinner loading-sm"></span>
                                            กำลังอัปเดต...
                                        {:else}
                                            <Icon icon="solar:save-line-duotone" class="w-5 h-5" />
                                            บันทึกการเปลี่ยนแปลง
                                        {/if}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                {/if}

                <!-- Password Tab -->
                {#if activeTab === 'password'}
                    <div class="card bg-base-100 shadow-xl">
                        <div class="card-body">
                            <h2 class="card-title text-2xl mb-6">เปลี่ยนรหัสผ่าน</h2>

                            <form 
                                method="POST" 
                                action="?/changePassword"
                                use:enhance={() => {
                                    return async ({ result }) => {
                                        if (result.type === 'success') {
                                            handlePasswordSuccess();
                                        } else if (result.type === 'failure') {
                                            handlePasswordError();
                                        }
                                    };
                                }}
                                class="space-y-6"
                            >
                                <!-- Current Password -->
                                <div class="form-control">
                                    <label for="currentPassword" class="label">
                                        <span class="label-text font-semibold">รหัสผ่านปัจจุบัน *</span>
                                    </label>
                                    <input
                                        id="currentPassword"
                                        name="currentPassword"
                                        type="password"
                                        class="input input-bordered {errors.currentPassword ? 'input-error' : ''}"
                                        bind:value={passwordData.currentPassword}
                                        disabled={loading}
                                        required
                                    />
                                    {#if errors.currentPassword}
                                        <label for="currentPassword" class="label">
                                            <span class="label-text-alt text-error">{errors.currentPassword}</span>
                                        </label>
                                    {/if}
                                </div>

                                <!-- New Password -->
                                <div class="form-control">
                                    <label for="newPassword" class="label">
                                        <span class="label-text font-semibold">รหัสผ่านใหม่ *</span>
                                    </label>
                                    <input
                                        id="newPassword"
                                        name="newPassword"
                                        type="password"
                                        class="input input-bordered {errors.newPassword ? 'input-error' : ''}"
                                        bind:value={passwordData.newPassword}
                                        disabled={loading}
                                        required
                                    />
                                    {#if errors.newPassword}
                                        <label for="newPassword" class="label">
                                            <span class="label-text-alt text-error">{errors.newPassword}</span>
                                        </label>
                                    {/if}
                                    <label for="newPassword" class="label">
                                        <span class="label-text-alt">รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร</span>
                                    </label>
                                </div>

                                <!-- Confirm Password -->
                                <div class="form-control">
                                    <label for="confirmPassword" class="label">
                                        <span class="label-text font-semibold">ยืนยันรหัสผ่านใหม่ *</span>
                                    </label>
                                    <input
                                        id="confirmPassword"
                                        name="confirmPassword"
                                        type="password"
                                        class="input input-bordered {errors.confirmPassword ? 'input-error' : ''}"
                                        bind:value={passwordData.confirmPassword}
                                        disabled={loading}
                                        required
                                    />
                                    {#if errors.confirmPassword}
                                        <label for="confirmPassword" class="label">
                                            <span class="label-text-alt text-error">{errors.confirmPassword}</span>
                                        </label>
                                    {/if}
                                </div>

                                <!-- Submit Button -->
                                <div class="card-actions justify-end pt-4">
                                    <button
                                        type="submit"
                                        class="btn btn-primary"
                                        class:loading
                                        disabled={loading}
                                    >
                                        {#if loading}
                                            <span class="loading loading-spinner loading-sm"></span>
                                            กำลังเปลี่ยนรหัสผ่าน...
                                        {:else}
                                            <Icon icon="solar:lock-password-line-duotone" class="w-5 h-5" />
                                            เปลี่ยนรหัสผ่าน
                                        {/if}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                {/if}

                <!-- Security Tab -->
                {#if activeTab === 'security'}
                    <div class="card bg-base-100 shadow-xl">
                        <div class="card-body">
                            <h2 class="card-title text-2xl mb-6">ความปลอดภัย</h2>

                            <div class="space-y-6">
                                <!-- Token Status -->
                                <div class="bg-base-200 rounded-lg p-6">
                                    <h3 class="text-lg font-semibold mb-4">สถานะโทเคน</h3>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div class="flex items-center justify-between">
                                            <span class="text-base-content/70">Access Token:</span>
                                            <span class="badge badge-success">ใช้งานได้</span>
                                        </div>
                                        <div class="flex items-center justify-between">
                                            <span class="text-base-content/70">Refresh Token:</span>
                                            <span class="badge badge-success">ใช้งานได้</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Refresh Token -->
                                <div class="bg-base-200 rounded-lg p-6">
                                    <h3 class="text-lg font-semibold mb-4">รีเฟรชโทเคน</h3>
                                    <p class="text-base-content/70 mb-4">
                                        หากโทเคนหมดอายุหรือมีปัญหา สามารถรีเฟรชโทเคนใหม่ได้
                                    </p>
                                    <form 
                                        method="POST" 
                                        action="?/refreshToken"
                                        use:enhance={() => {
                                            return async ({ result }) => {
                                                console.log(result);
                                                if (result.type === 'success') {
                                                    showSuccess("สำเร็จ!", String(result.data?.message) || "รีเฟชโทเคนเรียบร้อยแล้ว");
                                                } else if (result.type === 'failure') {
                                                    showError("ล้มเหลว!", String(result.data?.message) || "เกิดข้อผิดพลาดในการรีเฟรชโทเคน");
                                                }
                                            };
                                        }}
                                    >
                                        <button
                                            type="submit"
                                            class="btn btn-outline"
                                            class:loading
                                            disabled={loading}
                                        >
                                            {#if loading}
                                                <span class="loading loading-spinner loading-sm"></span>
                                                กำลังรีเฟรช...
                                            {:else}
                                                <Icon icon="solar:refresh-circle-line-duotone" class="w-5 h-5" />
                                                รีเฟรชโทเคน
                                            {/if}
                                        </button>
                                    </form>
                                </div>

                                <!-- Account Actions -->
                                <div class="bg-base-200 rounded-lg p-6">
                                    <h3 class="text-lg font-semibold mb-4">การดำเนินการบัญชี</h3>
                                    <div class="space-y-3">
                                        <button class="btn btn-outline btn-error w-full justify-start">
                                            <Icon icon="solar:trash-bin-trash-line-duotone" class="w-5 h-5" />
                                            ลบบัญชี
                                        </button>
                                        <button class="btn btn-outline btn-warning w-full justify-start">
                                            <Icon icon="solar:lock-unlock-line-duotone" class="w-5 h-5" />
                                            ระงับบัญชี
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                {/if}
            </div>
        </div>
    </div>
</div> 