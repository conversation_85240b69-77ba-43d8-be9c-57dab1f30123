<script lang="ts">
	import { goto } from '$app/navigation';
	import { authStore } from '$lib/stores/auth';
	import { onMount } from 'svelte';
	import Swal from 'sweetalert2-neutral';
	import Icon from '@iconify/svelte';
	import Seo from '$lib/layouts/Seo.svelte';
	import { enhance } from '$app/forms';
	import { Card, Input, Radio, Select, Badge, Button } from '$lib/components/ui';

	let { data, form }: any = $props();

	console.log('Create page data:', data);
	console.log('Packages:', data.packages);

	const packages = data.packages;

	let formData = $state({
		name: 'MyCoffee',
		typeDomain: 'subdomain' as 'subdomain' | 'custom',
		mainDomain: 'is1.shop',
		subDomain: 'mycoffee',
		customDomain: '',
		description: '',
		package: 'monthly'
	});

	let domainAvailable = $state(false);
	let domainChecked = $state(false);
	let isCheckingDomain = $state(false);
	let isCheckingDiscount = $state(false);
	let discountValid = $state(false);
	let discountInfo = $state<any | null>(null);
	let packageType = $state('monthly');
	let discountCode = $state('');
	let isLoading = $state(false);
	
	// เก็บค่าโดเมนที่ตรวจสอบแล้ว
	let lastCheckedDomain = $state('');
	
	// ตรวจสอบว่าโดเมนปัจจุบันตรงกับที่ตรวจสอบแล้วหรือไม่
	const isDomainChanged = $derived(() => {
		const currentDomain = formData.typeDomain === 'subdomain' 
			? `${formData.subDomain}.${formData.mainDomain}`
			: formData.customDomain;
		return currentDomain !== lastCheckedDomain;
	});

	// รีเซ็ตสถานะการตรวจสอบเมื่อโดเมนเปลี่ยนแปลง
	$effect(() => {
		if (isDomainChanged() && domainChecked) {
			domainChecked = false;
			domainAvailable = false;
		}
	});

	// โดเมนหลักที่รองรับ
	const availableDomains = ['is1.shop'];

	// ฟังก์ชันหาแพ็คเกจที่เลือก
	function getSelectedPackage() {
		if (!Array.isArray(packages) || packages.length === 0) {
			console.log('No packages available');
			return null;
		}
		const selectedPackage = packages.find((pkg: any) => pkg.id === packageType);
		console.log('Selected package:', selectedPackage, 'packageType:', packageType);
		return selectedPackage;
	}

	const currentPrice = $derived(getSelectedPackage()?.moneyPoint || 0);

	// Debug log สำหรับราคา
	$effect(() => {
		console.log('Current price changed:', currentPrice);
	});

	// คำนวณราคาหลังส่วนลด
	const finalPrice = $derived(() => {
		if (discountValid && discountInfo) {
			if (discountInfo.type === 'percentage') {
				return Math.max(0, currentPrice - (currentPrice * discountInfo.value) / 100);
			} else {
				return Math.max(0, currentPrice - discountInfo.value);
			}
		}
		return currentPrice;
	});

	const discountAmount = $derived(() => currentPrice - finalPrice());

	// ฟังก์ชันจัดการผลลัพธ์จาก form actions
	function handleFormResult(result: any, action: string) {
		if (result.type === 'success') {
			if (action === 'checkDomain') {
				domainChecked = true;
				domainAvailable = result.data?.available || false;
				// บันทึกโดเมนที่ตรวจสอบแล้ว
				lastCheckedDomain = formData.typeDomain === 'subdomain' 
					? `${formData.subDomain}.${formData.mainDomain}`
					: formData.customDomain;
				if (result.data?.available) {
					Swal.fire('สำเร็จ', 'โดเมนนี้สามารถใช้งานได้', 'success');
				} else {
					Swal.fire('ไม่สามารถใช้งานได้', 'โดเมนนี้ถูกใช้งานแล้ว', 'error');
				}
			} else if (action === 'checkDiscount') {
				if (result.data?.valid) {
					discountValid = true;
					discountInfo = result.data?.discount;
					Swal.fire('สำเร็จ', 'โค้ดส่วนลดถูกต้อง', 'success');
				} else {
					discountValid = false;
					discountInfo = null;
					Swal.fire('ไม่ถูกต้อง', result.data?.message || 'โค้ดส่วนลดไม่ถูกต้อง', 'error');
				}
			} else if (action === 'createSite') {
				Swal.fire({
					title: 'สร้างเว็บไซต์สำเร็จ!',
					text: `เว็บไซต์ ${formData.name} ถูกสร้างเรียบร้อยแล้ว`,
					icon: 'success',
					confirmButtonText: 'ไปจัดการเว็บไซต์'
				}).then(() => {
					if (result.data?._id) {
						goto(`/dashboard/${result.data._id}`);
					} else {
						goto('/dashboard');
					}
				});
			}
		} else {
			Swal.fire('ข้อผิดพลาด', result.data?.error || 'เกิดข้อผิดพลาด', 'error');
		}
	}

	// ฟังก์ชันยกเลิกส่วนลด
	function cancelDiscount() {
		discountValid = false;
		discountInfo = null;
		discountCode = '';
	}

	// ฟังก์ชันแสดง loading
	function showLoading(message: string) {
		Swal.fire({
			title: message,
			allowOutsideClick: false,
			didOpen: () => {
				Swal.showLoading();
			}
		});
	}

	// onMount(() => {
	// 	// ตรวจสอบ authentication
	// 	const unsubscribe = authStore.subscribe((state) => {
	// 		if (!state.isAuthenticated) {
	// 			goto("/signin");
	// 		}
	// 	});

	// 	return unsubscribe;
	// });
</script>

<Seo
	title="สร้างเว็บไซต์ใหม่ - Dashboard"
	description="สร้างเว็บไซต์ร้านค้าออนไลน์ของคุณ เลือกโดเมน แพ็คเกจ และเริ่มต้นขายของออนไลน์ได้ทันที"
	keywords="สร้างเว็บไซต์, ร้านค้าออนไลน์, โดเมน, แพ็คเกจ, อีคอมเมิร์ส"
	url="/dashboard/create"
	noindex={true}
/>

<div class="space-y-4">
	<!-- Header -->
	<div class="text-center mb-8">
		<div class="flex items-center justify-center gap-3 mb-4">
			<Icon icon="solar:shop-bold" class="size-10 text-primary" />
			<h1 class="text-3xl font-bold text-primary">สร้างเว็บไซต์ใหม่</h1>
		</div>
		<p class="text-lg text-base-content/70 max-w-2xl mx-auto">
			สร้างเว็บไซต์ร้านค้าออนไลน์ของคุณในไม่กี่ขั้นตอน เลือกโดเมน แพ็คเกจ
			และเริ่มต้นขายของออนไลน์ได้ทันที
		</p>
	</div>

	<div class="w-full flex flex-col lg:flex-row gap-4">
		<Card title="ข้อมูลเว็บไซต์" titleIcon="solar:document-add-bold" size="full" class="space-y-4">
			<!-- ชื่อเว็บไซต์ -->
			<Input
				id="siteName"
				variant="bordered"
				label="ชื่อเว็บไซต์"
				icon="solar:pen-bold"
				bind:value={formData.name}
				placeholder="ชื่อร้านค้าของคุณ เช่น MyCoffee"
				required={true}
			/>

			<div class="divider"></div>

			<!-- ประเภทโดเมน -->
			<Radio
				label="ประเภทโดเมน"
				required={true}
				options={[
					{
						value: 'subdomain',
						label: 'ซับโดเมนฟรี',
						description: 'yourname.is1.shop',
						icon: 'solar:shield-network-broken'
					},
					{
						value: 'custom',
						label: 'โดเมนส่วนตัว',
						description: 'yourdomain.com',
						icon: 'solar:shield-network-line-duotone'
					}
				]}
				value={formData.typeDomain}
				onChange={(value) => (formData.typeDomain = value as 'subdomain' | 'custom')}
				name="domain-type"
			/>

			<div class="space-y-4">
				<!-- โดเมน -->
				{#if formData.typeDomain === 'subdomain'}
					<div class="space-y-4">
						<div class="flex flex-col sm:flex-row gap-3">
							<div class="flex-1">
								<Input
									label="ซับโดเมน"
									icon="solar:shield-network-broken"
									bind:value={formData.subDomain}
									placeholder="yourname"
									required
								/>
							</div>

							<div class="flex-1">
								<div class="form-control w-full">
									<label for="mainDomain" class="label">
										<span class="label-text font-medium">โดเมนหลัก</span>
									</label>
									<Select
										options={availableDomains.map((domain) => ({
											value: domain,
											label: domain
										}))}
										value={formData.mainDomain}
										placeholder="เลือกโดเมนหลัก"
										onchange={(value) => {
											if (typeof value === 'string') {
												formData.mainDomain = value;
											}
										}}
										class="w-full"
										id="mainDomain"
									/>
								</div>
							</div>
						</div>
					</div>
				{:else}
					<div class="space-y-4">
						<Input
							label="โดเมน"
							icon="solar:shield-network-line-duotone"
							bind:value={formData.customDomain}
							placeholder="yourdomain.com"
							required
						/>

						<div class="alert alert-warning alert-soft">
							<Icon icon="solar:danger-triangle-bold" class="w-5 h-5" />
							<div>
								<div class="font-semibold">หมายเหตุ</div>
								<div class="text-sm">คุณต้องตั้งค่า DNS ของโดเมนให้ชี้มาที่เซิร์ฟเวอร์ของเรา</div>
								<div class="overflow-x-auto mt-5">
									<table class="table table-sm">
										<thead>
											<tr class="bg-base-200">
												<th>Type</th>
												<th>Name</th>
												<th>Content</th>
											</tr>
										</thead>
										<tbody>
											<tr>
												<td>A</td>
												<td>{formData.customDomain ? formData.customDomain : '@'}</td>
												<td>76.76.21.21</td>
											</tr>
											<tr>
												<td>CNAME</td>
												<td>*</td>
												<td>cname.vercel-dns.com</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				{/if}

				{#if formData.typeDomain === 'subdomain' || formData.customDomain}
					<div class="alert alert-info alert-soft">
						<Icon icon="solar:info-circle-bold" class="size-8" />
						<div>
							<div class="font-semibold">โดเมนที่จะใช้งาน</div>
							<div class="text-sm">
								{formData.typeDomain === 'subdomain'
									? `${formData.subDomain}.${formData.mainDomain}`
									: formData.customDomain}
							</div>
						</div>
					</div>
				{/if}

				<!-- ✅ ตรวจสอบโดเมน - Hybrid Approach -->
				<form
					method="POST"
					action="?/checkDomain"
					use:enhance={() => {
						isCheckingDomain = true;
						return async ({ result }) => {
							isCheckingDomain = false;
							handleFormResult(result, 'checkDomain');
						};
					}}
				>
					<input type="hidden" name="typeDomain" value={formData.typeDomain} />
					<input type="hidden" name="subDomain" value={formData.subDomain} />
					<input type="hidden" name="mainDomain" value={formData.mainDomain} />
					<input type="hidden" name="customDomain" value={formData.customDomain} />

					<div class="flex gap-2">
						<Button
							icon="solar:search-bold"
							label="ตรวจสอบโดเมน"
							type="submit"
							outline={true}
							disabled={isCheckingDomain}
							class="flex-1"
							loading={isCheckingDomain}
						/>

						{#if domainChecked}
							<Badge
								label={domainAvailable ? 'ว่าง' : 'ไม่ว่าง'}
								variant={domainAvailable ? 'solid' : 'solid'}
								color={domainAvailable ? 'success' : 'error'}
							/>
						{/if}
					</div>
				</form>
			</div>

			<div class="divider"></div>

			<!-- แพ็คเกจ -->
			{#if Array.isArray(packages) && packages.length > 0}
				<Radio
					label="แพ็คเกจ"
					required={true}
					options={packages.map((pkg: any) => ({
						value: pkg.id,
						label: pkg.name,
						description: `${pkg.days >= 36500 ? `฿${pkg.moneyPoint.toLocaleString()} / ไม่จำกัด` : `฿${pkg.moneyPoint.toLocaleString()} / ${pkg.days} วัน`}`,
						icon: 'solar:calendar-bold'
					}))}
					cols={3}
					value={packageType}
					onChange={(value) => (packageType = value as string)}
					name="package-type"
				/>
			{:else}
				<div class="alert alert-warning">
					<Icon icon="solar:danger-triangle-bold" class="w-5 h-5" />
					<div>
						<div class="font-semibold">ไม่สามารถโหลดแพ็คเกจได้</div>
						<div class="text-sm">กรุณาลองใหม่อีกครั้ง</div>
					</div>
				</div>
			{/if}
		</Card>

		<!-- Summary -->
		<Card title="ข้อมูลแพ็คเกจ" titleIcon="solar:info-circle-bold" size="md" class="space-y-5">
			<div class="space-y-5">
				<div class="space-y-3">
					<div class="flex justify-between">
						<span class="text-base-content/70">แพ็คเกจ:</span>
						<span class="font-medium">{getSelectedPackage()?.name || 'ไม่ระบุ'}</span>
					</div>
					{#if !Array.isArray(packages) || packages.length === 0}
						<div class="alert alert-error alert-sm">
							<Icon icon="solar:danger-triangle-bold" class="w-4 h-4" />
							<div class="text-sm">ไม่สามารถโหลดแพ็คเกจได้</div>
						</div>
					{/if}
					<div class="flex justify-between">
						<span class="text-base-content/70">ประเภทโดเมน:</span>
						<span class="font-medium"
							>{formData.typeDomain === 'subdomain' ? 'ซับโดเมน' : 'โดเมนเอง'}</span
						>
					</div>
					<div class="flex justify-between">
						<span class="text-base-content/70">โดเมนที่เลือก:</span>
						<span class="font-medium text-info">
							{#if domainChecked && domainAvailable}
								{formData.typeDomain === 'subdomain'
									? `${formData.subDomain}.${formData.mainDomain}`
									: formData.customDomain}
							{:else if domainChecked && !domainAvailable}
								<span class="text-error">โดเมนไม่ว่าง</span>
							{:else}
								ยังไม่ได้ตรวจสอบ
							{/if}
						</span>
					</div>
					{#if domainChecked}
						<div class="flex justify-between">
							<span class="text-base-content/70">สถานะโดเมน:</span>
							<Badge
								label={domainAvailable ? 'ว่าง - ใช้งานได้' : 'ไม่ว่าง - ถูกใช้แล้ว'}
								variant="solid"
								color={domainAvailable ? 'success' : 'error'}
								size="sm"
							/>
						</div>
					{/if}
					<div class="flex justify-between">
						<span class="text-base-content/70">ราคา:</span>
						<span class="font-medium text-primary">฿{currentPrice.toLocaleString()}</span>
					</div>

					<div class="flex gap-1 items-end w-full">
						<form
							method="POST"
							action="?/checkDiscount"
							use:enhance={() => {
								isCheckingDiscount = true;
								return async ({ result }) => {
									isCheckingDiscount = false;
									handleFormResult(result, 'checkDiscount');
								};
							}}
							class="flex gap-1 items-end w-full"
						>
							<!-- Hidden field สำหรับส่ง orderAmount -->
							<input type="hidden" name="orderAmount" value={finalPrice()} />

							<Input
								clear={true}
								size="sm"
								label="โค้ดส่วนลด"
								icon="solar:bag-heart-line-duotone"
								bind:value={discountCode}
								placeholder="รหัสส่วนลด เช่น WELCOME, SAVE10"
								disabled={isCheckingDiscount || discountValid}
								name="discountCode"
							/>
							{#if !discountValid}
								<Button
									label="ตรวจสอบ"
									icon="solar:discount-bold"
									color="primary"
									size="sm"
									outline={true}
									loading={isCheckingDiscount}
									disabled={isCheckingDiscount || !discountCode.trim()}
									type="submit"
								/>
							{:else}
								<Button
									label="ยกเลิก"
									icon="solar:close-circle-bold"
									color="error"
									size="sm"
									outline={true}
									onclick={cancelDiscount}
									type="button"
								/>
							{/if}
						</form>
					</div>

					{#if discountValid && discountInfo}
						<div class="alert alert-success">
							<Icon icon="solar:check-circle-bold" class="w-5 h-5" />
							<div class="flex-1">
								<div class="font-semibold">
									โค้ดส่วนลด: {discountInfo?.code}
								</div>
								<div class="text-sm">
									ส่วนลด {discountInfo?.value}{discountInfo?.type === 'percentage' ? '%' : '฿'}
								</div>

								<!-- แสดงข้อมูลเพิ่มเติมถ้ามี -->
								{#if discountInfo?.name}
									<div class="text-xs text-success-content/70 mt-1">
										{discountInfo.name}
									</div>
								{/if}

								{#if discountInfo?.description}
									<div class="text-xs text-success-content/70">
										{discountInfo.description}
									</div>
								{/if}

								{#if discountInfo?.conditions?.minOrderAmount && discountInfo.conditions.minOrderAmount > 0}
									<div class="text-xs text-success-content/70">
										ขั้นต่ำ ฿{discountInfo.conditions.minOrderAmount.toLocaleString()}
									</div>
								{/if}

								{#if discountInfo?.maxDiscountAmount && discountInfo.maxDiscountAmount > 0}
									<div class="text-xs text-success-content/70">
										ส่วนลดสูงสุด ฿{discountInfo.maxDiscountAmount.toLocaleString()}
									</div>
								{/if}

								{#if discountInfo?.conditions?.firstTimeOnly}
									<div class="text-xs text-success-content/70">สำหรับลูกค้าใหม่เท่านั้น</div>
								{/if}

								{#if discountInfo?.endDate}
									<div class="text-xs text-success-content/70">
										ใช้ได้ถึง {new Date(discountInfo.endDate).toLocaleDateString('th-TH')}
									</div>
								{/if}
							</div>
						</div>
					{/if}

					{#if discountValid && discountInfo}
						<div class="flex justify-between">
							<span class="text-base-content/70">ส่วนลด:</span>
							<span class="font-medium text-success">-฿{discountAmount().toLocaleString()}</span>
						</div>
						<div class="flex justify-between">
							<span class="text-base-content/70">ราคาสุทธิ:</span>
							<span class="font-medium text-primary">฿{finalPrice().toLocaleString()}</span>
						</div>
					{/if}
				</div>

				<!-- ✅ สร้างเว็บไซต์ - Hybrid Approach -->
				<form
					method="POST"
					action="?/createSite"
					use:enhance={() => {
						isLoading = true;
						showLoading('กำลังสร้างเว็บไซต์...');

						return async ({ result }) => {
							console.log('createSite result', result);
							handleFormResult(result, 'createSite');
							setTimeout(() => {
								isLoading = false;
							}, 1000);
						};
					}}
				>
					<!-- Hidden fields for form data -->
					<input type="hidden" name="siteName" value={formData.name} />
					<input type="hidden" name="typeDomain" value={formData.typeDomain} />
					<input type="hidden" name="subDomain" value={formData.subDomain} />
					<input type="hidden" name="mainDomain" value={formData.mainDomain} />
					<input type="hidden" name="customDomain" value={formData.customDomain} />
					<input type="hidden" name="packageType" value={packageType} />
					{#if discountValid && discountCode}
						<input type="hidden" name="discountCode" value={discountCode} />
					{/if}

					<div class="flex gap-3">
						<Button
							type="submit"
							color="primary"
							size="xl"
							loading={isLoading}
							disabled={isLoading || !domainChecked || !domainAvailable || isDomainChanged()}
							class="flex-1"
							icon="solar:shop-bold"
							label="สร้างเว็บไซต์ (฿{finalPrice().toLocaleString()})"
						/>
					</div>
				</form>
			</div>
		</Card>
	</div>
</div>
