import { fail } from '@sveltejs/kit';
import { z } from 'zod';
import type { Actions, PageServerLoad } from './$types';
import { ApiClient } from '$lib/api/client';

const apiClient = new ApiClient();

// Schema สำหรับการสร้างเว็บไซต์
const createSiteSchema = z.object({
    name: z.string().min(1, 'กรุณากรอกชื่อเว็บไซต์').max(100, 'ชื่อเว็บไซต์ต้องไม่เกิน 100 ตัวอักษร'),
    domain: z.string().min(1, 'กรุณากรอกโดเมน').regex(/^[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]*\.[a-zA-Z]{2,}$/, 'รูปแบบโดเมนไม่ถูกต้อง (เช่น example.com)'),
    subdomain: z.string().min(1, 'กรุณากรอก subdomain').regex(/^[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]*$/, 'Subdomain ต้องเป็นตัวอักษรและตัวเลขเท่านั้น'),
    title: z.string().min(1, 'กรุณากรอกหัวข้อเว็บไซต์').max(200, 'หัวข้อเว็บไซต์ต้องไม่เกิน 200 ตัวอักษร'),
    description: z.string().max(500, 'คำอธิบายต้องไม่เกิน 500 ตัวอักษร').optional(),
    theme: z.string().min(1, 'กรุณาเลือกธีม'),
});

export const load: PageServerLoad = async ({ cookies }) => {
    try {
        const accessToken = cookies.get('accessToken');

        if (!accessToken) {
            return fail(401, {
                error: 'ไม่พบ access token',
            });
        }

        // โหลดข้อมูลแพ็คเกจ
        const packagesResponse = await apiClient.makeAuthenticatedRequest<{
            success: boolean;
            data: any[];
            pagination: {
                total: number;
                page: number;
                limit: number;
                totalPages: number;
                hasNext: boolean;
                hasPrev: boolean;
            };
        }>('/sites/packages', accessToken, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        return {
            packages: packagesResponse.data || []
        };
    } catch (error) {
        console.error('Error loading packages:', error);
        return fail(500, {
            message: 'เกิดข้อผิดพลาดในการดึงข้อมูลแพ็คเกจ',
            type: 'create',
        });
    }
};

export const actions: Actions = {
    checkDomain: async ({ request, cookies }) => {
        const accessToken = cookies.get('accessToken');

        if (!accessToken) {
            return fail(401, {
                error: 'ไม่พบ access token',
            });
        }

        try {
            const formData = await request.formData();
            const typeDomain = formData.get('typeDomain') as string;
            const subDomain = formData.get('subDomain') as string;
            const mainDomain = formData.get('mainDomain') as string;
            const customDomain = formData.get('customDomain') as string;

            let domainToCheck = '';
            if (typeDomain === 'subdomain') {
                domainToCheck = `${subDomain}.${mainDomain}`;
            } else {
                domainToCheck = customDomain;
            }

            // เรียก API ตรวจสอบโดเมน
            const result = await apiClient.makeAuthenticatedRequest<{
                success: boolean;
                available: boolean;
                message: string;
            }>('/sites/check-domain', accessToken, {
                method: 'POST',
                body: { domain: domainToCheck },
            });

            return {
                success: true,
                available: result.available,
                message: result.message,
            };
        } catch (error) {
            console.error('Error checking domain:', error);
            return fail(500, {
                error: 'เกิดข้อผิดพลาดในการตรวจสอบโดเมน',
            });
        }
    },

    checkDiscount: async ({ request, cookies }) => {
        const accessToken = cookies.get('accessToken');

        if (!accessToken) {
            return fail(401, {
                error: 'ไม่พบ access token',
            });
        }

        try {
            const formData = await request.formData();
            const discountCode = formData.get('discountCode') as string;
            const orderAmount = parseFloat(formData.get('orderAmount') as string);

            // เรียก API ตรวจสอบส่วนลด
            const result = await apiClient.makeAuthenticatedRequest<{
                success: boolean;
                valid: boolean;
                discount: any;
                message: string;
            }>('/discounts/check', accessToken, {
                method: 'POST',
                body: { 
                    code: discountCode,
                    orderAmount: orderAmount
                },
            });

            return {
                success: true,
                valid: result.valid,
                discount: result.discount,
                message: result.message,
            };
        } catch (error) {
            console.error('Error checking discount:', error);
            return fail(500, {
                error: 'เกิดข้อผิดพลาดในการตรวจสอบส่วนลด',
            });
        }
    },

    createSite: async ({ request, cookies }) => {
        const accessToken = cookies.get('accessToken');

        if (!accessToken) {
            return fail(401, {
                error: 'ไม่พบ access token',
            });
        }

        try {
            const formData = await request.formData();
            const siteName = formData.get('siteName') as string;
            const typeDomain = formData.get('typeDomain') as string;
            const subDomain = formData.get('subDomain') as string;
            const mainDomain = formData.get('mainDomain') as string;
            const customDomain = formData.get('customDomain') as string;
            const packageType = formData.get('packageType') as string;
            const discountCode = formData.get('discountCode') as string;

            // สร้างข้อมูลสำหรับ API
            const siteData = {
                name: siteName,
                typeDomain: typeDomain,
                subDomain: typeDomain === 'subdomain' ? subDomain : undefined,
                mainDomain: typeDomain === 'subdomain' ? mainDomain : undefined,
                customDomain: typeDomain === 'custom' ? customDomain : undefined,
                packageType: packageType,
                discountCode: discountCode || undefined,
            };

            // เรียก API สร้างเว็บไซต์
            const result = await apiClient.makeAuthenticatedRequest<{
                success: boolean;
                message: string;
                data: any;
            }>('/sites', accessToken, {
                method: 'POST',
                body: siteData,
            });

            return {
                success: true,
                message: 'สร้างเว็บไซต์สำเร็จ',
                data: result.data,
            };
        } catch (error) {
            console.error('Error creating site:', error);
            return fail(500, {
                error: 'เกิดข้อผิดพลาดในการสร้างเว็บไซต์',
            });
        }
    },
}; 