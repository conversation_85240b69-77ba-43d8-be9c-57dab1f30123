<script lang="ts">
	import { goto } from "$app/navigation";
	import { enhance } from "$app/forms";
	import { authStore } from "$lib";
	import { onMount } from "svelte";

	const data = $props();

	// State สำหรับข้อมูลเว็บไซต์
	let websites = $state(data.websites || []);
	let pagination = $state(data.pagination);
	let error = $state(data.error);
	let loading = $state(false);
	let currentPage = $state(1);
	let currentLimit = $state(10);

	// Svelte action สำหรับ refresh data
	function handleRefresh() {
		return async ({ formData, update }: { formData: FormData; update: () => Promise<void> }) => {
			loading = true;
			try {
				// ใช้ update() เพื่อโหลดข้อมูลใหม่จาก server
				await update();
			} catch (err: any) {
				console.error("Error refreshing data:", err);
				error = err.message || "เกิดข้อผิดพลาดในการโหลดข้อมูล";
			} finally {
				loading = false;
			}
		};
	}

	// Svelte action สำหรับเปลี่ยนหน้า
	function handlePageChange() {
		return async ({ formData, update }: { formData: FormData; update: () => Promise<void> }) => {
			const page = parseInt(formData.get('page') as string) || 1;
			const limit = parseInt(formData.get('limit') as string) || currentLimit;
			
			if (page >= 1 && page <= (pagination?.totalPages || 1)) {
				loading = true;
				try {
					// ใช้ update() เพื่อโหลดข้อมูลใหม่จาก server
					await update();
				} catch (err: any) {
					console.error("Error changing page:", err);
					error = err.message || "เกิดข้อผิดพลาดในการโหลดข้อมูล";
				} finally {
					loading = false;
				}
			}
		};
	}

	// Svelte action สำหรับเปลี่ยนจำนวนรายการต่อหน้า
	function handleLimitChange() {
		return async ({ formData, update }: { formData: FormData; update: () => Promise<void> }) => {
			const limit = parseInt(formData.get('limit') as string) || 10;
			
			loading = true;
			try {
				// ใช้ update() เพื่อโหลดข้อมูลใหม่จาก server
				await update();
			} catch (err: any) {
				console.error("Error changing limit:", err);
				error = err.message || "เกิดข้อผิดพลาดในการโหลดข้อมูล";
			} finally {
				loading = false;
			}
		};
	}

	// onMount(() => {
	// 	// ตรวจสอบ authentication
	// 	const unsubscribe = authStore.subscribe((state) => {
	// 		if (!state.isAuthenticated) {
	// 			goto("/signin");
	// 		}
	// 	});

	// 	return unsubscribe;
	// });

	/**
	 * ✅ ไปยัง dashboard ของเว็บไซต์
	 */
	function goToSiteDashboard(siteId: string) {
		goto(`/dashboard/${siteId}`);
	}

	/**
	 * ✅ สร้างเว็บไซต์ใหม่
	 */
	function createNewWebsite() {
		goto("/dashboard/create");
	}

	/**
	 * ✅ ดูสถิติรวม
	 */
	function viewOverallStats() {
		goto("/dashboard/analytics");
	}

	// Type สำหรับ website
	interface Website {
		_id: string;
		name: string;
		domain: string;
		updatedAt: string;
		isPublished: boolean;
		status: string;
		stats?: {
			visitors: number;
			revenue: number;
			performance: number;
		};
	}
</script>

<svelte:head>
	<title>Dashboard - เว็บไซต์ของฉัน</title>
</svelte:head>

<div class="space-y-6">
	<!-- Header -->
	<div class="flex justify-between items-center">
		<div>
			<h1 class="text-2xl font-bold text-gray-900">เว็บไซต์ของฉัน</h1>
			<p class="text-gray-600">จัดการและติดตามเว็บไซต์ทั้งหมดของคุณ</p>
		</div>
		<div class="flex space-x-3">
			<button class="btn btn-primary" onclick={createNewWebsite}>
				<svg
					class="w-4 h-4 mr-2"
					fill="none"
					stroke="currentColor"
					viewBox="0 0 24 24"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						stroke-width="2"
						d="M12 6v6m0 0v6m0-6h6m-6 0H6"
					></path>
				</svg>
				สร้างเว็บไซต์ใหม่
			</button>
		</div>
	</div>

	<!-- Stats Cards -->
	{#if loading}
		<div class="grid grid-cols-1 md:grid-cols-4 gap-6">
			{#each Array(4) as _}
				<div class="bg-base-100 rounded-lg shadow p-6 animate-pulse">
					<div class="flex items-center">
						<div class="w-12 h-12 bg-gray-200 rounded-full"></div>
						<div class="ml-4 space-y-2">
							<div class="h-4 bg-gray-200 rounded w-24"></div>
							<div class="h-6 bg-gray-200 rounded w-16"></div>
						</div>
					</div>
				</div>
			{/each}
		</div>
	{:else if error}
		<div class="bg-red-50 border border-red-200 rounded-lg p-4">
			<div class="flex items-center">
				<svg
					class="w-5 h-5 text-red-600 mr-2"
					fill="none"
					stroke="currentColor"
					viewBox="0 0 24 24"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						stroke-width="2"
						d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
					></path>
				</svg>
				<span class="text-red-800">{error}</span>
				<form method="GET" use:enhance={handleRefresh}>
					<input type="hidden" name="page" value="1" />
					<input type="hidden" name="limit" value="10" />
					<button
						type="submit"
						class="ml-auto btn btn-sm btn-outline btn-error">ลองใหม่</button>
				</form>
			</div>
		</div>
	{:else}
		<div class="grid grid-cols-1 md:grid-cols-4 gap-6">
			<div class="bg-base-100 rounded-lg shadow p-6">
				<div class="flex items-center">
					<div class="p-3 rounded-full bg-blue-100 text-blue-600">
						<svg
							class="w-6 h-6"
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"
							></path>
						</svg>
					</div>
					<div class="ml-4">
						<p class="text-sm font-medium text-gray-600">
							เว็บไซต์ทั้งหมด
						</p>
						<p class="text-2xl font-semibold text-gray-900">
							{pagination?.total || websites.length}
						</p>
					</div>
				</div>
			</div>

			<div class="bg-base-100 rounded-lg shadow p-6">
				<div class="flex items-center">
					<div class="p-3 rounded-full bg-green-100 text-green-600">
						<svg
							class="w-6 h-6"
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
							></path>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
							></path>
						</svg>
					</div>
					<div class="ml-4">
						<p class="text-sm font-medium text-gray-600">
							ผู้เข้าชมรวม
						</p>
						<p class="text-2xl font-semibold text-gray-900">
							{websites
								.reduce(
									(sum: number, site: Website) =>
										sum + (site.stats?.visitors || 0),
									0,
								)
								.toLocaleString()}
						</p>
					</div>
				</div>
			</div>

			<div class="bg-base-100 rounded-lg shadow p-6">
				<div class="flex items-center">
					<div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
						<svg
							class="w-6 h-6"
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
							></path>
						</svg>
					</div>
					<div class="ml-4">
						<p class="text-sm font-medium text-gray-600">
							รายได้รวม
						</p>
						<p class="text-2xl font-semibold text-gray-900">
							฿{websites
								.reduce(
									(sum: number, site: Website) =>
										sum + (site.stats?.revenue || 0),
									0,
								)
								.toLocaleString()}
						</p>
					</div>
				</div>
			</div>

			<div class="bg-base-100 rounded-lg shadow p-6">
				<div class="flex items-center">
					<div class="p-3 rounded-full bg-purple-100 text-purple-600">
						<svg
							class="w-6 h-6"
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M13 10V3L4 14h7v7l9-11h-7z"
							></path>
						</svg>
					</div>
					<div class="ml-4">
						<p class="text-sm font-medium text-gray-600">
							ประสิทธิภาพเฉลี่ย
						</p>
						<p class="text-2xl font-semibold text-gray-900">
							{websites.length > 0
								? Math.round(
										websites.reduce(
											(sum: number, site: Website) =>
												sum +
												(site.stats?.performance || 0),
											0,
										) / websites.length,
									)
								: 0}%
						</p>
					</div>
				</div>
			</div>
		</div>
	{/if}

	<!-- Websites List -->
	<div class="bg-base-100 rounded-lg shadow">
		<div class="px-6 py-4 border-b border-gray-200">
			<div class="flex justify-between items-center">
				<h3 class="text-lg font-medium text-gray-900">เว็บไซต์ทั้งหมด</h3>
				{#if pagination}
					<div class="flex items-center space-x-4">
						<label for="currentLimit" class="text-sm text-gray-600">แสดง:</label>
						<form method="GET" use:enhance={handleLimitChange}>
							<select 
								name="limit"
								class="select select-sm select-bordered"
								value={currentLimit}
							>
								<option value="5">5 รายการ</option>
								<option value="10">10 รายการ</option>
								<option value="20">20 รายการ</option>
								<option value="50">50 รายการ</option>
							</select>
						</form>
					</div>
				{/if}
			</div>
		</div>
		<div class="p-6">
			{#if websites.length === 0}
				<div class="text-center py-12">
					<svg
						class="w-16 h-16 text-gray-400 mx-auto mb-4"
						fill="none"
						stroke="currentColor"
						viewBox="0 0 24 24"
					>
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"
						></path>
					</svg>
					<h3 class="text-lg font-medium text-gray-900 mb-2">
						ยังไม่มีเว็บไซต์
					</h3>
					<p class="text-gray-600 mb-6">
						เริ่มต้นสร้างเว็บไซต์แรกของคุณ
					</p>
					<button class="btn btn-primary" onclick={createNewWebsite}>
						สร้างเว็บไซต์แรก
					</button>
				</div>
			{:else}
				<div class="space-y-4">
					{#each websites as website (website._id)}
						<div
							class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
						>
							<div class="flex items-center space-x-4">
								<div
									class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center"
								>
									<span class="text-white font-bold text-lg">
										{website.name.charAt(0).toUpperCase()}
									</span>
								</div>
								<div>
									<h4 class="font-medium text-gray-900">
										{website.name}
									</h4>
									<p class="text-sm text-gray-600">
										{website.domain}
									</p>
									<p class="text-xs text-gray-500">
										อัปเดตล่าสุด: {new Date(
											website.updatedAt,
										).toLocaleDateString("th-TH")}
									</p>
									{#if website.isPublished}
										<span
											class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 mt-1"
										>
											เผยแพร่แล้ว
										</span>
									{:else}
										<span
											class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 mt-1"
										>
											ยังไม่เผยแพร่
										</span>
									{/if}
								</div>
							</div>
							<div class="flex items-center space-x-4">
								<div class="text-right">
									<p
										class="text-sm font-medium text-gray-900"
									>
										{(
											website.stats?.visitors || 0
										).toLocaleString()} ผู้เข้าชม
									</p>
									<p class="text-xs text-gray-500">
										฿{(
											website.stats?.revenue || 0
										).toLocaleString()} รายได้
									</p>
								</div>
								<span
									class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {website.status ===
									'active'
										? 'bg-green-100 text-green-800'
										: website.status === 'maintenance'
											? 'bg-yellow-100 text-yellow-800'
											: 'bg-red-100 text-red-800'}"
								>
									{website.status === "active"
										? "ใช้งาน"
										: website.status === "maintenance"
											? "บำรุงรักษา"
											: "ไม่ใช้งาน"}
								</span>
								<button
									class="btn btn-primary btn-sm"
									onclick={() =>
										goToSiteDashboard(website._id)}
								>
									จัดการ
								</button>
							</div>
						</div>
					{/each}
				</div>

				<!-- Pagination -->
				{#if pagination && pagination.totalPages > 1}
					<div class="flex justify-between items-center mt-6 pt-6 border-t border-gray-200">
						<div class="text-sm text-gray-600">
							แสดง {((pagination.page - 1) * pagination.limit) + 1} ถึง {Math.min(pagination.page * pagination.limit, pagination.total)} จาก {pagination.total} รายการ
						</div>
						<div class="flex items-center space-x-2">
							<form method="GET" use:enhance={handlePageChange}>
								<input type="hidden" name="limit" value={currentLimit} />
								<button
									type="submit"
									name="page"
									value={pagination.page - 1}
									class="btn btn-sm btn-outline"
									disabled={!pagination.hasPrev}
								>
									<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
									</svg>
									ก่อนหน้า
								</button>
							</form>
							
							{#each Array(pagination.totalPages) as _, i}
								{@const pageNum = i + 1}
								{#if pageNum === pagination.page}
									<button class="btn btn-sm btn-primary">{pageNum}</button>
								{:else if pageNum === 1 || pageNum === pagination.totalPages || (pageNum >= pagination.page - 1 && pageNum <= pagination.page + 1)}
									<form method="GET" use:enhance={handlePageChange} class="inline">
										<input type="hidden" name="limit" value={currentLimit} />
										<button
											type="submit"
											name="page"
											value={pageNum}
											class="btn btn-sm btn-outline"
										>
											{pageNum}
										</button>
									</form>
								{:else if pageNum === pagination.page - 2 || pageNum === pagination.page + 2}
									<span class="px-2 text-gray-400">...</span>
								{/if}
							{/each}
							
							<form method="GET" use:enhance={handlePageChange}>
								<input type="hidden" name="limit" value={currentLimit} />
								<button
									type="submit"
									name="page"
									value={pagination.page + 1}
									class="btn btn-sm btn-outline"
									disabled={!pagination.hasNext}
								>
									ถัดไป
									<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
									</svg>
								</button>
							</form>
						</div>
					</div>
				{/if}
			{/if}
		</div>
	</div>

	<!-- Quick Actions -->
	<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
		<div class="bg-base-100 rounded-lg shadow p-6">
			<h3 class="text-lg font-medium text-gray-900 mb-4">
				การดำเนินการด่วน
			</h3>
			<div class="space-y-3">
				<button
					class="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
					onclick={createNewWebsite}
				>
					<div class="flex items-center">
						<svg
							class="w-5 h-5 text-blue-600 mr-3"
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M12 6v6m0 0v6m0-6h6m-6 0H6"
							></path>
						</svg>
						<span class="font-medium">สร้างเว็บไซต์ใหม่</span>
					</div>
				</button>
				<button
					class="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
					onclick={() => goto("/dashboard/topup")}
				>
					<div class="flex items-center">
						<svg
							class="w-5 h-5 text-green-600 mr-3"
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
							></path>
						</svg>
						<span class="font-medium">เติมเงิน</span>
					</div>
				</button>
				<button
					class="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
					onclick={viewOverallStats}
				>
					<div class="flex items-center">
						<svg
							class="w-5 h-5 text-purple-600 mr-3"
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
							></path>
						</svg>
						<span class="font-medium">ดูสถิติรวม</span>
					</div>
				</button>
			</div>
		</div>

		<div class="bg-base-100 rounded-lg shadow p-6">
			<h3 class="text-lg font-medium text-gray-900 mb-4">
				การแจ้งเตือนล่าสุด
			</h3>
			<div class="space-y-3">
				<div
					class="flex items-start space-x-3 p-3 rounded-lg bg-blue-50"
				>
					<svg
						class="w-5 h-5 text-blue-600 mt-0.5"
						fill="none"
						stroke="currentColor"
						viewBox="0 0 24 24"
					>
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
						></path>
					</svg>
					<div>
						<p class="text-sm font-medium text-blue-900">
							เว็บไซต์อัปเดตสำเร็จ
						</p>
						<p class="text-xs text-blue-700">
							MyWebsite.com ได้รับการอัปเดตแล้ว
						</p>
					</div>
				</div>
				<div
					class="flex items-start space-x-3 p-3 rounded-lg bg-green-50"
				>
					<svg
						class="w-5 h-5 text-green-600 mt-0.5"
						fill="none"
						stroke="currentColor"
						viewBox="0 0 24 24"
					>
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
						></path>
					</svg>
					<div>
						<p class="text-sm font-medium text-green-900">
							การชำระเงินสำเร็จ
						</p>
						<p class="text-xs text-green-700">
							ชำระเงินแพ็กเกจธุรกิจแล้ว
						</p>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
