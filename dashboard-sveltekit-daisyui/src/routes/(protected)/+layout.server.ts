import { redirect } from '@sveltejs/kit';
import type { LayoutServerLoad } from './$types';

// Cache สำหรับ server-side (ใน memory)
const userCache = new Map<string, { user: any; timestamp: number }>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 นาที

function getUserFromServerCache(token: string): any | null {
    const cached = userCache.get(token);
    if (!cached) return null;
    
    const cacheAge = Date.now() - cached.timestamp;
    if (cacheAge > CACHE_DURATION) {
        userCache.delete(token);
        return null;
    }
    
    return cached.user;
}

function saveUserToServerCache(token: string, user: any) {
    userCache.set(token, {
        user,
        timestamp: Date.now()
    });
}

export const load: LayoutServerLoad = async ({ cookies, locals, url }) => {
    const accessToken = cookies.get('accessToken');
    console.log("accessToken", accessToken);
    console.log("locals", locals);

    // ถ้าไม่มี token ให้ redirect ไปหน้า signin
    if (!accessToken) {
        throw redirect(302, '/signin');
    }

    // ตรวจสอบ cache ก่อน
    const cachedUser = getUserFromServerCache(accessToken);
    if (cachedUser) {
        console.log("Using cached user data");
        return {
            user: cachedUser,
            accessToken,
            fromCache: true
        };
    }

    // ถ้าไม่มี cache ให้เรียก API
    try {
        console.log("Fetching user data from API");
        const response = await fetch('http://localhost:5000/v1/users/me', {
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json',
            },
        });

        if (!response.ok) {
            // Token ไม่ถูกต้อง ลบ cookie และ redirect
            cookies.delete('accessToken', { path: '/' });
            cookies.delete('refreshToken', { path: '/' });
            throw redirect(302, '/signin');
        }

        const result = await response.json();
        
        // บันทึกลง cache
        saveUserToServerCache(accessToken, result.data);

        return {
            user: result.data,
            accessToken,
            fromCache: false
        };
    } catch (error) {
        // เกิดข้อผิดพลาดในการตรวจสอบ token
        cookies.delete('accessToken', { path: '/' });
        cookies.delete('refreshToken', { path: '/' });
        throw redirect(302, '/signin');
    }
};