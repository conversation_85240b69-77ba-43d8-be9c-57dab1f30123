import { LogCategory, logger } from '$lib/utils/logger';
import { fail, redirect } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { ApiClient } from '$lib/api/client';

const apiClient = new ApiClient();

export const csr = false;

export const load: PageServerLoad = async ({ cookies, locals }) => {
  // เมื่อมี GET request ไป /signout ให้ทำการ signout
  try {
    // ✅ Get tokens from cookies
    const accessToken = cookies.get('accessToken');
    const refreshToken = cookies.get('refreshToken');
    const userId = locals.user?.id;

    logger.info(LogCategory.AUTH, 'signout_attempt', 'User attempting signout via GET', {
      userId,
      hasAccessToken: !!accessToken,
      hasRefreshToken: !!refreshToken,
    });

    // ✅ Call backend signout API
    if (accessToken) {
      try {
        const result = await apiClient.makeAuthenticatedRequest<{ message: string }>('/users/me/signout', accessToken, {
          method: 'POST'
        });

        logger.info(LogCategory.AUTH, 'signout_api_success', 'Backend signout successful', {
          userId,
          message: result.message
        });
      } catch (error) {
        logger.warn(LogCategory.AUTH, 'signout_api_failed', 'Backend signout failed', {
          userId,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    // ✅ Clear all auth-related cookies
    const cookiesToClear = [
      'accessToken',
      'refreshToken',
      'session_id',
      'csrf_token',
      'remember_me',
    ];

    cookiesToClear.forEach(cookieName => {
      cookies.delete(cookieName, { path: '/' });
    });

    logger.info(LogCategory.AUTH, 'signout_success', 'User signed out successfully via GET', {
      userId,
      cookiesCleared: cookiesToClear.length,
    });

  } catch (error) {
    logger.error(LogCategory.AUTH, 'signout_error', 'Signout process failed', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: locals.user?.id,
    });
  }

  // ✅ Redirect to signin page
  throw redirect(302, '/signin');
};

export const actions: Actions = {
  default: async ({ request, cookies, locals }) => {
    try {
      // ✅ Get tokens from cookies
      const accessToken = cookies.get('accessToken');
      const refreshToken = cookies.get('refreshToken');
      const userId = locals.user?.id;

      logger.info(LogCategory.AUTH, 'signout_attempt', 'User attempting signout', {
        userId,
        hasAccessToken: !!accessToken,
        hasRefreshToken: !!refreshToken,
      });

      // ✅ Call backend signout API
      if (accessToken) {
        try {
          const result = await apiClient.makeAuthenticatedRequest<{ message: string }>('/users/me/signout', accessToken, {
            method: 'POST'
          });

          logger.info(LogCategory.AUTH, 'signout_api_success', 'Backend signout successful', {
            userId,
            message: result.message
          });
        } catch (error) {
          logger.warn(LogCategory.AUTH, 'signout_api_failed', 'Backend signout failed', {
            userId,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      // ✅ Clear all auth-related cookies
      const cookiesToClear = [
        'accessToken',
        'refreshToken',
        'session_id',
        'csrf_token',
        'remember_me',
      ];

      cookiesToClear.forEach(cookieName => {
        cookies.delete(cookieName, { path: '/' });
      });

      logger.info(LogCategory.AUTH, 'signout_success', 'User signed out successfully', {
        userId,
        cookiesCleared: cookiesToClear.length,
      });

      // ✅ Redirect to signin page
      throw redirect(302, '/signin');
    } catch (error) {
      if (error instanceof Response) {
        throw error; // Re-throw redirects
      }

      logger.error(LogCategory.AUTH, 'signout_error', 'Signout process failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: locals.user?.id,
      });

      return fail(500, {
        error: 'เกิดข้อผิดพลาดในการออกจากระบบ',
      });
    }
  },
};
