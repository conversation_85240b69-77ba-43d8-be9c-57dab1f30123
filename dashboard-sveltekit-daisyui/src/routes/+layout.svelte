<script lang="ts">
	import "$lib/assets/css/app.css";
	import { onMount } from "svelte";
	import { page } from "$app/state"; 
	import favicon from "$lib/assets/favicon.svg"; 

	let currentTheme = $state("light");
	let { children } = $props();
	let isLoading = $state(true);

	onMount(() => {
		// โหลด theme จาก localStorage
		const savedTheme = localStorage.getItem("theme") || "light";
		currentTheme = savedTheme;
		
		// ใช้ theme กับ document
		document.documentElement.setAttribute('data-theme', currentTheme);
		
		// ตั้งค่า locale จาก server
		if (page.data.initialLocale) {
			document.documentElement.lang = page.data.initialLocale;
		}
		
		isLoading = false;
	});
</script>

<svelte:head>
	<link rel="icon" href={favicon} />
	<title>แพลตฟอร์มสร้างเว็บไซต์ที่ง่ายและรวดเร็ว</title>
	<meta name="description" content="สร้างเว็บไซต์อีคอมเมิร์ซที่ง่ายและรวดเร็วด้วยแพลตฟอร์มของเรา" />
</svelte:head>

{#if isLoading}
	<div class="min-h-screen flex items-center justify-center bg-base-100">
		<div class="loading loading-spinner loading-lg text-primary w-24 h-24"></div>
	</div>
{:else}
	{@render children()}
{/if}
