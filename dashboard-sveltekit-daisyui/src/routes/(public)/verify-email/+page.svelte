<script lang="ts">
	import { enhance } from "$app/forms";
	import { page } from "$app/state";

	let { form } = $props<{ form: any }>();

	let isLoading = $state(false);

	// Get token from URL params
	let token = page.url.searchParams.get("token") || "";

	function handleSubmit() {
		isLoading = true;
	}
</script>

<svelte:head>
	<title>ยืนยันอีเมล - Dashboard</title>
</svelte:head>

<div class="space-y-6">
	<div class="text-center">
		<h2 class="text-2xl font-bold text-gray-900">ยืนยันอีเมล</h2>
		<p class="text-gray-600 mt-2">ยืนยันอีเมลของคุณเพื่อเปิดใช้งานบัญชี</p>
	</div>

	{#if form?.error}
		<div class="alert alert-error">
			<svg
				class="w-5 h-5"
				fill="none"
				stroke="currentColor"
				viewBox="0 0 24 24"
			>
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
				></path>
			</svg>
			<span>{form.error}</span>
		</div>
	{/if}

	{#if form?.success}
		<div class="alert alert-success">
			<svg
				class="w-5 h-5"
				fill="none"
				stroke="currentColor"
				viewBox="0 0 24 24"
			>
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
				></path>
			</svg>
			<span>{form.message}</span>
		</div>
	{/if}

	{#if token}
		<form
			method="POST"
			action="?/verifyEmail"
			use:enhance={handleSubmit}
			class="space-y-4"
		>
			<input type="hidden" name="token" value={token} />

			<button
				type="submit"
				disabled={isLoading}
				class="btn btn-primary w-full"
			>
				{#if isLoading}
					<span class="loading loading-spinner loading-sm"></span>
					กำลังยืนยันอีเมล...
				{:else}
					ยืนยันอีเมล
				{/if}
			</button>
		</form>
	{:else}
		<div class="text-center">
			<div class="alert alert-warning">
				<svg
					class="w-5 h-5"
					fill="none"
					stroke="currentColor"
					viewBox="0 0 24 24"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						stroke-width="2"
						d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
					></path>
				</svg>
				<span>ไม่พบ token สำหรับยืนยันอีเมล</span>
			</div>
		</div>
	{/if}

	<div class="text-center mt-6">
		<p class="text-gray-600">
			<a href="/signin" class="text-primary hover:underline font-medium">
				กลับไปหน้าเข้าสู่ระบบ
			</a>
		</p>
	</div>
</div>
