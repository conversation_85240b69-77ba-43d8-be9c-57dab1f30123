import type { PageServerLoad, Actions } from './$types';
import { fail } from '@sveltejs/kit';
import { verifyEmailSchema } from '$lib/schemas/auth';

const API_BASE_URL = 'http://localhost:5000/v1';

export const load: PageServerLoad = async () => {
	return {};
};

export const actions: Actions = {
	verifyEmail: async ({ request }) => {
		const formData = await request.formData();
		const token = formData.get('token') as string;

		try {
			const validatedData = verifyEmailSchema.parse({ token });

			const response = await fetch(`${API_BASE_URL}/users/verify-email`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(validatedData),
			});

			const result = await response.json();

			if (!response.ok) {
				return fail(400, {
					error: result.message || 'เกิดข้อผิดพลาดในการยืนยันอีเมล',
				});
			}

			return {
				success: true,
				message: 'ยืนยันอีเมลสำเร็จ',
			};
		} catch (error) {
			return fail(400, {
				error: 'Token ไม่ถูกต้อง',
			});
		}
	},
}; 