import type { LayoutServerLoad } from './$types';
import { redirect } from '@sveltejs/kit';

export const load: LayoutServerLoad = async ({ locals, cookies, request }) => {
  // Debug information
  console.log('🔍 Public Layout Server - URL:', new URL(request.url).pathname);
  console.log('🔍 Public Layout Server - locals.user:', locals.user ? 'exists' : 'null');
  console.log('🔍 Public Layout Server - locals.token:', locals.token ? 'exists' : 'null');

  // รับภาษาจาก cookie หรือ header
  const savedLocale = cookies.get('locale')
    || request.headers.get('accept-language')?.split(',')[0]?.split('-')[0]
    || 'th';

  // ถ้ามี user อยู่แล้วให้ redirect ไป dashboard
  if (locals.user) {
    console.log('🔍 Public Layout Server - Redirecting to dashboard');
    throw redirect(302, '/dashboard');
  }

  console.log('🔍 Public Layout Server - No user found, staying on public page');

  return {
    user: locals.user || null,
    token: locals.token || null,
    initialLocale: savedLocale,
  };
};