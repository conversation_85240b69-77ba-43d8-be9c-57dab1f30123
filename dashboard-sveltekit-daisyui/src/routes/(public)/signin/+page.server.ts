import type { Actions } from './$types';
import { fail } from '@sveltejs/kit';
import { signinSchema } from '$lib/schemas/auth';
import { ApiClient } from '$lib/api/client';

export const prerender = false;

const apiClient = new ApiClient();

export const actions: Actions = {
	signin: async ({ request, cookies }) => {
		const formData = await request.formData();
		const email = formData.get('email') as string;
		const password = formData.get('password') as string;
		const rememberMe = formData.get('rememberMe') === 'on';

		try {
			const validatedData = signinSchema.parse({ email, password });

			const result = await apiClient.makePublicRequest<{
				success: boolean;
				message: string;
				data: {
					accessToken: string;
					refreshToken: string;
					user: any;
					rememberMe?: boolean;
				};
			}>('/users/signin', {
				method: 'POST',
				body: { ...validatedData, rememberMe },
			});

			// Set cookies - ใช้ค่า rememberMe จาก backend response หรือ fallback เป็น form value
			const shouldRemember = result.data.rememberMe ?? rememberMe;
			const maxAge = shouldRemember ? 60 * 60 * 24 * 30 : 60 * 60 * 24 * 7; // 30 days or 7 days

			cookies.set('accessToken', result.data.accessToken, {
				path: '/',
				httpOnly: true,
				secure: process.env.NODE_ENV === 'production',
				sameSite: 'strict',
				maxAge,
			});

			cookies.set('refreshToken', result.data.refreshToken, {
				path: '/',
				httpOnly: true,
				secure: process.env.NODE_ENV === 'production',
				sameSite: 'strict',
				maxAge: 60 * 60 * 24 * 30, // 30 days
			});

			return {
				success: true,
				message: 'เข้าสู่ระบบสำเร็จ',
				user: result.data.user,
			};
		} catch (error) {
			console.error('Signin error:', error);

			if (error instanceof Error) {
				return fail(400, {
					error: error.message,
					email,
				});
			}

			return fail(400, {
				error: 'ข้อมูลไม่ถูกต้อง',
				email,
			});
		}
	},
}; 