<script lang="ts">
	import { enhance } from '$app/forms';
	import Card from '$lib/components/ui/Card.svelte';
	import Input from '@/lib/components/ui/Input.svelte';
	import Button from '$lib/components/ui/Button.svelte';

	import { browser } from '$app/environment';
	import { onMount } from 'svelte';
	import { authStore, logger, LogCategory, showToast, showError } from '$lib';
	import type { SigninForm } from '$lib/schemas/auth';

	let { form } = $props<{ form: any }>();

	// ✅ Form data state
	let formData = $state<SigninForm>({
		email: '',
		password: '',
		rememberMe: false
	});

	let isLoading = $state(false);

	// ✅ Form validation errors
	let formErrors = $state<Record<string, string>>({});

	// ✅ Derived state for form validation
	const isFormValid = $derived(
		// Basic validation
		formData.email &&
			formData.password &&
			// Email validation
			/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email) &&
			// Password validation
			formData.password.length >= 6
	);

	// ✅ Debug validation state
	$effect(() => {
		console.log('Form validation state:', {
			isValid: isFormValid,
			data: formData,
			errors: formErrors,
			isLoading
		});
	});

	// ✅ Real-time validation
	$effect(() => {
		const errors: Record<string, string> = {};

		// Email validation
		if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
			errors.email = 'รูปแบบอีเมลไม่ถูกต้อง';
		}

		// Password validation
		if (formData.password && formData.password.length < 6) {
			errors.password = 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร';
		}

		formErrors = errors;
	});

	onMount(() => {
		// ลบ auto-redirect logic ออกเพื่อหลีกเลี่ยง redirect loop
		// การ redirect จะทำผ่าน form submission แทน

		// Clear sensitive data from localStorage if exists
		if (typeof window !== 'undefined') {
			const oldToken = localStorage.getItem('auth_token');
			if (oldToken) {
				localStorage.removeItem('auth_token');
				logger.info(LogCategory.AUTH, 'old_token_cleared', 'Cleared old auth token on signin page');
			}
		}
	});

	/**
	 * ✅ Handle form submission
	 */
	function handleSubmit() {
		return async ({ result, update }: { result: any; update: () => Promise<void> }) => {
			isLoading = true;

			await update();

			if (result.type === 'success' && result.data?.success) {
				// ✅ Success case
				if (result.data?.user) {
					// อัปเดต auth store
					authStore.signin(result.data.user);

					// เก็บ tokens ใน localStorage สำหรับ client-side usage
					if (browser) {
						// Note: tokens จะถูกเก็บใน HttpOnly cookies โดย server แล้ว
						// localStorage ใช้สำหรับ client-side operations เท่านั้น
						localStorage.setItem('user', JSON.stringify(result.data.user));
					}
				}

				logger.info(LogCategory.AUTH, 'client_signin_success', 'Client-side signin success');

				showToast('success', 'เข้าสู่ระบบสำเร็จ!', {
					timer: 1000,
					timerProgressBar: true,
					showCloseButton: false,
					allowEscapeKey: true
				});

				// ใช้ window.location.href แทน goto เพื่อ force page reload
				// และให้ server-side authentication ทำงาน
				setTimeout(() => {
					window.location.href = '/dashboard';
				}, 1100);
			} else if (result.type === 'failure') {
				// ✅ Handle different error types
				const errorData = result.data;
				const errorMessage = errorData?.error || 'เข้าสู่ระบบล้มเหลว';

				logger.warn(LogCategory.AUTH, 'client_signin_failed', 'Client-side signin failed', {
					error: errorMessage
				});

				showError('เข้าสู่ระบบล้มเหลว', errorMessage);
			}

			isLoading = false;
		};
	}

	/**
	 * ✅ Handle Enter key submission
	 */
	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Enter') {
			const target = event.target as HTMLElement;
			const form = target?.closest('form') as HTMLFormElement;
			if (form && !isLoading && isFormValid) {
				form.requestSubmit();
			}
		}
	}
</script>

<svelte:head>
	<title>เข้าสู่ระบบ - Dashboard</title>
</svelte:head>

<Card
	centered
	title="เข้าสู่ระบบ"
	subtitle="เข้าสู่ระบบเพื่อจัดการเว็บไซต์ของคุณ"
	class="space-y-6"
>
	{#if form?.error}
		<div class="alert alert-error">
			<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
				></path>
			</svg>
			<span>{form.error}</span>
		</div>
	{/if}

	{#if form?.success}
		<div class="alert alert-success">
			<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
				></path>
			</svg>
			<span>{form.message}</span>
		</div>
	{/if}

	<form method="POST" action="?/signin" use:enhance={handleSubmit} class="space-y-4">
		<Input
			icon="solar:letter-line-duotone"
			label="อีเมล"
			required
			type="email"
			id="email"
			name="email"
			bind:value={formData.email}
			placeholder="<EMAIL>"
			error={formErrors.email}
			onkeydown={handleKeydown}
			showRequired={true}
		/>

		<Input
			icon="solar:key-minimalistic-square-2-line-duotone"
			label="รหัสผ่าน"
			required
			type="password"
			id="password"
			name="password"
			bind:value={formData.password}
			placeholder="••••••••"
			error={formErrors.password}
			onkeydown={handleKeydown}
			showRequired={true}
		/>

		<div class="flex items-center justify-between">
			<label for="rememberMe" class="label cursor-pointer">
				<input
					id="rememberMe"
					name="rememberMe"
					type="checkbox"
					bind:checked={formData.rememberMe}
					class="checkbox checkbox-primary checkbox-sm"
				/>
				<span class="label-text ml-2">จดจำฉัน</span>
			</label>

			<a href="/forgot-password" class="link link-primary text-sm"> ลืมรหัสผ่าน? </a>
		</div>

		<!-- ✅ Submit button with proper loading state -->
		<Button
			label="เข้าสู่ระบบ"
			type="submit"
			color="primary"
			size="lg"
			block
			loading={isLoading}
			disabled={isLoading || !isFormValid}
		/>

		<!-- ✅ Debug information for development -->
		{#if import.meta.env.DEV}
			<div class="text-xs text-gray-500 mt-2">
				<div>Form Valid: {isFormValid}</div>
				<div>Loading: {isLoading}</div>
				<div>Remember Me: {formData.rememberMe}</div>
			</div>
		{/if}
	</form>

	<div class="divider">หรือ</div>

	<div class="space-y-3">
		<button class="btn btn-outline w-full">
			<svg class="w-5 h-5 mr-2" viewBox="0 0 24 24">
				<path
					fill="currentColor"
					d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
				/>
				<path
					fill="currentColor"
					d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
				/>
				<path
					fill="currentColor"
					d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
				/>
				<path
					fill="currentColor"
					d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
				/>
			</svg>
			เข้าสู่ระบบด้วย Google
		</button>
	</div>

	<div class="text-center mt-6">
		<p class="text-gray-600">
			ยังไม่มีบัญชี?
			<a href="/signup" class="text-primary hover:underline font-medium"> สมัครสมาชิก </a>
		</p>
	</div>
</Card>
