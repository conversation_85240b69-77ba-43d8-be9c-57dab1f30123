<script lang="ts">
	import { enhance } from "$app/forms";
	import { page } from "$app/state";

	let { form } = $props<{ form: any }>();

	let password = $state("");
	let confirmPassword = $state("");
	let isLoading = $state(false);

	// Get token from URL params
	let token = page.url.searchParams.get("token") || "";

	function handleSubmit() {
		isLoading = true;
	}
</script>

<svelte:head>
	<title>รีเซ็ตรหัสผ่าน - Dashboard</title>
</svelte:head>

<div class="space-y-6">
	<div class="text-center">
		<h2 class="text-2xl font-bold text-gray-900">รีเซ็ตรหัสผ่าน</h2>
		<p class="text-gray-600 mt-2">ตั้งรหัสผ่านใหม่สำหรับบัญชีของคุณ</p>
	</div>

	{#if form?.error}
		<div class="alert alert-error">
			<svg
				class="w-5 h-5"
				fill="none"
				stroke="currentColor"
				viewBox="0 0 24 24"
			>
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
				></path>
			</svg>
			<span>{form.error}</span>
		</div>
	{/if}

	{#if form?.success}
		<div class="alert alert-success">
			<svg
				class="w-5 h-5"
				fill="none"
				stroke="currentColor"
				viewBox="0 0 24 24"
			>
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
				></path>
			</svg>
			<span>{form.message}</span>
		</div>
	{/if}

	<form
		method="POST"
		action="?/resetPassword"
		use:enhance={handleSubmit}
		class="space-y-4"
	>
		<input type="hidden" name="token" value={token} />

		<div>
			<label
				for="password"
				class="block text-sm font-medium text-gray-700 mb-2"
			>
				รหัสผ่านใหม่
			</label>
			<input
				type="password"
				id="password"
				name="password"
				bind:value={password}
				required
				class="input input-bordered w-full"
				placeholder="รหัสผ่านใหม่อย่างน้อย 6 ตัวอักษร"
			/>
		</div>

		<div>
			<label
				for="confirmPassword"
				class="block text-sm font-medium text-gray-700 mb-2"
			>
				ยืนยันรหัสผ่านใหม่
			</label>
			<input
				type="password"
				id="confirmPassword"
				bind:value={confirmPassword}
				required
				class="input input-bordered w-full"
				placeholder="ยืนยันรหัสผ่านใหม่"
			/>
		</div>

		<button
			type="submit"
			disabled={isLoading || password !== confirmPassword || !token}
			class="btn btn-primary w-full"
		>
			{#if isLoading}
				<span class="loading loading-spinner loading-sm"></span>
				กำลังรีเซ็ตรหัสผ่าน...
			{:else}
				รีเซ็ตรหัสผ่าน
			{/if}
		</button>
	</form>

	<div class="text-center mt-6">
		<p class="text-gray-600">
			<a href="/signin" class="text-primary hover:underline font-medium">
				กลับไปหน้าเข้าสู่ระบบ
			</a>
		</p>
	</div>
</div>
