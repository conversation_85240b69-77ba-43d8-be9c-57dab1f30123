import type { PageServerLoad, Actions } from './$types';
import { fail } from '@sveltejs/kit';
import { resetPasswordSchema } from '$lib/schemas/auth';

const API_BASE_URL = 'http://localhost:5000/v1';

export const load: PageServerLoad = async () => {
	return {};
};

export const actions: Actions = {
	resetPassword: async ({ request }) => {
		const formData = await request.formData();
		const token = formData.get('token') as string;
		const password = formData.get('password') as string;

		try {
			const validatedData = resetPasswordSchema.parse({ token, password });

			const response = await fetch(`${API_BASE_URL}/users/reset-password`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(validatedData),
			});

			const result = await response.json();

			if (!response.ok) {
				return fail(400, {
					error: result.message || 'เกิดข้อผิดพลาดในการรีเซ็ตรหัสผ่าน',
				});
			}

			return {
				success: true,
				message: 'รีเซ็ตรหัสผ่านสำเร็จ',
			};
		} catch (error) {
			return fail(400, {
				error: 'ข้อมูลไม่ถูกต้อง',
			});
		}
	},
}; 