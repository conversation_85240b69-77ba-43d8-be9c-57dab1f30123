<script lang="ts">
	import { enhance } from "$app/forms";
	import Card from "$lib/components/ui/Card.svelte";
	import Input from "@/lib/components/ui/Input.svelte";
	import Button from "$lib/components/ui/Button.svelte";
	import { goto } from "$app/navigation";
	import { onMount } from "svelte";
	import { authStore, logger, LogCategory, showToast, showError } from "$lib";
	import type { SignupForm } from "$lib/schemas/auth";
	import Checkbox from "$lib/components/ui/Checkbox.svelte";
	let { form } = $props<{ form: any }>();

	// ✅ Form data state
	let formData = $state<SignupForm>({
		email: "<EMAIL>",
		password: "testtest",
		confirmPassword: "testtest",
		agreeToTerms: false,
	});

	let isLoading = $state(false);

	// ✅ Form validation errors
	let formErrors = $state<Record<string, string>>({});

	// ✅ Derived state for form validation
	const isFormValid = $derived(
		// Basic validation
		formData.email &&
			formData.password &&
			formData.confirmPassword &&
			formData.agreeToTerms &&
			// Email validation
			/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email) &&
			// Password validation
			formData.password.length >= 6 &&
			// Password confirmation
			formData.password === formData.confirmPassword,
	);

	// ✅ Debug validation state
	$effect(() => {
		console.log("Form validation state:", {
			isValid: isFormValid,
			data: formData,
			errors: formErrors,
			isLoading,
		});
	});

	// ✅ Real-time validation
	$effect(() => {
		const errors: Record<string, string> = {};

		// Email validation
		if (
			formData.email &&
			!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)
		) {
			errors.email = "รูปแบบอีเมลไม่ถูกต้อง";
		}

		// Password validation
		if (formData.password && formData.password.length < 6) {
			errors.password = "รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร";
		}

		// Password confirmation validation
		if (
			formData.confirmPassword &&
			formData.password !== formData.confirmPassword
		) {
			errors.confirmPassword = "รหัสผ่านไม่ตรงกัน";
		}

		formErrors = errors;
	});

	onMount(() => {
		// ถ้า signin แล้วให้ redirect ไป dashboard
		const unsubscribe = authStore.subscribe((state) => {
			if (state.isAuthenticated) {
				goto("/dashboard");
			}
		});

		return unsubscribe;
	});

	/**
	 * ✅ Handle form submission
	 */
	function handleSubmit() {
		return async ({
			result,
			update,
		}: {
			result: any;
			update: () => Promise<void>;
		}) => {
			isLoading = true;

			await update();

			if (result.type === "success" && result.data?.success) {
				// ✅ Success case
				logger.info(
					LogCategory.AUTH,
					"client_signup_success",
					"Client-side registration success",
				);

				// Redirect to signin page
				goto("/signin");

				showToast(
					"success",
					"สมัครสมาชิกสำเร็จ! กรุณาตรวจสอบอีเมลเพื่อยืนยันบัญชี",
					{
						timer: 3000,
						timerProgressBar: true,
						showCloseButton: false,
						allowEscapeKey: true,
					},
				);
			} else if (result.type === "failure") {
				// ✅ Handle different error types
				const errorData = result.data;
				const errorMessage = errorData?.error || "สมัครสมาชิกล้มเหลว";

				logger.warn(
					LogCategory.AUTH,
					"client_signup_failed",
					"Client-side registration failed",
					{
						error: errorMessage,
					},
				);

				showError("สมัครสมาชิกล้มเหลว", errorMessage);
			}

			isLoading = false;
		};
	}

	/**
	 * ✅ Handle Enter key submission
	 */
	function handleKeydown(event: KeyboardEvent) {
		if (event.key === "Enter") {
			const target = event.target as HTMLElement;
			const form = target?.closest("form") as HTMLFormElement;
			if (form && !isLoading && isFormValid) {
				form.requestSubmit();
			}
		}
	}

	// Terms & Conditions
	const termList = [
		{
			icon: "solar:info-circle-bold",
			label: "การมีสิทธิ์ของผู้ใช้",
			content:
				"เว็บไซต์นี้เปิดให้บริการเฉพาะแก่องค์กรและบุคคลที่บรรลุนิติภาวะและมีความเหมาะสมที่จะทำธุรกรรมที่มีผลผูกมัดทางกฎหมายตามข้อกฎหมายที่เกี่ยวข้องได้ หากคุณไม่มีคุณสมบัติเหมาะสม ไม่อนุญาตให้ใช้งานเว็บไซต์",
		},
		{
			icon: "solar:info-circle-bold",
			label: "การแก้ไขเปลี่ยนแปลงข้อตกลง",
			content:
				"ทางผู้ให้บริการขอสงวนสิทธิที่จะแก้ไขหรือเปลี่ยนแปลงข้อตกลงการใช้งานที่ระบุไว้ในเว็บไซต์นี้ ท่านมีหน้าที่ตรวจสอบข้อตกลงการใช้งานเว็บไซต์นี้ รวมถึงข้อกำหนดเพิ่มเติมใดๆ ที่ระบุไว้ในเว็บไซต์ของเราอย่างสม่ำเสมอ",
		},
		{
			icon: "solar:info-circle-bold",
			label: "การใช้และการเปิดเผยข้อมูลส่วนบุคคล",
			content:
				"เว็บไซต์แห่งนี้ อนุญาติให้ผู้ใช้งาน เสพข้อมูล เพิ่มเนื้อหาเองได้โดยอิสระ และทางผู้ให้บริการจะไม่ขอรับผิดชอบกับเนื้อหาที่ผู้ใช้งาน เขียน อัพโหลด หรือเพิ่มเข้ามาในเว็บไซต์แห่งนี้",
		},
		{
			icon: "solar:info-circle-bold",
			label: "การชดใช้ค่าเสียหาย",
			content:
				"ท่านตกลงว่าจะชดใช้ค่าเสียหายให้แก่ทางเราสำหรับข้อเรียกร้อง ความสูญเสีย ค่าใช้จ่าย และความเสียหายใดๆ ที่ทางเราต้องรับภาระหรือเกิดขึ้นแก่การให้บริการเว็บไซต์หรือบริการต่างๆ",
		},
		{
			icon: "solar:info-circle-bold",
			label: "นโยบายคุ้มครองข้อมูลส่วนบุคคล",
			content:
				"ข้อมูลส่วนบุคคลของผู้ใช้งานสำหรับการใช้งานเว็บไซต์นี้ ได้รับความคุ้มครองภายใต้นโยบายคุ้มครองข้อมูลส่วนบุคคล (Privacy Policy)",
		},
		{
			icon: "solar:info-circle-bold",
			label: "นโยบายการรักษาความมั่นคงปลอดภัย",
			content:
				"ทางเราได้เลือกใช้เทคโนโลยี มาตรการรักษาความมั่นคงปลอดภัยในการทำธุรกรรมบนเครือข่ายอินเทอร์เน็ต เพื่อป้องกันข้อมูลของผู้ใช้งาน",
		},
		{
			icon: "solar:info-circle-bold",
			label: "การละเมิดข้อตกลงและเงื่อนไข",
			content:
				"ในกรณีที่มีการละเมิดข้อตกลงและเงื่อนไขการใช้งานเว็บไซต์ของเรา ทางเราขอสงวนสิทธิ์ที่จะระงับการเข้าถึงหรือยกเลิกการให้บริการใด ๆ",
		},
		{
			icon: "solar:info-circle-bold",
			label: "กฎหมายที่บังคับใช้",
			content:
				"ข้อตกลงและเงื่อนไขการการใช้งานเว็บไซต์นี้อยู่ภายใต้การบังคับใช้แห่งกฎหมายไทย",
		},
	];
</script>

<svelte:head>
	<title>สมัครสมาชิก - Dashboard</title>
</svelte:head>

<div class="flex flex-col md:flex-row w-full gap-5">
	<!-- Terms & Conditions -->
	<div class="w-full sm:min-w-sm space-y-5">
		<h2 class="text-lg text-center font-bold">
			ข้อตกลงและเงื่อนไขการใช้งานเว็บไซต์
		</h2>
		<div class="join join-vertical bg-base-100">
			{#each termList as term}
				<div
					class="collapse collapse-arrow join-item border-base-300 border"
				>
					<input type="radio" name="my-accordion-1" />
					<div class="collapse-title font-semibold">{term.label}</div>
					<div class="collapse-content text-sm">{term.content}</div>
				</div>
			{/each}
		</div>
	</div>

	<Card
		centered
		title="สมัครสมาชิก"
		subtitle="สร้างบัญชีใหม่เพื่อเริ่มต้นใช้งาน"
		class="space-y-6"
	>
		<form
			method="POST"
			action="?/signup"
			use:enhance={handleSubmit}
			class="space-y-4"
		>
			<Input
				icon="solar:letter-line-duotone"
				label="อีเมล"
				required
				type="email"
				id="email"
				name="email"
				bind:value={formData.email}
				placeholder="<EMAIL>"
				error={formErrors.email}
				onkeydown={handleKeydown}
			/>

			<Input
				icon="solar:key-minimalistic-square-2-line-duotone"
				label="รหัสผ่าน"
				required
				type="password"
				id="password"
				name="password"
				bind:value={formData.password}
				placeholder="รหัสผ่านของคุณ"
				error={formErrors.password}
				onkeydown={handleKeydown}
			/>

			<Input
				icon="solar:key-square-line-duotone"
				label="ยืนยันรหัสผ่าน"
				required
				type="password"
				id="confirmPassword"
				name="confirmPassword"
				bind:value={formData.confirmPassword}
				placeholder="ยืนยันรหัสผ่าน"
				error={formErrors.confirmPassword}
				onkeydown={handleKeydown}
			/>

			<Checkbox
			id="agreeToTerms"
			name="agreeToTerms"
				label="ฉันยอมรับข้อตกลงและเงื่อนไขการใช้งานเว็บไซต์"
				bind:checked={formData.agreeToTerms}
				required={true}
			/>

	 

			<!-- ✅ Submit button with proper loading state -->
			<Button
				type="submit"
				color="primary"
				size="lg"
				block
				loading={isLoading}
				disabled={isLoading || !isFormValid}
			>
				{isLoading ? "กำลังสมัครสมาชิก..." : "สมัครสมาชิก"}
			</Button>

			<!-- ✅ Debug information for development -->
			{#if import.meta.env.DEV}
				<div class="text-xs text-gray-500 mt-2">
					<div>Form Valid: {isFormValid}</div>
					<div>Loading: {isLoading}</div>
					<div>Accept Terms: {formData.agreeToTerms}</div>
				</div>
			{/if}
		</form>

		<div class="divider">หรือ</div>

		<div class="space-y-3">
			<button class="btn btn-outline w-full">
				<svg class="w-5 h-5 mr-2" viewBox="0 0 24 24">
					<path
						fill="currentColor"
						d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
					/>
					<path
						fill="currentColor"
						d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
					/>
					<path
						fill="currentColor"
						d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
					/>
					<path
						fill="currentColor"
						d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
					/>
				</svg>
				สมัครสมาชิกด้วย Google
			</button>
		</div>

		<div class="text-center mt-6">
			<p class="text-gray-600">
				มีบัญชีอยู่แล้ว?
				<a
					href="/signin"
					class="text-primary hover:underline font-medium"
				>
					เข้าสู่ระบบ
				</a>
			</p>
		</div>
	</Card>
</div>
