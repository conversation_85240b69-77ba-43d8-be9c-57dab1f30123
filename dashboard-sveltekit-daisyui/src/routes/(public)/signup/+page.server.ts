import type { PageServerLoad, Actions } from './$types';
import { fail } from '@sveltejs/kit';
import { signupSchema } from '$lib/schemas/auth';
import { ApiClient } from '$lib/api/client';

export const prerender = false;

const apiClient = new ApiClient();

export const load: PageServerLoad = async () => {
	return {};
};

export const actions: Actions = {
	signup: async ({ request, cookies }) => {
		const formData = await request.formData();
		const email = formData.get('email') as string;
		const password = formData.get('password') as string;
		const confirmPassword = formData.get('confirmPassword') as string;
		const agreeToTerms = formData.get('agreeToTerms') === 'on';

		try {
			const validatedData = signupSchema.parse({
				email,
				password,
				confirmPassword,
				agreeToTerms,
			});

			const result = await apiClient.makePublicRequest<{
				success: boolean;
				message: string;
				data: {
					accessToken: string;
					refreshToken: string;
					user: any;
				};
			}>('/users/signup', {
				method: 'POST',
				body: validatedData,
			});

			// Set cookies
			cookies.set('accessToken', result.data.accessToken, {
				path: '/',
				httpOnly: true,
				secure: process.env.NODE_ENV === 'production',
				sameSite: 'strict',
				maxAge: 60 * 60 * 24 * 7, // 7 days
			});

			cookies.set('refreshToken', result.data.refreshToken, {
				path: '/',
				httpOnly: true,
				secure: process.env.NODE_ENV === 'production',
				sameSite: 'strict',
				maxAge: 60 * 60 * 24 * 30, // 30 days
			});

			return {
				success: true,
				message: 'สมัครสมาชิกสำเร็จ กรุณายืนยันอีเมล',
				user: result.data.user,
			};
		} catch (error) {
			console.error('Signup error:', error);
			
			if (error instanceof Error) {
				return fail(400, {
					error: error.message,
					email,
				});
			}
			
			return fail(400, {
				error: 'ข้อมูลไม่ถูกต้อง',
				email,
			});
		}
	},
}; 