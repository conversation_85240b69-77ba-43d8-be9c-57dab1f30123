<script lang="ts"> 
	import logo from "$lib/assets/images/logo.avif"; 
	import LanguageSwitcher from "$lib/components/LanguageSwitcher.svelte";
	import ThemeSwitcher from "$lib/components/ThemeSwitcher.svelte";
 
	let { children } = $props();
 
</script>
 

<div class="min-h-screen">
	<!-- Public pages with header -->
	<header>
		<div class="flex flex-row gap-3 justify-between items-center p-3">
			<ThemeSwitcher />
			<LanguageSwitcher />
		</div>
		<div class="flex justify-center items-center">
			<a href="/" class="flex justify-center items-center">
				<img
					height="220"
					width="220"
					src={logo}
					alt="logo is1site"
					class="max-w-40 sm:max-w-full mx-auto box-shadow bg-white/60 dark:bg-white/20 backdrop-blur-sm p-5 rounded-lg box-shadow-xl"
				/>
			</a>
		</div>
	</header>

	<!-- Main Content -->
	<main class="max-w-screen-2xl mx-auto p-3 md:p-5">
		{@render children()}
	</main>

	<!-- Footer -->
	<footer class="bg-base-100/90 text-base-content w-full text-center p-3">
		<p>&copy; 2024 Dashboard. สงวนลิขสิทธิ์</p>
	</footer>
</div>
