import type { PageServerLoad, Actions } from './$types';
import { fail } from '@sveltejs/kit';
import { forgotPasswordSchema } from '$lib/schemas/auth';

const API_BASE_URL = 'http://localhost:5000/v1';

export const load: PageServerLoad = async () => {
	return {};
};

export const actions: Actions = {
	forgotPassword: async ({ request }) => {
		const formData = await request.formData();
		const email = formData.get('email') as string;

		try {
			const validatedData = forgotPasswordSchema.parse({ email });

			const response = await fetch(`${API_BASE_URL}/users/forgot-password`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(validatedData),
			});

			const result = await response.json();

			if (!response.ok) {
				return fail(400, {
					error: result.message || 'เกิดข้อผิดพลาดในการส่งอีเมล',
					email,
				});
			}

			return {
				success: true,
				message: 'ส่งอีเมลรีเซ็ตรหัสผ่านแล้ว',
			};
		} catch (error) {
			return fail(400, {
				error: 'อีเมลไม่ถูกต้อง',
				email,
			});
		}
	},
}; 