<script lang="ts">
	import { enhance } from "$app/forms";

	let { form } = $props<{ form: any }>();

	let email = $state("");
	let isLoading = $state(false);

	function handleSubmit() {
		isLoading = true;
	}
</script>

<svelte:head>
	<title>ลืมรหัสผ่าน - Dashboard</title>
</svelte:head>

<div class="space-y-6">
	<div class="text-center">
		<h2 class="text-2xl font-bold text-gray-900">ลืมรหัสผ่าน</h2>
		<p class="text-gray-600 mt-2">
			กรุณากรอกอีเมลเพื่อรับลิงก์รีเซ็ตรหัสผ่าน
		</p>
	</div>

	{#if form?.error}
		<div class="alert alert-error">
			<svg
				class="w-5 h-5"
				fill="none"
				stroke="currentColor"
				viewBox="0 0 24 24"
			>
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
				></path>
			</svg>
			<span>{form.error}</span>
		</div>
	{/if}

	{#if form?.success}
		<div class="alert alert-success">
			<svg
				class="w-5 h-5"
				fill="none"
				stroke="currentColor"
				viewBox="0 0 24 24"
			>
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
				></path>
			</svg>
			<span>{form.message}</span>
		</div>
	{/if}

	<form
		method="POST"
		action="?/forgotPassword"
		use:enhance={handleSubmit}
		class="space-y-4"
	>
		<div>
			<label
				for="email"
				class="block text-sm font-medium text-gray-700 mb-2"
			>
				อีเมล
			</label>
			<input
				type="email"
				id="email"
				name="email"
				bind:value={email}
				required
				class="input input-bordered w-full"
				placeholder="<EMAIL>"
			/>
		</div>

		<button
			type="submit"
			disabled={isLoading}
			class="btn btn-primary w-full"
		>
			{#if isLoading}
				<span class="loading loading-spinner loading-sm"></span>
				กำลังส่งอีเมล...
			{:else}
				ส่งลิงก์รีเซ็ตรหัสผ่าน
			{/if}
		</button>
	</form>

	<div class="text-center mt-6">
		<p class="text-gray-600">
			จำรหัสผ่านได้แล้ว?
			<a href="/signin" class="text-primary hover:underline font-medium">
				เข้าสู่ระบบ
			</a>
		</p>
	</div>
</div>
