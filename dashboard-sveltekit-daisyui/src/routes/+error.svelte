<script lang="ts">
	const { status, error } = $props();
	
	// กำหนด error type ตาม status
	const getErrorType = (status: number) => {
		if (status >= 500) return 'server';
		if (status >= 400) return 'client';
		return 'general';
	};
	
	const errorType = status ? getErrorType(status) : 'general';
	
	// ข้อมูลตาม error type
	const errorConfig = {
		server: {
			title: 'เซิร์ฟเวอร์มีปัญหา',
			description: 'เซิร์ฟเวอร์กำลังประสบปัญหา กรุณาลองใหม่อีกครั้งในภายหลัง',
			icon: 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z',
			color: 'red'
		},
		client: {
			title: 'ไม่พบหน้าที่ต้องการ',
			description: 'หน้าที่คุณกำลังค้นหาอาจถูกลบหรือย้ายไปที่อื่น',
			icon: 'M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.47-.881-6.08-2.33',
			color: 'amber'
		},
		general: {
			title: 'เกิดข้อผิดพลาด',
			description: 'เกิดข้อผิดพลาดที่ไม่คาดคิด กรุณาลองใหม่อีกครั้ง',
			icon: 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z',
			color: 'blue'
		}
	};
	
	const config = errorConfig[errorType];
</script>

<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 flex items-center justify-center p-4 relative overflow-hidden">
	<!-- Background decoration -->
	<div class="absolute inset-0 overflow-hidden">
		<div class="absolute -top-40 -right-40 w-80 h-80 bg-blue-200 dark:bg-blue-900/20 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
		<div class="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-200 dark:bg-purple-900/20 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
		<div class="absolute top-40 left-40 w-80 h-80 bg-pink-200 dark:bg-pink-900/20 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
	</div>

	<div class="relative max-w-lg w-full bg-white/80 dark:bg-slate-800/80 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 dark:border-slate-700/50 p-8 text-center transform transition-all duration-500 hover:scale-105">
		<!-- Error Icon -->
		<div class="mb-8">
			<div class="w-20 h-20 bg-gradient-to-br from-{config.color}-100 to-{config.color}-200 dark:from-{config.color}-900/30 dark:to-{config.color}-800/30 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg border-4 border-white/50 dark:border-slate-700/50">
				<svg class="w-10 h-10 text-{config.color}-600 dark:text-{config.color}-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="{config.icon}"></path>
				</svg>
			</div>
			
			<!-- Error Title -->
			<h1 class="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 dark:from-white dark:to-slate-300 bg-clip-text text-transparent mb-4">
				{config.title}
			</h1>
			
			<!-- Error Description -->
			<p class="text-slate-600 dark:text-slate-400 text-lg leading-relaxed mb-6">
				{#if error?.message}
					{error.message}
				{:else}
					{config.description}
				{/if}
			</p>
			
			<!-- Status Code -->
			{#if status}
				<div class="inline-flex items-center gap-2 bg-slate-100 dark:bg-slate-700 px-4 py-2 rounded-full">
					<span class="text-sm font-medium text-slate-600 dark:text-slate-400">รหัสข้อผิดพลาด:</span>
					<span class="text-sm font-bold text-{config.color}-600 dark:text-{config.color}-400 bg-{config.color}-100 dark:bg-{config.color}-900/30 px-2 py-1 rounded-md">
						{status}
					</span>
				</div>
			{/if}
		</div>

		<!-- Action Buttons -->
		<div class="space-y-4">
			<button
				onclick={() => window.location.reload()}
				class="w-full bg-base-100 hover:bg-base-200 text-base-content py-3 px-6 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-lg active:scale-95 focus:outline-none focus:ring-4 focus:ring-primary/30 border border-base-300"
			>
				<svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
				</svg>
				ลองใหม่อีกครั้ง
			</button>

			<button
				onclick={() => window.history.back()}
				class="w-full bg-base-200 hover:bg-base-300 text-base-content py-3 px-6 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-lg active:scale-95 focus:outline-none focus:ring-4 focus:ring-secondary/30 border border-base-300"
			>
				<svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
				</svg>
				ย้อนกลับ
			</button>

			<a
				href="/"
				class="block w-full bg-base-300 hover:bg-base-200 text-base-content py-3 px-6 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-lg active:scale-95 focus:outline-none focus:ring-4 focus:ring-accent/30 border border-base-300"
			>
				<svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
				</svg>
				กลับหน้าหลัก
			</a>
		</div>

		<!-- Help Section -->
		<div class="mt-8 pt-6 border-t border-slate-200 dark:border-slate-700">
			<div class="flex items-center justify-center gap-2 text-slate-500 dark:text-slate-400 mb-3">
				<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
				</svg>
				<span class="text-sm font-medium">ต้องการความช่วยเหลือ?</span>
			</div>
			<div class="flex flex-wrap gap-2 justify-center">
				<a href="/help" class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors">
					หน้าความช่วยเหลือ
				</a>
				<span class="text-slate-400">•</span>
				<a href="mailto:<EMAIL>" class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors">
					ติดต่อผู้ดูแลระบบ
				</a>
			</div>
		</div>
	</div>
</div>

<style>
	@keyframes blob {
		0% {
			transform: translate(0px, 0px) scale(1);
		}
		33% {
			transform: translate(30px, -50px) scale(1.1);
		}
		66% {
			transform: translate(-20px, 20px) scale(0.9);
		}
		100% {
			transform: translate(0px, 0px) scale(1);
		}
	}
	
	.animate-blob {
		animation: blob 7s infinite;
	}
	
	.animation-delay-2000 {
		animation-delay: 2s;
	}
	
	.animation-delay-4000 {
		animation-delay: 4s;
	}
</style>