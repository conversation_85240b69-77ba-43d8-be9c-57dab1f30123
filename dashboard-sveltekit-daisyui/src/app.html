<!doctype html>
<html lang="%paraglide.lang%" data-theme="light">
	<head>
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<script>
			// ตั้งค่า theme ทันทีก่อน DOM โหลด
			(function() {
				try {
					const savedTheme = localStorage.getItem('theme') || 'light';
					document.documentElement.setAttribute('data-theme', savedTheme);
					// ตั้งค่า color-scheme ด้วย
					document.documentElement.style.colorScheme = savedTheme;
				} catch (e) {
					document.documentElement.setAttribute('data-theme', 'light');
					document.documentElement.style.colorScheme = 'light';
				}
			})();
		</script>
		%sveltekit.head%
	</head>
	<body data-sveltekit-preload-data="hover">
		<div style="display: contents">%sveltekit.body%</div>
	</body>
</html>
