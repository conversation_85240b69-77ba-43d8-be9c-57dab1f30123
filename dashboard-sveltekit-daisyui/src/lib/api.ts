import { handleApiError } from '@/lib/utils/auth';
import { browser } from '$app/environment';

const API_BASE_URL = 'http://localhost:5000/v1';

interface ApiResponse<T = any> {
    success: boolean;
    message?: string;
    data?: T;
    error?: string;
}

class ApiClient {
    private getAuthHeaders(): HeadersInit {
        const headers: HeadersInit = {
            'Content-Type': 'application/json',
        };

        // Add Authorization header if token exists
        if (browser) {
            const token = localStorage.getItem('accessToken');
            if (token) {
                headers['Authorization'] = `Bearer ${token}`;
            }
        }

        return headers;
    }

    async request<T>(endpoint: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
        try {
            const response = await fetch(`${API_BASE_URL}${endpoint}`, {
                ...options,
                headers: {
                    ...this.getAuthHeaders(),
                    ...options.headers,
                },
                credentials: 'include', // Include cookies
            });

            const data = await response.json();

            if (!response.ok) {
                // Handle token refresh for 401 errors
                if (response.status === 401) {
                    const retrySuccess = await handleApiError({ message: '401' });
                    if (retrySuccess) {
                        // Retry the request after token refresh
                        return this.request(endpoint, options);
                    }
                }
                throw new Error(data.message || 'เกิดข้อผิดพลาด');
            }

            return data;
        } catch (error) {
            console.error('API Error:', error);
            throw error;
        }
    }

    // Auth methods
    async signin(email: string, password: string) {
        return this.request('/users/signin', {
            method: 'POST',
            body: JSON.stringify({ email, password }),
        });
    }

    async getProfile() {
        return this.request('/users/me');
    }

    // Admin methods
    async getDashboardStats() {
        return this.request('/admin/dashboard');
    }

    async getUsers(page = 1, limit = 10, search = '') {
        const params = new URLSearchParams({
            page: page.toString(),
            limit: limit.toString(),
            ...(search && { search }),
        });
        return this.request(`/admin/users?${params}`);
    }

    async getCustomers(page = 1, limit = 10, search = '') {
        const params = new URLSearchParams({
            page: page.toString(),
            limit: limit.toString(),
            ...(search && { search }),
        });
        return this.request(`/admin/customers?${params}`);
    }

    async toggleUserStatus(userId: string) {
        return this.request(`/admin/users/${userId}/toggle-status`, {
            method: 'PUT',
        });
    }

    async toggleCustomerStatus(customerId: string) {
        return this.request(`/admin/customers/${customerId}/toggle-status`, {
            method: 'PUT',
        });
    }

    async updateUser(userId: string, data: any) {
        return this.request(`/admin/users/${userId}`, {
            method: 'PUT',
            body: JSON.stringify(data),
        });
    }

    async updateCustomer(customerId: string, data: any) {
        return this.request(`/admin/customers/${customerId}`, {
            method: 'PUT',
            body: JSON.stringify(data),
        });
    }

    async deleteUser(userId: string) {
        return this.request(`/admin/users/${userId}`, {
            method: 'DELETE',
        });
    }

    async deleteCustomer(customerId: string) {
        return this.request(`/admin/customers/${customerId}`, {
            method: 'DELETE',
        });
    }

    // Site management methods
    async getSites() {
        return this.request('/sites');
    }

    async getSite(siteId: string) {
        return this.request(`/sites/${siteId}`);
    }

    async createSite(data: any) {
        return this.request('/sites', {
            method: 'POST',
            body: JSON.stringify(data),
        });
    }

    async updateSite(siteId: string, data: any) {
        return this.request(`/sites/${siteId}`, {
            method: 'PUT',
            body: JSON.stringify(data),
        });
    }

    async deleteSite(siteId: string) {
        return this.request(`/sites/${siteId}`, {
            method: 'DELETE',
        });
    }

    async getSiteStats(siteId: string) {
        return this.request(`/sites/${siteId}/stats`);
    }

    async getSiteAnalytics(siteId: string, period = '7d') {
        return this.request(`/sites/${siteId}/analytics?period=${period}`);
    }

    async getSitePages(siteId: string) {
        return this.request(`/sites/${siteId}/pages`);
    }

    async getSiteContent(siteId: string) {
        return this.request(`/sites/${siteId}/content`);
    }

    async getSiteMedia(siteId: string) {
        return this.request(`/sites/${siteId}/media`);
    }

    async getSiteSettings(siteId: string) {
        return this.request(`/sites/${siteId}/settings`);
    }

    async updateSiteSettings(siteId: string, settings: any) {
        return this.request(`/sites/${siteId}/settings`, {
            method: 'PUT',
            body: JSON.stringify(settings),
        });
    }
}

export const api = new ApiClient();