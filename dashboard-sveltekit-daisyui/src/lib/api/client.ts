import { PUBLIC_API_BASE_URL } from '$env/static/public';

export interface ApiClientOptions {
	method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
	headers?: Record<string, string>;
	body?: any;
}

export interface ApiResponse<T = any> {
	data: T;
	message: string;
	success: boolean;
}

const API_BASE_URL = PUBLIC_API_BASE_URL || 'http://localhost:5000/v1';

function createAuthHeaders(token: string): Record<string, string> {
	return {
		'Authorization': `Bearer ${token}`,
		'Content-Type': 'application/json',
	};
}

export class ApiClient {
	protected async request<T>(
		endpoint: string,
		options: ApiClientOptions = {},
	): Promise<T> {
		const url = `${API_BASE_URL}${endpoint}`;
		const { method = 'GET', headers = {}, body } = options;

		const response = await fetch(url, {
			method,
			headers: {
				'Content-Type': 'application/json',
				...headers,
			},
			body: body ? JSON.stringify(body) : undefined,
		});

		const result = await response.json();

		if (!response.ok) {
			throw new Error(result.message || 'Request failed');
		}

		return result;
	}

	/**
	 * Create authenticated API call
	 */
	public async makeAuthenticatedRequest<T>(
		endpoint: string,
		token: string,
		options: Omit<ApiClientOptions, 'headers'> & {
			headers?: Record<string, string>;
		} = {},
	): Promise<T> {
		return this.request<T>(endpoint, {
			...options,
			headers: {
				...createAuthHeaders(token),
				...options.headers,
			},
		});
	}

	/**
	 * Create public API call (no auth required)
	 */
	public async makePublicRequest<T>(
		endpoint: string,
		options: ApiClientOptions = {},
	): Promise<T> {
		return this.request<T>(endpoint, options);
	}
} 