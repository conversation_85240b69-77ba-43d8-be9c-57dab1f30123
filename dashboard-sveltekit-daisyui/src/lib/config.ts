// ✅ Client-side configuration
export const clientConfig = {
    // App settings
    appName: 'WebShop',
    appVersion: '1.0.0',


    // UI settings
    ui: {
        defaultTheme: 'light',
        defaultLanguage: 'th',
        animationDuration: 300,
    },

    // Validation settings
    validation: {
        passwordMinLength: 6,
        emailRegex: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    },
} as const;



// ✅ API configuration - ใช้ได้ทั้ง client และ server
export const backendApiUrl = import.meta.env.VITE_BACKEND_API_URL + import.meta.env.VITE_BACKEND_VERSION
    || 'http://localhost:5000/v1';

// ✅ Environment configuration
export const env = {
    isDev: import.meta.env.DEV,
    isProd: import.meta.env.PROD,
    isTest: import.meta.env.MODE === 'test',
} as const;


// ✅ Type exports
export type ClientConfig = typeof clientConfig;
export type Env = typeof env;