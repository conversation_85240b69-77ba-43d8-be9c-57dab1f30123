import { writable } from 'svelte/store';
import { browser } from '$app/environment';
import { ApiClient } from '$lib/api/client';

const apiClient = new ApiClient();

export interface User {
	_id: string;
	email: string;
	firstName?: string;
	lastName?: string;
	avatar?: string;
	cover?: string;
	isEmailVerified: boolean;
	moneyPoint: number;
	goldPoint: number;
	role?: 'admin' | 'user' | 'moderator';
	status?: 'active' | 'inactive';
	createdAt: string;
	updatedAt: string;
}

export interface SigninData {
	email: string;
	password: string;
	rememberMe?: boolean;
}

export interface SignupData {
	email: string;
	password: string;
	confirmPassword: string;
}

interface AuthState {
	user: User | null;
	isAuthenticated: boolean;
	isLoading: boolean;
}

const initialState: AuthState = {
	user: null,
	isAuthenticated: false,
	isLoading: false,
};

// Cache keys
const USER_CACHE_KEY = 'user_cache';
const CACHE_TIMESTAMP_KEY = 'user_cache_timestamp';
const CACHE_DURATION = 5 * 60 * 1000; // 5 นาที

// Helper functions for cache
function saveUserToCache(user: User) {
	if (browser) {
		localStorage.setItem(USER_CACHE_KEY, JSON.stringify(user));
		localStorage.setItem(CACHE_TIMESTAMP_KEY, Date.now().toString());
	}
}

function getUserFromCache(): User | null {
	if (!browser) return null;
	
	try {
		const cachedUser = localStorage.getItem(USER_CACHE_KEY);
		const timestamp = localStorage.getItem(CACHE_TIMESTAMP_KEY);
		
		if (!cachedUser || !timestamp) return null;
		
		const cacheAge = Date.now() - parseInt(timestamp);
		if (cacheAge > CACHE_DURATION) {
			// Cache หมดอายุ ลบออก
			localStorage.removeItem(USER_CACHE_KEY);
			localStorage.removeItem(CACHE_TIMESTAMP_KEY);
			return null;
		}
		
		return JSON.parse(cachedUser);
	} catch (error) {
		console.error('Error reading user cache:', error);
		return null;
	}
}

function clearUserCache() {
	if (browser) {
		localStorage.removeItem(USER_CACHE_KEY);
		localStorage.removeItem(CACHE_TIMESTAMP_KEY);
	}
}

function createAuthStore() {
	const { subscribe, set, update } = writable<AuthState>(initialState);

	return {
		subscribe,
		signin: (user: User) => {
			// บันทึก user ลง cache
			saveUserToCache(user);
			
			set({
				user,
				isAuthenticated: true,
				isLoading: false,
			});
		},
		signout: async () => {
			// Call backend API to signout
			try {
				// Get token from localStorage
				const token = browser ? localStorage.getItem('accessToken') : null;

				if (token) {
					await apiClient.makeAuthenticatedRequest('/users/me/signout', token, {
						method: 'POST'
					});
				}
			} catch (error) {
				// Ignore errors during signout
				console.error('Signout API error:', error);
			}

			// Clear cache and tokens
			clearUserCache();
			if (browser) {
				localStorage.removeItem('accessToken');
				localStorage.removeItem('refreshToken');
				window.location.href = '/signout';
			}

			set({
				user: null,
				isAuthenticated: false,
				isLoading: false,
			});
		},
		setUser: (user: User) => {
			// บันทึก user ลง cache
			saveUserToCache(user);
			
			update(state => ({
				...state,
				user,
				isAuthenticated: true,
				isLoading: false,
			}));
		},
		setLoading: (isLoading: boolean) => {
			update(state => ({
				...state,
				isLoading,
			}));
		},
		// ฟังก์ชันใหม่สำหรับโหลด user จาก cache
		loadUserFromCache: () => {
			const cachedUser = getUserFromCache();
			if (cachedUser) {
				set({
					user: cachedUser,
					isAuthenticated: true,
					isLoading: false,
				});
				return true; // มี cache
			}
			return false; // ไม่มี cache
		},
		// ฟังก์ชันสำหรับอัปเดต cache
		updateUserCache: (user: User) => {
			saveUserToCache(user);
			update(state => ({
				...state,
				user,
				isAuthenticated: true,
				isLoading: false,
			}));
		},
	};
}

export const authStore = createAuthStore();