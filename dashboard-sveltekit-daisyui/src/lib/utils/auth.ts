import { browser } from '$app/environment';

const API_BASE_URL = 'http://localhost:5000/v1';

export async function refreshToken(): Promise<boolean> {
	if (!browser) return false;

	try {
		const refreshToken = localStorage.getItem('refreshToken');
		if (!refreshToken) {
			throw new Error('ไม่พบ refresh token');
		}

		const response = await fetch(`${API_BASE_URL}/users/refresh-token`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({ refreshToken }),
		});

		const result = await response.json();

		if (!response.ok) {
			throw new Error(result.message || 'เกิดข้อผิดพลาดในการต่ออายุ token');
		}

		// Store new tokens
		if (result.data?.accessToken) {
			localStorage.setItem('accessToken', result.data.accessToken);
		}
		if (result.data?.refreshToken) {
			localStorage.setItem('refreshToken', result.data.refreshToken);
		}

		return true;
	} catch (error) {
		console.error('Token refresh failed:', error);
		return false;
	}
}

export async function handleApiError(error: any): Promise<boolean> {
	if (error.message?.includes('401') || error.message?.includes('Unauthorized')) {
		const refreshed = await refreshToken();
		if (!refreshed) {
			// Redirect to login if refresh fails
			window.location.href = '/signin';
			return false;
		}
		return true;
	}
	return false;
} 