export enum LogCategory {
	AUTH = 'auth',
	USER = 'user',
	PRODUCT = 'product',
	ORDER = 'order',
	SYSTEM = 'system',
}

export enum LogLevel {
	INFO = 'info',
	WARN = 'warn',
	ERROR = 'error',
	DEBUG = 'debug',
}

interface LogData {
	[key: string]: any;
}

class Logger {
	private isDevelopment = typeof window !== 'undefined' && window.location.hostname === 'localhost';

	info(category: LogCategory, action: string, message: string, data?: LogData) {
		this.log(LogLevel.INFO, category, action, message, data);
	}

	warn(category: LogCategory, action: string, message: string, data?: LogData) {
		this.log(LogLevel.WARN, category, action, message, data);
	}

	error(category: LogCategory, action: string, message: string, data?: LogData) {
		this.log(LogLevel.ERROR, category, action, message, data);
	}

	debug(category: LogCategory, action: string, message: string, data?: LogData) {
		if (this.isDevelopment) {
			this.log(LogLevel.DEBUG, category, action, message, data);
		}
	}

	private log(level: LogLevel, category: LogCategory, action: string, message: string, data?: LogData) {
		const timestamp = new Date().toISOString();
		const logEntry = {
			timestamp,
			level,
			category,
			action,
			message,
			data,
		};

		if (this.isDevelopment) {
			console.log(`[${level.toUpperCase()}] ${category}/${action}: ${message}`, data || '');
		}

		// In production, you might want to send logs to a service
		// await fetch('/api/logs', { method: 'POST', body: JSON.stringify(logEntry) });
	}
}

export const logger = new Logger(); 