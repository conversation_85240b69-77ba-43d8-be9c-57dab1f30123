import { browser } from '$app/environment';
import Swal from 'sweetalert2-neutral';

// สร้าง Swal instance เฉพาะใน browser
let swal: typeof Swal | null = null;

if (browser) {
  swal = Swal;
}

// Default configurations
const defaultConfig = {
  confirmButtonText: 'ตกลง',
  cancelButtonText: 'ยกเลิก',
  reverseButtons: true,
  allowEscapeKey: true,
  allowOutsideClick: true,
};

const toastConfig = {
  toast: true,
  position: 'top-end' as const,
  showConfirmButton: false,
  timer: 3000,
  timerProgressBar: true,
  allowEscapeKey: true,
  showCloseButton: true,
  didOpen: (toast: any) => {
    toast.addEventListener('mouseenter', Swal.stopTimer);
    toast.addEventListener('mouseleave', Swal.resumeTimer);
  },
};

// Success alert
export const showSuccess = (title: string, message?: string, options: any = {}) => {
  if (!browser || !swal) return Promise.resolve();

  return swal.fire({
    icon: 'success',
    title,
    text: message,
    confirmButtonColor: '#10b981',
    ...defaultConfig,
    ...options,
  });
};

// Error alert
export const showError = (title: string, message?: string, options: any = {}) => {
  if (!browser || !swal) return Promise.resolve();

  return swal.fire({
    icon: 'error',
    title,
    text: message,
    confirmButtonColor: '#ef4444',
    ...defaultConfig,
    ...options,
  });
};

// Warning alert
export const showWarning = (title: string, message?: string, options: any = {}) => {
  if (!browser || !swal) return Promise.resolve();

  return swal.fire({
    icon: 'warning',
    title,
    text: message,
    confirmButtonColor: '#f59e0b',
    ...defaultConfig,
    ...options,
  });
};

// Info alert
export const showInfo = (title: string, message?: string, options: any = {}) => {
  if (!browser || !swal) return Promise.resolve();

  return swal.fire({
    icon: 'info',
    title,
    text: message,
    confirmButtonColor: '#3b82f6',
    ...defaultConfig,
    ...options,
  });
};

// Confirmation dialog
export const showConfirm = (title: string, message: string, options: any = {}) => {
  if (!browser || !swal) return Promise.resolve();

  return swal.fire({
    icon: 'question',
    title,
    text: message,
    showCancelButton: true,
    confirmButtonColor: '#10b981',
    cancelButtonColor: '#6b7280',
    ...defaultConfig,
    ...options,
  });
};

// Loading alert
export const showLoading = (title: string = 'กำลังโหลด...', options: any = {}) => {
  if (!browser || !swal) return Promise.resolve();

  return swal.fire({
    title,
    allowOutsideClick: false,
    didOpen: () => {
      swal!.showLoading();
    },
    ...options,
  });
};

// Close current alert
export const closeAlert = () => {
  if (browser && swal) {
    swal.close();
  }
};

// Custom alert with options
export const showCustom = (options: any) => {
  if (!browser || !swal) return Promise.resolve();

  return swal.fire({
    confirmButtonColor: '#10b981',
    ...defaultConfig,
    ...options,
  });
};

// Toast notification
export const showToast = (
  type: 'success' | 'error' | 'info' | 'warning' = 'success',
  title: string,
  options: any = {},
) => {
  if (!browser || !swal) return Promise.resolve();

  return swal.fire({
    ...toastConfig,
    icon: type,
    title,
    ...options,
  });
};

// Toast functions (shorthand)
export const showSuccessToast = (message: string, options: any = {}) => {
  return showToast('success', message, options);
};

export const showErrorToast = (message: string, options: any = {}) => {
  return showToast('error', message, options);
};

export const showWarningToast = (message: string, options: any = {}) => {
  return showToast('warning', message, options);
};

export const showInfoToast = (message: string, options: any = {}) => {
  return showToast('info', message, options);
};
 
// Export swal instance สำหรับการใช้งานโดยตรง
export { swal as Swal };