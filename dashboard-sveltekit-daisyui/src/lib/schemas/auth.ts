import { z } from 'zod';

export const signinSchema = z.object({
	email: z.string().email('อีเมลไม่ถูกต้อง'),
	password: z.string().min(6, 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร'),
	rememberMe: z.boolean().optional().default(false),
});

export const signupSchema = z.object({
	email: z.string().email('อีเมลไม่ถูกต้อง'),
	password: z.string().min(6, 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร'),
	confirmPassword: z.string().min(1, 'ยืนยันรหัสผ่านต้องไม่ว่าง'),
	agreeToTerms: z.boolean().refine((val) => val, {
		message: 'ต้องยอมรับข้อตกลงและเงื่อนไขการใช้งานเว็บไซต์',
	}),
});

export const forgotPasswordSchema = z.object({
	email: z.string().email('อีเมลไม่ถูกต้อง'),
});

export const resetPasswordSchema = z.object({
	token: z.string().min(1, 'Token ต้องไม่ว่าง'),
	password: z.string().min(6, 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร'),
});

export const verifyEmailSchema = z.object({
	token: z.string().min(1, 'Token ต้องไม่ว่าง'),
});

export const updateProfileSchema = z.object({
	firstName: z.string().min(1, 'ชื่อต้องไม่ว่าง').optional(),
	lastName: z.string().min(1, 'นามสกุลต้องไม่ว่าง').optional(),
	phone: z.string().optional(),
	avatar: z.string().optional(),
});

export const changePasswordSchema = z.object({
	currentPassword: z.string().min(1, 'รหัสผ่านปัจจุบันต้องไม่ว่าง'),
	newPassword: z.string().min(6, 'รหัสผ่านใหม่ต้องมีอย่างน้อย 6 ตัวอักษร'),
});

export type SigninForm = z.infer<typeof signinSchema>;
export type SignupForm = z.infer<typeof signupSchema>;
export type ForgotPasswordForm = z.infer<typeof forgotPasswordSchema>;
export type ResetPasswordForm = z.infer<typeof resetPasswordSchema>;
export type VerifyEmailForm = z.infer<typeof verifyEmailSchema>;
export type UpdateProfileForm = z.infer<typeof updateProfileSchema>;
export type ChangePasswordForm = z.infer<typeof changePasswordSchema>; 