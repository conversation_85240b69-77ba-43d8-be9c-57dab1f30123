<script lang="ts">
    import Icon from "@iconify/svelte";
    import { page } from "$app/state";
    import { untrack } from "svelte";
    import Image from "$lib/components/ui/Image.svelte";

    const { data } = $props();
    const siteId = $derived(page.params.siteId);
    const siteData = $derived(data?.site || page.data.site);

    $effect(() => {
        console.log("🔍 Sidebar data prop:", data);
        console.log("🔍 Site data in Sidebar:", siteData);
        console.log("🔍 Page data:", page.data);
    });

    // อัปเดต site store เมื่อมีข้อมูลใหม่ (ป้องกัน infinite loop)
    // $effect(() => {
    //     if (siteData && siteData._id && siteData._id !== siteStore.site?._id) {
    //         console.log("🔄 Updating site store with new data:", siteData._id);
    //         // ใช้ untrack เพื่อป้องกัน infinite loop
    //         untrack(() => {
    //             siteStore.setSite(siteData, false); // ไม่ broadcast เพื่อป้องกัน loop
    //         });
    //     }
    // });

    interface NavItem {
        href: string;
        icon: string;
        label: string;
        adminOnly?: boolean;
    }

    interface NavGroup {
        id: string;
        label: string;
        icon: string;
        href?: string; // สำหรับเมนูลิงก์โดยตรง
        items?: NavItem[]; // สำหรับเมนูที่มีเมนูย่อย
        adminOnly?: boolean;
    }

    // จัดกลุ่มเมนูเป็นหมวดหมู่ตามโครงสร้างที่สร้างไว้
    const navGroups = $derived([
        {
            id: "dashboard",
            href: `/dashboard/${siteId}`,
            icon: "solar:card-2-line-duotone",
            label: "ภาพรวม",
        },
        {
            id: "analytics",
            label: "การวิเคราะห์",
            icon: "solar:pie-chart-line-duotone",
            href: `/dashboard/${siteId}/analytics`,
        },
        {
            id: "products",
            label: "สินค้า",
            icon: "solar:box-line-duotone",
            href: `/dashboard/${siteId}/products`,
        },
        {
            id: "categories",
            label: "หมวดหมู่",
            icon: "solar:folder-with-files-line-duotone",
            href: `/dashboard/${siteId}/categories`,
        },
        {
            id: "brands",
            label: "แบรนด์",
            icon: "solar:tag-line-duotone",
            href: `/dashboard/${siteId}/brands`,
        },
        {
            id: "reviews",
            label: "รีวิวสินค้า",
            icon: "solar:hand-stars-line-duotone",
            href: `/dashboard/${siteId}/reviews`,
        },
        {
            id: "orders",
            label: "คำสั่งซื้อ",
            icon: "solar:cart-large-line-duotone",
            items: [
                {
                    href: `/dashboard/${siteId}/orders`,
                    icon: "solar:cart-large-line-duotone",
                    label: "คำสั่งซื้อทั้งหมด",
                },
                {
                    href: `/dashboard/${siteId}/orders/preorders`,
                    icon: "solar:call-chat-line-duotone",
                    label: "Pre-order",
                },
                {
                    href: `/dashboard/${siteId}/orders/shipping`,
                    icon: "solar:delivery-line-duotone",
                    label: "การจัดส่ง",
                },
            ],
        },
        {
            id: "customers",
            label: "ลูกค้า",
            icon: "solar:user-hand-up-line-duotone",
            href: `/dashboard/${siteId}/customers`,
        },
        {
            id: "addons",
            label: "ระบบเสริม",
            icon: "solar:plug-circle-line-duotone",
            href: `/dashboard/${siteId}/addons`,
        },

        // {
        // 	id: 'contents',
        // 	label: 'เนื้อหา',
        // 	items: [
        // 		{
        // 			href: `/dashboard/${siteId}/contents/news`,
        // 			 icon: 'solar:document-text-line-duotone',
        // 			label: 'ข่าวสาร',
        // 		},
        // 		{
        // 			href: `/dashboard/${siteId}/contents/blogs`,
        // 			 icon: 'solar:pen-new-square-line-duotone',
        // 			label: 'บล็อก',
        // 		},
        // 		{
        // 			href: `/dashboard/${siteId}/contents/novels`,
        // 			 icon: 'solar:book-line-duotone',
        // 			label: 'นิยาย',
        // 		},
        // 	],
        // },

        {
            id: "manages",
            label: "การจัดการ",
            icon: "solar:settings-line-duotone",
            items: [
                // {
                // 	id: 'marketing',
                // 	label: 'การตลาด',
                // 	 icon: 'solar:shop-line-duotone',
                // 	href: `/dashboard/${siteId}/manages/marketing`,
                // },
                {
                    id: "team",
                    label: "ทีมงาน",
                    icon: "solar:users-group-two-rounded-line-duotone",
                    href: `/dashboard/${siteId}/manages/team`,
                },
            ],
        },
        {
            id: "settings",
            label: "การตั้งค่า",
            icon: "solar:tuning-square-line-duotone",
            items: [
                {
                    href: `/dashboard/${siteId}/settings`,
                    icon: "solar:settings-line-duotone",
                    label: "ตั้งค่าทั่วไป",
                },
                {
                    href: `/dashboard/${siteId}/settings/themes`,
                    icon: "solar:palette-line-duotone",
                    label: "ธีมและสไตล์",
                },
                {
                    href: `/dashboard/${siteId}/settings/subscriptions`,
                    icon: "solar:alarm-play-line-duotone",
                    label: "การสมัครใช้งาน",
                },
            ],
        },
    ] as NavGroup[]);

    // เก็บสถานะการเปิด/ปิดของแต่ละกลุ่ม
    let expandedGroups = $state(new Set(["dashboard"])); // เปิด dashboard เป็นค่าเริ่มต้น

    const filteredNavGroups = $derived(
        navGroups
            .filter((group) => {
                if (group.adminOnly && !authStore.isAdmin) return false;

                // ถ้าไม่มี items (เช่น dashboard) ให้แสดงเลย
                if (!group.items) return true;

                // กรอง items ในกลุ่มด้วย
                const filteredItems = group.items.filter((item) => {
                    if (item.adminOnly && !authStore.isAdmin) return false;
                    return true;
                });

                // ถ้าไม่มี items ที่เหลือ ให้ซ่อนกลุ่มนี้
                return filteredItems.length > 0;
            })
            .map((group) => ({
                ...group,
                items: group.items
                    ? group.items.filter((item) => {
                          if (item.adminOnly && !authStore.isAdmin)
                              return false;
                          return true;
                      })
                    : [],
            })),
    );

    const currentPath = $derived(page.url.pathname);

    function isActive(href: string): boolean {
        // กรณีพิเศษสำหรับ dashboard หลัก
        if (href === `/dashboard/${siteId}`) {
            return (
                currentPath === `/dashboard/${siteId}` ||
                currentPath === `/dashboard/${siteId}/`
            );
        }

        // กรณีอื่นๆ ตรวจสอบว่า path ตรงกันหรือไม่
        return currentPath === href;
    }

    function isGroupExpanded(groupId: string): boolean {
        return expandedGroups.has(groupId);
    }

    // ตรวจสอบว่ากลุ่มไหนมี active item
    function hasActiveItem(items?: NavItem[]): boolean {
        if (!items) return false;
        return items.some((item) => isActive(item.href));
    }

    // เปิดกลุ่มอัตโนมัติเมื่อมี active item
    $effect(() => {
        const activeGroup = filteredNavGroups.find(
            (group) => group.items && hasActiveItem(group.items),
        );
        if (activeGroup && !expandedGroups.has(activeGroup.id)) {
            expandedGroups.add(activeGroup.id);
            expandedGroups = new Set(expandedGroups);
        }
    });
</script>

<div class="drawer-side">
    <label for="drawer-toggle" class="drawer-overlay"></label>
    <aside class="h-screen w-64 bg-base-100 relative overflow-hidden">
        <!-- Logo - Fixed at top -->
        <div
            class="sticky top-0 z-10 bg-base-100 flex flex-row gap-2 p-1 items-center justify-center"
        >
            <div class="p-1 border-b border-base-300 overflow-hidden">
                <a
                    target="_blank"
                    href={siteData?.fullDomain}
                    class="rounded-lg bg-base-100 truncate hover:bg-base-300 flex p-2 items-center gap-2 w-full flex-row overflow-hidden"
                >
                    <div class="flex-shrink-0">
                        <Image
                            publicId={siteData?.seoSettings?.logo}
                            alt={siteData?.name}
                            width={40}
                            height={40}
                            className="rounded-lg"
                        />
                    </div>
                    <div class="flex flex-col truncate">
                        <span class="text-base font-semibold truncate"
                            >{siteData?.fullDomain}</span
                        >
                        {#if siteStore.site}
                            <div class="text-sm text-base-content/70">
                                <!-- <p class="font-medium">{$siteStore.site.name}</p> -->
                                <!-- <p class="text-xs">{$siteStore.site.fullDomain}</p> -->
                                <div class="flex gap-1 p-1">
                                    {#if siteStore.site?.isActive}
                                        <span
                                            class="badge badge-success badge-sm"
                                            >Active</span
                                        >
                                    {:else}
                                        <span class="badge badge-error badge-sm"
                                            >Inactive</span
                                        >
                                    {/if}
                                    {#if siteStore.isExpired}
                                        <span
                                            class="badge badge-warning badge-sm"
                                            >Expired</span
                                        >
                                    {:else if siteStore.daysUntilExpiry !== null && siteStore.daysUntilExpiry !== undefined && siteStore.daysUntilExpiry > 20000}
                                        <span
                                            class="badge badge-warning badge-sm"
                                            >Unlimited</span
                                        >
                                    {:else if siteStore.daysUntilExpiry !== null && siteStore.daysUntilExpiry !== undefined}
                                        <span class="badge badge-info badge-sm">
                                            {Math.max(
                                                0,
                                                siteStore.daysUntilExpiry,
                                            )} days
                                        </span>
                                    {/if}
                                </div>
                            </div>
                        {/if}
                    </div>
                </a>
            </div>
        </div>

        <!-- Navigation - Scrollable middle section -->
        <nav class="p-0 overflow-y-auto" style="height: calc(100vh - 160px);">
            <ul class="menu menu-vertical w-full pb-20">
                {#each filteredNavGroups as group}
                    <li>
                        {#if group.href}
                            <!-- เมนูลิงก์โดยตรง -->
                            <a
                                href={group.href}
                                class="text-base font-medium {isActive(
                                    group.href,
                                )
                                    ? 'menu-active'
                                    : ''}"
                            >
                                <Icon
                                    icon={group.icon}
                                    class="size-7 text-primary"
                                />
                                {group.label}
                            </a>
                        {:else}
                            <!-- เมนูที่มีเมนูย่อย -->
                            <details open={isGroupExpanded(group.id)}>
                                <summary
                                    class="text-base font-medium {hasActiveItem(
                                        group.items,
                                    )
                                        ? 'menu-active'
                                        : ''}"
                                >
                                    <Icon
                                        icon={group.icon}
                                        class="size-7 text-primary"
                                    />
                                    {group.label}
                                </summary>
                                <ul>
                                    {#each group.items as item}
                                        <li>
                                            <a
                                                href={item.href}
                                                class="text-base font-medium {isActive(
                                                    item.href,
                                                )
                                                    ? 'menu-active'
                                                    : ''}"
                                            >
                                                <Icon
                                                    icon={item.icon}
                                                    class="size-7 text-primary"
                                                />
                                                {item.label}
                                            </a>
                                        </li>
                                    {/each}
                                </ul>
                            </details>
                        {/if}
                    </li>
                {/each}
            </ul>
        </nav>

        <!-- Button Back - Fixed at bottom -->
        {#if authStore.user}
            <div
                class="absolute bottom-0 left-0 right-0 p-4 border-t border-base-300 bg-base-100"
            >
                <a href="/dashboard" class="btn btn-wide">
                    <Icon
                        icon="solar:undo-left-round-square-line-duotone"
                        class="size-6 text-primary"
                    />
                    ย้อนกลับ
                </a>
            </div>
        {/if}
    </aside>
</div>

<style scoped>
    @reference "$lib/assets/css/app.css";
    .menu a,
    summary {
        @apply rounded-md hover:!bg-primary/10 text-base md:text-lg !font-medium !text-base-content/90 leading-8;
    }
    .menu-active {
        @apply !bg-primary/10 !shadow-none;
    }
</style>
