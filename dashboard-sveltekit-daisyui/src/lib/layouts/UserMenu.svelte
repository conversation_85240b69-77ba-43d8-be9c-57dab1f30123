<script lang="ts">
    import Icon from "@iconify/svelte";
    import { authStore } from "$lib/stores/auth";
    import { goto } from "$app/navigation";
    import { page } from "$app/state";
    import { onMount } from "svelte";
    import Image from "$lib/components/ui/Image.svelte";

    // Type definition สำหรับ user - ใช้ any เพื่อรองรับข้อมูลที่หลากหลาย
    type User = any;

    // รับข้อมูล user จาก page data หรือ cache
    let user: any = $state(null);
    let isAuthenticated = $state(false);

    // โหลดข้อมูล user จาก cache หรือ page data
    onMount(() => {
        // ลองโหลดจาก cache ก่อน
        const hasCache = authStore.loadUserFromCache();
        
        if (hasCache) {
            // ใช้ข้อมูลจาก cache
            const unsubscribe = authStore.subscribe((state) => {
                user = state.user;
                isAuthenticated = state.isAuthenticated;
            });
            
            // อัปเดต cache ด้วยข้อมูลใหม่จาก server (ถ้ามี)
            if (page.data.user && !page.data.fromCache) {
                authStore.updateUserCache(page.data.user as any);
            }
            
            return unsubscribe;
        } else {
            // ใช้ข้อมูลจาก page data
            user = page.data.user;
            isAuthenticated = !!user;
            
            // บันทึกลง cache
            if (user) {
                authStore.updateUserCache(user as any);
            }
        }
    });

    // Get user initials
    function getUserInitials(user: any): string {
        if (!user) return "U";

        if (user.firstName && user.lastName) {
            return (
                user.firstName.charAt(0) + user.lastName.charAt(0)
            ).toUpperCase();
        }

        if (user.email) {
            return user.email.charAt(0).toUpperCase();
        }

        return "U";
    }

    // Debug log เพื่อดูการเปลี่ยนแปลง
    $effect(() => {
        console.log("UserMenu: User data changed", {
            currentUser: user,
            moneyPoint: user?.moneyPoint,
            isAuthenticated
        });
    });

    async function signout() {
        console.log("UserMenu: Signout button clicked");

        try {
            // ใช้ authStore.signout() ที่จะจัดการทุกอย่างให้
            // รวมถึงการเรียก backend API และลบ cookies
            await authStore.signout();

            console.log("UserMenu: Signout completed");
        } catch (error) {
            console.error("UserMenu: Signout failed:", error);
            // Force redirect as last resort
            goto("/signin");
        }
    }
</script>

{#if isAuthenticated && user}
    <div class="flex items-center gap-2">
        <!-- <ThemeSwitcher />
        <LanguageSwitcher /> -->
        <div class="dropdown dropdown-end">
            <button
                tabindex="0"
                class="btn btn-primary rounded-full p-1 leading-none h-fit"
            >
               
                    <Icon icon="solar:money-bag-bold" class="size-6 text-primary-content" />
                    <span class="text-primary-content text-lg font-bold">{user?.moneyPoint?.toLocaleString() || "0"} </span>
             
                <span class="text-primary-content text-xl font-medium">
                    <!-- {user?.email?.charAt(0) || "U"} -->
                    <Image
                        publicId={user?.avatar}
                        width={26}
                        height={26}
                        cover={true}
                        class="w-26 h-26 rounded-full object-cover"
                        alt="โลโก้"
                        fallbackIcon="solar:global-line-duotone"
                        fallbackIconClass="w-12 h-12 text-primary"
                    />
                </span>
            </button>
            <ul
                class="dropdown-content menu bg-base-100 rounded-box z-1 w-52 p-2 mt-2 shadow-sm gap-1"
            >
                <li>
                    <a href="/dashboard/profile">
                        <Icon icon="solar:user-line-duotone" class="size-6" />
                        {user?.email || "User"}
                    </a>
                </li>
                <li>
                    <a
                        class="text-success bg-success/10 hover:bg-success/20"
                        href="/dashboard/topup"
                    >
                        <Icon
                            icon="solar:wallet-money-line-duotone"
                            class="size-6 text-success"
                        />เติมเงิน
                    </a>
                </li>
                <li>
                    <a
                        class="text-warning bg-warning/10 hover:bg-warning/20"
                        href="/dashboard/admin"
                    >
                        <Icon
                            icon="solar:settings-line-duotone"
                            class="size-6 text-warning"
                        />จัดการระบบ
                    </a>
                </li>
                <li>
                    <button
                        class="text-error bg-error/10 hover:bg-error/20"
                        onclick={signout}
                    >
                        <Icon
                            icon="solar:logout-3-line-duotone"
                            class="size-6 text-error"
                        />ออกจากระบบ
                    </button>
                </li>
            </ul>
        </div>
    </div>
{/if}
