<script lang="ts">
	import Icon from '@iconify/svelte';

	interface Props {
		color?:
			| 'primary'
			| 'secondary'
			| 'accent'
			| 'ghost'
			| 'link'
			| 'info'
			| 'success'
			| 'warning'
			| 'error';
		size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
		variant?: 'solid' | 'outline' | 'ghost' | 'link';
		outline?: boolean;
		clear?: boolean;
		loading?: boolean;
		disabled?: boolean;
		wide?: boolean;
		block?: boolean;
		circle?: boolean;
		square?: boolean;
		glass?: boolean;
		noAnimation?: boolean;
		icon?: string;
		iconRight?: string;
		href?: string;
		type?: 'button' | 'submit' | 'reset';
		onclick?: () => void;
		label?: string;
		children?: any;
		class?: string;
		[key: `data-${string}`]: any;
	}

	const {
		color = 'primary',
		size = 'md',
		variant = 'solid',
		outline = false,
		clear = false,
		loading = false,
		disabled = false,
		wide = false,
		block = false,
		circle = false,
		square = false,
		glass = false,
		noAnimation = false,
		icon,
		iconRight,
		href,
		type = 'button',
		onclick,
		label,
		children,
		class: className = '',
		...restProps
	}: Props = $props();

	// Computed icon size based on button size
	const iconSize = $derived(
		{
			xs: 'size-3',
			sm: 'size-4',
			md: 'size-5',
			lg: 'size-6',
			xl: 'size-7',
		}[size] || 'size-5'
	);

	// Disabled state includes loading
	const isDisabled = $derived(disabled || loading);

	const baseClasses = 'btn';

	const classes = $derived(
		[
			baseClasses,
			color !== 'primary' ? `btn-${color}` : 'btn-primary',
			size === 'xs'
				? 'btn-xs'
				: size === 'sm'
					? 'btn-sm'
					: size === 'lg'
						? 'btn-lg'
						: size === 'xl'
							? 'btn-xl'
							: '',
			variant !== 'solid' ? `btn-${variant}` : '',
			outline ? 'btn-outline' : '',
			// loading ? 'loading' : '',
			isDisabled ? 'btn-disabled' : '',
			wide ? 'btn-wide' : '',
			block ? 'btn-block' : '',
			circle ? 'btn-circle' : '',
			square ? 'btn-square' : '',
			glass ? 'btn-glass' : '',
			noAnimation ? 'no-animation' : '',
			className,
		]
			.filter(Boolean)
			.join(' ')
	);
</script>

{#if href}
	<a {href} class={classes} {...restProps}>
		{#if loading}
			<span class="loading loading-spinner loading-sm"></span>
		{:else if icon}
			<Icon {icon} class={iconSize} />
		{/if}
		{#if label}
			{label}
		{:else}
			{@render children?.()}
		{/if}
		{#if iconRight && !loading}
			<Icon icon={iconRight} class={iconSize} />
		{/if}
	</a>
{:else}
	<button {type} class={classes} disabled={isDisabled} {onclick} {...restProps}>
		{#if loading}
			<span class="loading loading-spinner loading-sm"></span>
		{:else if icon}
			<Icon {icon} class={iconSize} />
		{/if}
		{#if label}
			{label}
		{:else}
			{@render children?.()}
		{/if}
		{#if iconRight && !loading}
			<Icon icon={iconRight} class={iconSize} />
		{/if}
	</button>
{/if}