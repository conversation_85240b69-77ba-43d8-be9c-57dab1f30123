<script lang="ts">
	import Icon from '@iconify/svelte';
	import Button from './Button.svelte';

	interface Props {
		open?: boolean;
		title?: string;
		size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
		closable?: boolean;
		onclose?: () => void;
		children?: any;
		actions?: any;
		class?: string;
	}

	let {
		open = $bindable(false),
		title,
		size = 'md',
		closable = true,
		onclose,
		children,
		actions,
		class: className = '',
	}: Props = $props();

	function handleClose() {
		if (closable) {
			open = false;
			onclose?.();
		}
	}

	function handleBackdropClick(event: MouseEvent) {
		if (event.target === event.currentTarget) {
			handleClose();
		}
	}

	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Escape' && closable) {
			handleClose();
		}
	}

	const modalClasses = $derived(
		['modal', open ? 'modal-open' : '', className].filter(Boolean).join(' ')
	);
	const boxClasses = $derived(
		[
			'modal-box',
			size === 'sm' ? 'max-w-sm' : '',
			size === 'lg' ? 'max-w-2xl' : '',
			size === 'xl' ? 'max-w-4xl' : '',
			size === 'full' ? 'max-w-full h-full' : '',
		]
			.filter(Boolean)
			.join(' ')
	);
</script>

<div
	class={modalClasses}
	onclick={handleBackdropClick}
	onkeydown={handleKeydown}
	role="dialog"
	aria-modal="true"
	aria-labelledby={title ? 'modal-title' : undefined}
	tabindex="-1"
>
	<div class={boxClasses}>
		{#if title || closable}
			<div class="flex items-center justify-between mb-4">
				{#if title}
					<h3 id="modal-title" class="font-bold text-lg">{title}</h3>
				{/if}
				{#if closable}
					<Button variant="ghost" size="sm" circle icon="mdi:close" onclick={handleClose} />
				{/if}
			</div>
		{/if}

		<div class="py-4">
			{@render children?.()}
		</div>

		{#if actions}
			<div class="modal-action">
				{@render actions?.()}
			</div>
		{/if}
	</div>
</div>
