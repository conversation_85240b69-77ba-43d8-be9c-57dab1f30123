<script lang="ts">
	import Icon from '@iconify/svelte';

	interface Option {
		value: string | number;
		label: string;
		description?: string;
		icon?: string;
		disabled?: boolean;
	}

	interface Props {
		options?: Option[];
		value?: (string | number)[];
		onChange?: (value: (string | number)[]) => void;
		name?: string;
		disabled?: boolean; 
		checked?: boolean;
		variant?: 'default' | 'card';
		cols?: 1 | 2 | 3 | 4;
		size?: 'sm' | 'md' | 'lg';
		label?: string;
		required?: boolean;
		id?: string;
		class?: string;
		color?: 'primary' | 'secondary' | 'accent' | 'info' | 'success' | 'warning' | 'error';
	}

	let {
		color = 'primary',
		options = [],
		value = $bindable([]),
		onChange,
		name = 'checkbox-group',
		disabled = false,
		class: className = '',
		checked = $bindable(false),
		variant = 'default',
		cols = 2,
		size = 'md',
		label,
		required = false,
		id = 'checkbox-group',
	}: Props = $props();

	// Functions
	function handleOptionClick(optionValue: string | number) {
		if (disabled || options.find(opt => opt.value === optionValue)?.disabled) return;

		const newValue = [...value];
		const index = newValue.indexOf(optionValue);

		if (index > -1) {
			newValue.splice(index, 1);
		} else {
			newValue.push(optionValue);
		}

		value = newValue;
		onChange?.(newValue);
	}

	function handleSingleCheckboxChange() {
		if (disabled) return;
		checked = !checked;
	}

	function handleKeyDown(event: KeyboardEvent, optionValue: string | number) {
		if (event.key === 'Enter' || event.key === ' ') {
			event.preventDefault();
			handleOptionClick(optionValue);
		}
	}

	function isSelected(optionValue: string | number): boolean {
		return value.includes(optionValue);
	}

	// Computed classes
	const gridCols = $derived(`grid-cols-1 sm:grid-cols-${cols}`);
	const sizeClasses = $derived(
		{
			sm: 'p-2 text-sm',
			md: 'p-4',
			lg: 'p-6 text-lg',
		}[size]
	);

	// Single checkbox mode
	const isSingleMode = $derived(options.length === 0);
</script>

<div class={className}>
	<!-- {#if label}
		<label for={id} class="label">
			<span class="label-text">{label}</span>
			{#if required}
				<span class="text-error">*</span>
			{/if}
		</label>
	{/if} -->

	{#if isSingleMode}
		<!-- Single checkbox mode -->
		<label class="label cursor-pointer justify-start gap-3 hover:bg-base-200 rounded-lg transition-colors p-3 {disabled ? 'opacity-50 cursor-not-allowed' : ''}">
			<input
				type="checkbox"
				{name}
				{id}
				checked={checked}
				disabled={disabled}
				class="checkbox checkbox-{size} checkbox-{color}"
				onchange={handleSingleCheckboxChange}
			/>
			<span class="label-text">{label}</span>
		</label>
	{:else if variant === 'card'}
		<!-- Multiple options card mode -->
		<div class="grid {gridCols} gap-3">
			{#each options as option}
				<button
					type="button"
					class="relative flex items-center gap-3 cursor-pointer rounded-xl border transition-colors select-none {sizeClasses}
							{isSelected(option.value)
						? 'border-primary bg-primary/5 ring-2 ring-primary/20'
						: 'border-base-300 hover:bg-base-200'}
							{option.disabled || disabled ? 'opacity-50 cursor-not-allowed' : ''}"
					onclick={() => handleOptionClick(option.value)}
					onkeydown={e => handleKeyDown(e, option.value)}
					disabled={option.disabled || disabled}
				>
					<input
						type="checkbox"
						{name}
						value={option.value}
						checked={isSelected(option.value)}
						disabled={option.disabled || disabled}
						class="sr-only"
					/>

					{#if option.icon}
						<Icon icon={option.icon} class="w-6 h-6 text-primary" />
					{/if}

					<div class="flex-1">
						<div class="font-medium text-base-content">
							{option.label}
						</div>
						{#if option.description}
							<div class="text-sm text-base-content/70">
								{option.description}
							</div>
						{/if}
					</div>

					<!-- Checkbox indicator -->
					<div
						class="flex items-center justify-center w-5 h-5 rounded border-2 transition-colors
							{isSelected(option.value) ? 'border-primary bg-primary text-primary-content' : 'border-base-300'}"
					>
						{#if isSelected(option.value)}
							<Icon icon="solar:check-bold" class="w-3 h-3" />
						{/if}
					</div>
				</button>
			{/each}
		</div>
	{:else}
		<!-- Multiple options default mode -->
		<div class="space-y-2">
			{#each options as option}
				<label
					class="label cursor-pointer {sizeClasses} hover:bg-base-200 rounded-lg transition-colors
							{option.disabled || disabled ? 'opacity-50 cursor-not-allowed' : ''}"
				>
					<input
						type="checkbox"
						{name}
						value={option.value}
						checked={isSelected(option.value)}
						disabled={option.disabled || disabled}
						class="checkbox checkbox-{size} checkbox-{color}"
					/>
					<div class="flex items-center gap-2">
						{#if option.icon}
							<Icon icon={option.icon} class="w-4 h-4 text-primary" />
						{/if}
						<span class="label-text">{option.label}</span>
						{#if option.description}
							<span class="text-xs text-base-content/70">({option.description})</span>
						{/if}
					</div>
				</label>
			{/each}
		</div>
	{/if}
</div>