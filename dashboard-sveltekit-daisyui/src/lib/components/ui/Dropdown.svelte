<script lang="ts">
	import Icon from '@iconify/svelte'; 
	import { onMount } from 'svelte';

	let { 
		items = [], 
		width = 'w-fit',
		children
	} = $props();

	let isOpen = $state(false);
	let dropdownRef: HTMLDivElement;

	function toggleDropdown() {
		isOpen = !isOpen;
	}

	function closeDropdown() {
		isOpen = false;
	}

	function handleItemClick(item: any) {
		if (item.onClick) {
			item.onClick(item);
		}
		closeDropdown();
	}

	// ปิด dropdown เมื่อคลิกข้างนอก
	onMount(() => {
		function handleClickOutside(event: MouseEvent) {
			if (dropdownRef && !dropdownRef.contains(event.target as Node)) {
				closeDropdown();
			}
		}

		document.addEventListener('click', handleClickOutside);
		return () => document.removeEventListener('click', handleClickOutside);
	});
</script>

<div class="dropdown dropdown-end" bind:this={dropdownRef}>
	<button
		type="button"
		class="btn btn-circle btn-soft m-1"
		aria-haspopup="menu"
		aria-expanded={isOpen}
		onclick={toggleDropdown}
		onkeydown={e => {
			if (e.key === 'Enter' || e.key === ' ') {
				e.preventDefault();
				toggleDropdown();
			}
		}}
	>
		{@render children?.()}
	</button>

	{#if isOpen}
		<ul 
			class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box {width}"
			role="menu"
		>
			{#each items as item}
				<li role="none">
					<button
						class="flex items-center space-x-2 w-full text-left {item.active ? 'bg-primary text-primary-content' : ''}"
						onclick={() => handleItemClick(item)}
						role="menuitem"
					>
						{#if item.icon}
							<Icon icon={item.icon} class="w-4 h-4" />
						{/if}
						<span>{item.label}</span>
						{#if item.active}
							<svg class="w-4 h-4 ml-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
							</svg>
						{/if}
					</button>
				</li>
			{/each}
		</ul>
	{/if}
</div>