<script lang="ts">
	import { onMount } from "svelte";
	import Icon from "@iconify/svelte";

	let currentTheme = "light";

	function handleThemeChange() {
		const newTheme = currentTheme === "light" ? "dark" : "light";
		currentTheme = newTheme;
		document.documentElement.setAttribute("data-theme", newTheme);
		localStorage.setItem("theme", newTheme);
	}

	onMount(() => {
		// โหลด theme จาก localStorage หรือใช้ default
		const savedTheme = localStorage.getItem("theme") || "light";
		currentTheme = savedTheme;
	});
</script>

<button
	class="btn btn-circle btn-soft relative"
	onclick={handleThemeChange}
	title={currentTheme === "light"
		? "เปลี่ยนเป็นโหมดมืด"
		: "เปลี่ยนเป็นโหมดสว่าง"}
> 
	<Icon
		icon={currentTheme === "light"
			? "line-md:sunny-filled-loop-to-moon-filled-alt-loop-transition"
			: "line-md:moon-filled-alt-to-sunny-filled-loop-transition"}
		class="size-6"
	/> 
</button>
