<script lang="ts">
	import Icon from "@iconify/svelte";
	import Dropdown from "./ui/Dropdown.svelte";
	import { setLocale, getLocale } from "$lib/paraglide/runtime.js";

	let selectedLanguage = $state(getLocale());

	const languages = [
		{ code: "th", name: "ไทย", icon: "emojione:flag-for-thailand" },
		{ code: "lo", name: "ລາວ", icon: "emojione:flag-for-laos" },
		{ code: "en", name: "English", icon: "emojione:flag-for-united-states" },
	];

	function handleLanguageChange(language: any) {
		selectedLanguage = language.code;
		setLocale(language.code);
	}

	const currentLanguageData = $derived(
		languages.find((lang) => lang.code === selectedLanguage),
	);

	const dropdownItems = $derived(
		languages.map(lang => ({
			...lang,
			label: lang.name,
			active: lang.code === selectedLanguage,
			onClick: handleLanguageChange
		}))
	);
</script>

<Dropdown 
	items={dropdownItems}
>
	<Icon icon={currentLanguageData?.icon || 'emojione:flag-for-thailand'} class="size-7" />
</Dropdown>
