{"name": "dashboard-sveltekit", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "typecheck": "tsc --noEmit", "lint": "bunx @biomejs/biome check ./src", "lint:ts": "bun lint.ts", "format": "bunx @biomejs/biome format --write ./src", "format:ts": "bun format.ts", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch"}, "devDependencies": {"@biomejs/biome": "2.1.3", "@iconify/svelte": "^5.0.1", "@sveltejs/adapter-vercel": "^5.8.1", "@sveltejs/kit": "^2.27.0", "@sveltejs/vite-plugin-svelte": "^6.1.0", "@tailwindcss/vite": "^4.1.11", "daisyui": "^5.0.50", "svelte": "^5.37.3", "svelte-check": "^4.3.1", "tailwindcss": "^4.1.11", "typescript": "^5.9.2", "vite": "^7.0.6", "vite-plugin-devtools-json": "^0.4.1"}, "dependencies": {"@inlang/paraglide-js": "^2.2.0", "sweetalert2-neutral": "^11.22.2-neutral", "zod": "^4.0.14"}}