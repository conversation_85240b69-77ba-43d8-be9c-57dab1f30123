# ข้อเสนอแนะด้านความปลอดภัยสำหรับระบบ Authentication

## 1. ปรับปรุง JWT Secret
```env
# ใช้ secret key ที่แข็งแกร่งกว่า (อย่างน้อย 256 bits)
JWT_SECRET=your-very-strong-secret-key-with-at-least-32-characters-or-more
JWT_REFRESH_SECRET=your-different-refresh-secret-key-with-at-least-32-characters
```

## 2. ปรับปรุง Token Storage
```typescript
// ใช้ HttpOnly cookies แทน localStorage
cookies.set('accessToken', token, {
  httpOnly: true,
  secure: true, // HTTPS only
  sameSite: 'strict',
  maxAge: 15 * 60, // 15 minutes
  path: '/'
});
```

## 3. Token Rotation
```typescript
// Rotate refresh token ทุกครั้งที่ใช้
static async refreshToken(oldRefreshToken: string) {
  // Verify old token
  const user = await User.findOne({ refreshToken: oldRefreshToken });
  
  // Generate new tokens
  const newAccessToken = generateAccessToken(user._id);
  const newRefreshToken = generateRefreshToken();
  
  // Update in database
  user.refreshToken = newRefreshToken;
  await user.save();
  
  return { accessToken: newAccessToken, refreshToken: newRefreshToken };
}
```

## 4. Token Blacklist
```typescript
// เพิ่ม blacklist collection
const tokenBlacklistSchema = new mongoose.Schema({
  token: { type: String, required: true, unique: true },
  expiresAt: { type: Date, required: true }
});

// ตรวจสอบ blacklist ใน middleware
const isTokenBlacklisted = await TokenBlacklist.findOne({ token });
if (isTokenBlacklisted) {
  throw new Error('Token has been revoked');
}
```

## 5. Rate Limiting
```typescript
// เพิ่ม rate limiting สำหรับ signin
import { rateLimit } from '@elysiajs/rate-limit';

app.use(rateLimit({
  duration: 60000, // 1 minute
  max: 5, // 5 attempts per minute
  generator: (request) => request.headers['x-forwarded-for'] || 'anonymous'
}));
```

## 6. Session Management
```typescript
// เพิ่มการจัดการ session
interface UserSession {
  userId: string;
  deviceId: string;
  ipAddress: string;
  userAgent: string;
  lastActivity: Date;
  isActive: boolean;
}

// ตรวจสอบ concurrent sessions
const activeSessions = await UserSession.countDocuments({ 
  userId, 
  isActive: true 
});

if (activeSessions > MAX_CONCURRENT_SESSIONS) {
  // Revoke oldest session
}
```

## 7. CSRF Protection
```typescript
// เพิ่ม CSRF token
import { csrf } from '@elysiajs/csrf';

app.use(csrf({
  origin: ['http://localhost:8000'],
  methods: ['POST', 'PUT', 'DELETE']
}));
```