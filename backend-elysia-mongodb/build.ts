import { join } from 'path';
import { build } from 'bun';
import { mkdir, writeFile } from 'fs/promises';
// import { config } from '@/src/config';

async function buildApp() {
  console.log('🚀 เริ่มต้นการ build แอปพลิเคชัน...');

  try {
    // สร้างโฟลเดอร์ dist ถ้ายังไม่มี
    await mkdir('dist', { recursive: true });

    // Build แอปพลิเคชัน
    const result = await build({
      entrypoints: ['./src/index.ts'],
      outdir: './dist',
      target: 'bun',
      minify: true,
      sourcemap: 'external',
    });

    if (!result.success) {
      console.error('❌ การ build ล้มเหลว:');
      for (const message of result.logs) {
        console.error(`- ${message.message}`);
      }
      process.exit(1);
    }

    // สร้างไฟล์ package.json สำหรับ production
    const packageJson = {
      name: 'bunshop2026',
      version: '1.0.0',
      type: 'module',
      dependencies: {
        '@elysiajs/cors': '^1.2.0',
        '@elysiajs/jwt': '^1.2.0',
        '@elysiajs/static': '^1.2.0',
        '@elysiajs/swagger': '^1.2.2',
        argon2: '^0.41.1',
        bcryptjs: '^3.0.2',
        elysia: 'latest',
        'elysia-helmet': '^2.0.0',
        'elysia-rate-limit': '^4.3.0',
        'http-status-codes': '^2.3.0',
        logestic: '^1.2.4',
        mongoose: '^8.13.2',
        nanoid: '^5.1.5',
        nodemailer: '^6.10.0',
        otplib: '^12.0.1',
        sharp: '^0.34.1',
      },
      engines: {
        node: '>=18.0.0',
        bun: '>=1.0.0',
      },
    };

    await writeFile(join('dist', 'package.json'), JSON.stringify(packageJson, null, 2));

    // สร้างไฟล์ .env.example สำหรับ production
    await writeFile(
      join('dist', '.env.example'),
      `# แอปพลิเคชัน
NODE_ENV=production
PORT=3000
APP_URL=http://localhost:3000

# JWT
JWT_SECRET=your_jwt_secret_here

# MongoDB
MONGODB_URI=mongodb://localhost:27017/bunshop2026

# SMTP
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=password
SMTP_FROM=BunShop <<EMAIL>>

# CORS
CORS_ORIGINS=http://localhost:8000,http://localhost:8080`
    );

    // สร้างไฟล์ README.md สำหรับ production
    await writeFile(
      join('dist', 'README.md'),
      `# BunShop2026 - Production Build

## การติดตั้ง

1. สร้างไฟล์ \`.env\` จาก \`.env.example\`:
   \`\`\`bash
   cp .env.example .env
   \`\`\`

2. แก้ไขไฟล์ \`.env\` ตามการตั้งค่าของคุณ

3. ติดตั้ง dependencies:
   \`\`\`bash
   bun install
   \`\`\`

4. เริ่มต้นแอปพลิเคชัน:
   \`\`\`bash
   bun index.js
   \`\`\`

## การใช้งาน Docker

\`\`\`bash
# Build image
docker build -t bunshop2026 .

# Run container
docker run -p 3000:3000 bunshop2026
\`\`\`
`
    );

    // สร้างไฟล์ Dockerfile สำหรับ production
    await writeFile(
      join('dist', 'Dockerfile'),
      `FROM oven/bun:1.0-slim

WORKDIR /app

COPY package.json .
COPY .env.example .env

RUN bun install --production

COPY . .

EXPOSE 3000

CMD ["bun", "index.js"]`
    );

    // สร้างโฟลเดอร์ที่จำเป็น
    await mkdir('dist/uploads', { recursive: true });
    await mkdir('dist/logs', { recursive: true });

    console.log('✅ การ build สำเร็จ! ไฟล์ถูกสร้างไว้ที่โฟลเดอร์ dist');
    console.log(
      '📦 ขนาดไฟล์ทั้งหมด:',
      formatBytes(result.outputs.reduce((acc, output) => acc + output.size, 0))
    );
  } catch (error) {
    console.error('❌ เกิดข้อผิดพลาดในการ build:', error);
    process.exit(1);
  }
}

// ฟังก์ชันแปลงขนาดไฟล์เป็นหน่วยที่อ่านง่าย
function formatBytes(bytes: number, decimals = 2): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${Number.parseFloat((bytes / k ** i).toFixed(dm))} ${sizes[i]}`;
}

// เริ่มต้นการ build
buildApp();
