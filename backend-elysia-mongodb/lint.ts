// ฟังก์ชันสำหรับรัน linter
async function runLinter() {
  console.log('🔍 เริ่มต้นการตรวจสอบโค้ด...');

  try {
    // รัน Biome linter
    console.log('\n🧹 กำลังตรวจสอบโค้ดด้วย Biome...');
    const biomeProc = Bun.spawn(['bunx', '@biomejs/biome', 'lint', './src'], {
      stdout: 'inherit',
      stderr: 'inherit',
      stdin: 'inherit',
    });

    const biomeExitCode = await biomeProc.exited;
    if (biomeExitCode !== 0) {
      console.error('❌ Biome พบข้อผิดพลาด');
      console.log('\n💡 คุณสามารถแก้ไขปัญหาบางส่วนอัตโนมัติด้วยคำสั่ง: bun check');
      process.exit(1);
    }

    // รัน TypeScript type checking
    console.log('\n📝 กำลังตรวจสอบ TypeScript types...');
    const tscProc = Bun.spawn(['bun', 'typecheck'], {
      stdout: 'inherit',
      stderr: 'inherit',
      stdin: 'inherit',
    });

    const tscExitCode = await tscProc.exited;
    if (tscExitCode !== 0) {
      console.error('❌ TypeScript พบข้อผิดพลาด');
      process.exit(1);
    }

    console.log('\n✅ การตรวจสอบโค้ดผ่านทั้งหมด!');
  } catch (error) {
    console.error('❌ เกิดข้อผิดพลาดในการรัน linter:', error);
    process.exit(1);
  }
}

// เริ่มต้นการตรวจสอบโค้ด
runLinter();
