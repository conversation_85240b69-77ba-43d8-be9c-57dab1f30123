import { jwt } from "@elysiajs/jwt";
import { Elysia, error, t } from "elysia";
import { CustomerService } from "@/modules/customer/customer.service";
import { User } from "@/modules/user/user.model";
import { Customer } from "@/modules/customer/customer.model";

// JWT configuration for admin
const adminJwtConfig = {
	name: "jwt",
	secret: process.env.JWT_SECRET || "your-secret-key",
	exp: "7d",
	alg: "HS256",
};

// Admin authentication middleware
const adminAuth = async (headers: any, jwt: any) => {
	const authHeader = headers.authorization;
	if (!authHeader || !authHeader.startsWith("Bearer ")) {
		throw error(401, { message: "Token ไม่ถูกต้อง" });
	}

	const token = authHeader.substring(7);
	const payload = await jwt.verify(token);

	if (!payload || !payload.userId) {
		throw error(401, { message: "Token ไม่ถูกต้อง" });
	}

	// Check if user exists and is active
	const user = await User.findById(payload.userId);
	if (!user || !user.isActive) {
		throw error(401, { message: "ไม่มีสิทธิ์เข้าถึง" });
	}

	return payload.userId as string;
};

export const adminRoute = new Elysia({ prefix: "/admin" })
	.use(jwt(adminJwtConfig))

	// Get dashboard stats
	.get("/dashboard", async ({ headers, jwt }) => {
		await adminAuth(headers, jwt);

		const [totalUsers, totalCustomers, activeUsers, activeCustomers] = await Promise.all([
			User.countDocuments(),
			Customer.countDocuments(),
			User.countDocuments({ isActive: true }),
			Customer.countDocuments({ isActive: true }),
		]);

		return {
			success: true,
			data: {
				totalUsers,
				totalCustomers,
				activeUsers,
				activeCustomers,
				inactiveUsers: totalUsers - activeUsers,
				inactiveCustomers: totalCustomers - activeCustomers,
			},
		};
	})

	// User management routes
	.group("/users", (app) =>
		app
			// Get all users
			.get("/", async ({ headers, jwt, query }) => {
				await adminAuth(headers, jwt);

				const page = parseInt(query.page as string) || 1;
				const limit = parseInt(query.limit as string) || 10;
				const search = query.search as string;

				const skip = (page - 1) * limit;
				const searchQuery = search
					? {
							$or: [
								{ email: { $regex: search, $options: "i" } },
								{ firstName: { $regex: search, $options: "i" } },
								{ lastName: { $regex: search, $options: "i" } },
							],
						}
					: {};

				const [users, total] = await Promise.all([
					User.find(searchQuery)
						.select("-password")
						.sort({ createdAt: -1 })
						.skip(skip)
						.limit(limit),
					User.countDocuments(searchQuery),
				]);

				return {
					success: true,
					data: {
						users,
						total,
						page,
						totalPages: Math.ceil(total / limit),
					},
				};
			})

			// Get user by ID
			.get("/:id", async ({ headers, jwt, params }) => {
				await adminAuth(headers, jwt);

				const user = await User.findById(params.id).select("-password");
				if (!user) {
					throw error(404, { message: "ไม่พบผู้ใช้" });
				}

				return {
					success: true,
					data: user,
				};
			})

			// Toggle user status
			.put("/:id/toggle-status", async ({ headers, jwt, params }) => {
				const adminUserId = await adminAuth(headers, jwt);

				// Prevent admin from deactivating themselves
				if (adminUserId === params.id) {
					throw error(400, { message: "ไม่สามารถระงับบัญชีตัวเองได้" });
				}

				const user = await User.findById(params.id);
				if (!user) {
					throw error(404, { message: "ไม่พบผู้ใช้" });
				}

				user.isActive = !user.isActive;
				await user.save();

				return {
					success: true,
					message: user.isActive ? "เปิดใช้งานบัญชีแล้ว" : "ระงับบัญชีแล้ว",
					data: { isActive: user.isActive },
				};
			})

			// Update user
			.put(
				"/:id",
				async ({ headers, jwt, params, body }) => {
					await adminAuth(headers, jwt);

					const user = await User.findById(params.id);
					if (!user) {
						throw error(404, { message: "ไม่พบผู้ใช้" });
					}

					const { firstName, lastName, phone, avatar, isActive } = body as any;

					if (firstName) user.firstName = firstName;
					if (lastName) user.lastName = lastName;
					if (phone !== undefined) user.phone = phone;
					if (avatar !== undefined) user.avatar = avatar;
					if (isActive !== undefined) user.isActive = isActive;

					await user.save();

					const { password: _, ...userWithoutPassword } = user.toObject();

					return {
						success: true,
						message: "อัปเดตข้อมูลผู้ใช้สำเร็จ",
						data: userWithoutPassword,
					};
				},
				{
					body: t.Object({
						firstName: t.Optional(t.String()),
						lastName: t.Optional(t.String()),
						phone: t.Optional(t.String()),
						avatar: t.Optional(t.String()),
						isActive: t.Optional(t.Boolean()),
					}),
				},
			)

			// Delete user
			.delete("/:id", async ({ headers, jwt, params }) => {
				const adminUserId = await adminAuth(headers, jwt);

				// Prevent admin from deleting themselves
				if (adminUserId === params.id) {
					throw error(400, { message: "ไม่สามารถลบบัญชีตัวเองได้" });
				}

				const user = await User.findByIdAndDelete(params.id);
				if (!user) {
					throw error(404, { message: "ไม่พบผู้ใช้" });
				}

				return {
					success: true,
					message: "ลบผู้ใช้สำเร็จ",
				};
			}),
	)

	// Customer management routes
	.group("/customers", (app) =>
		app
			// Get all customers
			.get("/", async ({ headers, jwt, query }) => {
				await adminAuth(headers, jwt);

				const page = parseInt(query.page as string) || 1;
				const limit = parseInt(query.limit as string) || 10;
				const search = query.search as string;

				const result = await CustomerService.getAllCustomers(page, limit, search);

				return {
					success: true,
					data: result,
				};
			})

			// Get customer by ID
			.get("/:id", async ({ headers, jwt, params }) => {
				await adminAuth(headers, jwt);

				const customer = await Customer.findById(params.id).select("-password");
				if (!customer) {
					throw error(404, { message: "ไม่พบลูกค้า" });
				}

				return {
					success: true,
					data: customer,
				};
			})

			// Toggle customer status
			.put("/:id/toggle-status", async ({ headers, jwt, params }) => {
				await adminAuth(headers, jwt);

				const result = await CustomerService.toggleCustomerStatus(params.id);

				return {
					success: true,
					message: result.message,
					data: { isActive: result.isActive },
				};
			})

			// Update customer
			.put(
				"/:id",
				async ({ headers, jwt, params, body }) => {
					await adminAuth(headers, jwt);

					const customer = await Customer.findById(params.id);
					if (!customer) {
						throw error(404, { message: "ไม่พบลูกค้า" });
					}

					const { firstName, lastName, phone, avatar, isActive, address, dateOfBirth, gender } =
						body as any;

					if (firstName) customer.firstName = firstName;
					if (lastName) customer.lastName = lastName;
					if (phone !== undefined) customer.phone = phone;
					if (avatar !== undefined) customer.avatar = avatar;
					if (isActive !== undefined) customer.isActive = isActive;
					if (address) customer.address = { ...customer.address, ...address };
					if (dateOfBirth) customer.dateOfBirth = new Date(dateOfBirth);
					if (gender) customer.gender = gender;

					await customer.save();

					const { password: _, ...customerWithoutPassword } = customer.toObject();

					return {
						success: true,
						message: "อัปเดตข้อมูลลูกค้าสำเร็จ",
						data: customerWithoutPassword,
					};
				},
				{
					body: t.Object({
						firstName: t.Optional(t.String()),
						lastName: t.Optional(t.String()),
						phone: t.Optional(t.String()),
						avatar: t.Optional(t.String()),
						isActive: t.Optional(t.Boolean()),
						address: t.Optional(
							t.Object({
								street: t.Optional(t.String()),
								city: t.Optional(t.String()),
								province: t.Optional(t.String()),
								postalCode: t.Optional(t.String()),
								country: t.Optional(t.String()),
							}),
						),
						dateOfBirth: t.Optional(t.String()),
						gender: t.Optional(
							t.Union([t.Literal("male"), t.Literal("female"), t.Literal("other")]),
						),
					}),
				},
			)

			// Delete customer
			.delete("/:id", async ({ headers, jwt, params }) => {
				await adminAuth(headers, jwt);

				const customer = await Customer.findByIdAndDelete(params.id);
				if (!customer) {
					throw error(404, { message: "ไม่พบลูกค้า" });
				}

				return {
					success: true,
					message: "ลบลูกค้าสำเร็จ",
				};
			}),
	);
