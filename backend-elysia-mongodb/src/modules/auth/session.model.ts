import mongoose from "mongoose";

export interface IUserSession {
    _id: string;
    userId: string;
    sessionId: string;
    deviceInfo: {
        userAgent: string;
        browser: string;
        os: string;
        device: string;
    };
    location: {
        ipAddress: string;
        country?: string;
        city?: string;
    };
    isActive: boolean;
    lastActivity: Date;
    loginTime: Date;
    expiresAt: Date;
    refreshToken: string;
    createdAt: Date;
    updatedAt: Date;
}

const userSessionSchema = new mongoose.Schema<IUserSession>(
    {
        _id: { type: String, default: () => new mongoose.Types.ObjectId().toString() },
        userId: {
            type: String,
            required: true,
            index: true,
        },
        sessionId: {
            type: String,
            required: true,
            unique: true,
            index: true,
        },
        deviceInfo: {
            userAgent: { type: String, required: true },
            browser: { type: String, default: 'Unknown' },
            os: { type: String, default: 'Unknown' },
            device: { type: String, default: 'Unknown' },
        },
        location: {
            ipAddress: { type: String, required: true },
            country: { type: String },
            city: { type: String },
        },
        isActive: {
            type: Boolean,
            default: true,
            index: true,
        },
        lastActivity: {
            type: Date,
            default: Date.now,
            index: true,
        },
        loginTime: {
            type: Date,
            default: Date.now,
        },
        expiresAt: {
            type: Date,
            required: true,
            index: { expireAfterSeconds: 0 }, // MongoDB จะลบ document อัตโนมัติ
        },
        refreshToken: {
            type: String,
            required: true,
            index: true,
        },
    },
    {
        timestamps: true,
        versionKey: false,
    },
);

// Compound indexes สำหรับการค้นหาที่มีประสิทธิภาพ
userSessionSchema.index({ userId: 1, isActive: 1 });
userSessionSchema.index({ userId: 1, lastActivity: -1 });
userSessionSchema.index({ refreshToken: 1, isActive: 1 });

export const UserSession = mongoose.model<IUserSession>("UserSession", userSessionSchema);