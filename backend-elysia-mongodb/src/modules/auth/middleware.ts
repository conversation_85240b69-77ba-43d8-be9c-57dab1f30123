import { Elysia, error } from "elysia";
import { TokenBlacklistService } from "./blacklist.service";
import { RateLimitService } from "./ratelimit.service";

/**
 * Middleware สำหรับตรวจสอบ token blacklist
 */
export const tokenBlacklistMiddleware = new Elysia({ name: 'tokenBlacklist' })
    .derive(async ({ headers }) => {
        const authHeader = headers.authorization;

        if (authHeader && authHeader.startsWith("Bearer ")) {
            const token = authHeader.substring(7);

            // ตรวจสอบว่า token อยู่ใน blacklist หรือไม่
            const isBlacklisted = await TokenBlacklistService.isTokenBlacklisted(token);

            if (isBlacklisted) {
                throw error(401, { message: "Token ถูกยกเลิกแล้ว" });
            }
        }

        return {};
    });

/**
 * Middleware สำหรับ rate limiting
 */
export const rateLimitMiddleware = (endpoint: string) =>
    new Elysia({ name: `rateLimit-${endpoint}` })
        .derive(async ({ request, headers }) => {
            // ใช้ IP address เป็น identifier
            const identifier = headers['x-forwarded-for'] ||
                headers['x-real-ip'] ||
                'unknown';

            const rateLimitResult = await RateLimitService.checkRateLimit(
                identifier as string,
                endpoint
            );

            if (!rateLimitResult.allowed) {
                const resetTimeSeconds = Math.ceil(
                    (rateLimitResult.resetTime.getTime() - Date.now()) / 1000
                );

                throw error(429, {
                    message: rateLimitResult.isBlocked
                        ? `IP ถูกบล็อกชั่วคราว กรุณารอ ${resetTimeSeconds} วินาที`
                        : `คำขอเกินขีดจำกัด กรุณารอ ${resetTimeSeconds} วินาที`,
                    retryAfter: resetTimeSeconds,
                    remaining: rateLimitResult.remaining,
                });
            }

            return {
                rateLimitInfo: rateLimitResult
            };
        });

/**
 * Helper function สำหรับดึง IP address
 */
export function getClientIP(headers: Record<string, string | undefined>): string {
    return (headers['x-forwarded-for'] ||
        headers['x-real-ip'] ||
        headers['cf-connecting-ip'] ||
        'unknown') as string;
}