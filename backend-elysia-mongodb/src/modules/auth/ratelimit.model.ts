import mongoose from "mongoose";

export interface IRateLimit {
    _id: string;
    identifier: string; // IP address หรือ user ID
    endpoint: string; // endpoint ที่ถูก rate limit
    attempts: number;
    resetTime: Date;
    isBlocked: boolean;
    createdAt: Date;
    updatedAt: Date;
}

const rateLimitSchema = new mongoose.Schema<IRateLimit>(
    {
        _id: { type: String, default: () => new mongoose.Types.ObjectId().toString() },
        identifier: {
            type: String,
            required: true,
            index: true,
        },
        endpoint: {
            type: String,
            required: true,
        },
        attempts: {
            type: Number,
            default: 1,
        },
        resetTime: {
            type: Date,
            required: true,
            index: { expireAfterSeconds: 0 }, // MongoDB จะลบ document อัตโนมัติ
        },
        isBlocked: {
            type: Boolean,
            default: false,
        },
    },
    {
        timestamps: true,
        versionKey: false,
    },
);

// Compound index สำหรับการค้นหาที่มีประสิทธิภาพ
// rateLimitSchema.index({ identifier: 1, endpoint: 1 }, { unique: true });
// rateLimitSchema.index({ resetTime: 1 });

export const RateLimit = mongoose.model<IRateLimit>("RateLimit", rateLimitSchema);