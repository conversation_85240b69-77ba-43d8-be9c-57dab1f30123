import { Elysia, t } from "elysia";
import { TokenBlacklistService } from "./blacklist.service";
import { RateLimitService } from "./ratelimit.service";
import { SessionService } from "./session.service";
import { userAuthGuard } from "@/core/plugin";

/**
 * Security Monitoring Routes
 * สำหรับ Admin ในการติดตามและจัดการความปลอดภัย
 */
export const securityRoute = new Elysia({ prefix: "/security" })
    .use(userAuthGuard)

    // ดูสถิติ Token Blacklist
    .get("/blacklist/stats", async () => {
        const stats = await TokenBlacklistService.getBlacklistStats();

        return {
            success: true,
            data: stats,
        };
    })

    // ดูสถิติ Rate Limiting
    .get("/ratelimit/stats", async () => {
        const stats = await RateLimitService.getRateLimitStats();

        return {
            success: true,
            data: stats,
        };
    })

    // ดูสถิติ Sessions
    .get("/sessions/stats", async () => {
        const stats = await SessionService.getSessionStats();

        return {
            success: true,
            data: stats,
        };
    })

    // รีเซ็ต rate limit สำหรับ IP หรือ user
    .post("/ratelimit/reset", async ({ body }) => {
        const { identifier, endpoint } = body;

        if (endpoint) {
            await RateLimitService.resetRateLimit(identifier, endpoint);
        } else {
            await RateLimitService.resetAllRateLimits(identifier);
        }

        return {
            success: true,
            message: "รีเซ็ต rate limit สำเร็จ",
        };
    }, {
        body: t.Object({
            identifier: t.String(),
            endpoint: t.Optional(t.String()),
        }),
    })

    // ยกเลิก sessions ทั้งหมดของ user
    .post("/sessions/revoke-user", async ({ body }) => {
        const { userId } = body;

        const revokedCount = await SessionService.revokeAllSessions(userId);

        return {
            success: true,
            message: `ยกเลิก ${revokedCount} sessions สำเร็จ`,
            data: { revokedCount },
        };
    }, {
        body: t.Object({
            userId: t.String(),
        }),
    })

    // ดู sessions ของ user
    .get("/sessions/user/:userId", async ({ params }) => {
        const sessions = await SessionService.getUserSessions(params.userId);

        return {
            success: true,
            data: sessions,
        };
    })

    // Cleanup expired data
    .post("/cleanup", async () => {
        const [blacklistCleaned, rateLimitCleaned, sessionsCleaned] = await Promise.all([
            TokenBlacklistService.cleanupExpiredTokens(),
            RateLimitService.cleanupExpiredRecords(),
            SessionService.cleanupExpiredSessions(),
        ]);

        return {
            success: true,
            message: "ทำความสะอาดข้อมูลสำเร็จ",
            data: {
                blacklistCleaned,
                rateLimitCleaned,
                sessionsCleaned,
            },
        };
    })

    // Security overview dashboard
    .get("/overview", async () => {
        const [blacklistStats, rateLimitStats, sessionStats] = await Promise.all([
            TokenBlacklistService.getBlacklistStats(),
            RateLimitService.getRateLimitStats(),
            SessionService.getSessionStats(),
        ]);

        return {
            success: true,
            data: {
                blacklist: blacklistStats,
                rateLimit: rateLimitStats,
                sessions: sessionStats,
                timestamp: new Date().toISOString(),
            },
        };
    });