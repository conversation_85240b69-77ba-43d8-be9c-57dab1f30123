import mongoose from "mongoose";

export interface ITokenBlacklist {
    _id: string;
    token: string;
    tokenType: 'access' | 'refresh';
    userId: string;
    reason: 'logout' | 'revoked' | 'expired';
    expiresAt: Date;
    createdAt: Date;
}

const tokenBlacklistSchema = new mongoose.Schema<ITokenBlacklist>(
    {
        _id: { type: String, default: () => new mongoose.Types.ObjectId().toString() },
        token: {
            type: String,
            required: true,
            unique: true,
            index: true, // Index สำหรับการค้นหาที่เร็ว
        },
        tokenType: {
            type: String,
            enum: ['access', 'refresh'],
            required: true,
        },
        userId: {
            type: String,
            required: true,
            index: true,
        },
        reason: {
            type: String,
            enum: ['logout', 'revoked', 'expired'],
            default: 'logout',
        },
        expiresAt: {
            type: Date,
            required: true,
            index: { expireAfterSeconds: 0 }, // MongoDB จะลบ document อัตโนมัติเมื่อหมดอายุ
        },
    },
    {
        timestamps: true,
        versionKey: false,
    },
);

// Compound index สำหรับการค้นหาที่มีประสิทธิภาพ
tokenBlacklistSchema.index({ token: 1, expiresAt: 1 });
tokenBlacklistSchema.index({ userId: 1, tokenType: 1 });

export const TokenBlacklist = mongoose.model<ITokenBlacklist>("TokenBlacklist", tokenBlacklistSchema);