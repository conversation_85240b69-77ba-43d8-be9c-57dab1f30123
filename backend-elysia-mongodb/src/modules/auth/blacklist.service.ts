import { TokenBlacklist, type ITokenBlacklist } from "./blacklist.model";

export class TokenBlacklistService {
    /**
     * เพิ่ม token เข้า blacklist
     */
    static async addToBlacklist(
        token: string,
        tokenType: 'access' | 'refresh',
        userId: string,
        expiresAt: Date,
        reason: 'logout' | 'revoked' | 'expired' = 'logout'
    ): Promise<void> {
        try {
            await TokenBlacklist.create({
                token,
                tokenType,
                userId,
                reason,
                expiresAt,
            });
        } catch (error) {
            // ถ้า token มีอยู่แล้วใน blacklist ให้ ignore error
            if (error instanceof Error && error.message.includes('duplicate key')) {
                return;
            }
            throw error;
        }
    }

    /**
     * ตรวจสอบว่า token อยู่ใน blacklist หรือไม่
     */
    static async isTokenBlacklisted(token: string): Promise<boolean> {
        const blacklistedToken = await TokenBlacklist.findOne({
            token,
            expiresAt: { $gt: new Date() }, // ยังไม่หมดอายุ
        });

        return !!blacklistedToken;
    }

    /**
     * เพิ่ม refresh token ทั้งหมดของ user เข้า blacklist (เมื่อ logout all devices)
     */
    static async blacklistAllUserTokens(
        userId: string,
        reason: 'logout' | 'revoked' = 'logout'
    ): Promise<void> {
        // หา refresh tokens ทั้งหมดของ user จาก database
        const { User } = await import("../user/user.model");
        const user = await User.findById(userId);

        if (user && user.refreshToken) {
            // เพิ่ม refresh token ปัจจุบันเข้า blacklist
            const refreshTokenExpiry = new Date();
            refreshTokenExpiry.setDate(refreshTokenExpiry.getDate() + 30); // 30 วัน

            await this.addToBlacklist(
                user.refreshToken,
                'refresh',
                userId,
                refreshTokenExpiry,
                reason
            );
        }
    }

    /**
     * ลบ tokens ที่หมดอายุออกจาก blacklist (cleanup job)
     */
    static async cleanupExpiredTokens(): Promise<number> {
        const result = await TokenBlacklist.deleteMany({
            expiresAt: { $lt: new Date() }
        });

        return result.deletedCount || 0;
    }

    /**
     * ดูสถิติ blacklist
     */
    static async getBlacklistStats(): Promise<{
        totalBlacklisted: number;
        accessTokens: number;
        refreshTokens: number;
        expiredTokens: number;
    }> {
        const now = new Date();

        const [total, access, refresh, expired] = await Promise.all([
            TokenBlacklist.countDocuments({}),
            TokenBlacklist.countDocuments({ tokenType: 'access' }),
            TokenBlacklist.countDocuments({ tokenType: 'refresh' }),
            TokenBlacklist.countDocuments({ expiresAt: { $lt: now } }),
        ]);

        return {
            totalBlacklisted: total,
            accessTokens: access,
            refreshTokens: refresh,
            expiredTokens: expired,
        };
    }
}