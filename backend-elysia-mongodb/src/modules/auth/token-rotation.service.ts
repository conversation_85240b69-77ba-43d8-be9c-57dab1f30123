import { TokenBlacklistService } from "./blacklist.service";
import { UserService } from "../user/user.service";

export interface TokenRotationResult {
    accessToken: string;
    refreshToken: string;
    oldTokensBlacklisted: boolean;
}

/**
 * Token Rotation Service
 * 
 * Token Rotation คือการสร้าง tokens ใหม่ทุกครั้งที่มีการใช้ refresh token
 * และเพิ่ม tokens เก่าเข้า blacklist เพื่อป้องกันการใช้งานซ้ำ
 * 
 * ประโยชน์:
 * 1. ลดความเสี่ยงจากการ token hijacking
 * 2. จำกัดอายุการใช้งานของ tokens
 * 3. ตรวจจับการใช้งาน tokens ที่ถูกขโมย
 */
export class TokenRotationService {
    /**
     * หมุนเวียน tokens (สร้างใหม่และ blacklist เก่า)
     */
    static async rotateTokens(
        userId: string,
        oldAccessToken: string,
        oldRefreshToken: string,
        jwtSign: (payload: any) => Promise<string>,
        refreshJwtSign: (payload: any) => Promise<string>
    ): Promise<TokenRotationResult> {
        try {
            // 1. สร้าง tokens ใหม่
            const newAccessToken = await jwtSign({ userId });
            const newRefreshToken = await refreshJwtSign({ userId });

            // 2. อัปเดต refresh token ใน database
            await UserService.updateRefreshToken(userId, newRefreshToken);

            // 3. เพิ่ม tokens เก่าเข้า blacklist
            const now = new Date();
            const accessTokenExpiry = new Date(now.getTime() + 60 * 60 * 1000); // 1 ชั่วโมง
            const refreshTokenExpiry = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000); // 30 วัน

            await Promise.all([
                TokenBlacklistService.addToBlacklist(
                    oldAccessToken,
                    'access',
                    userId,
                    accessTokenExpiry,
                    'revoked'
                ),
                TokenBlacklistService.addToBlacklist(
                    oldRefreshToken,
                    'refresh',
                    userId,
                    refreshTokenExpiry,
                    'revoked'
                ),
            ]);

            return {
                accessToken: newAccessToken,
                refreshToken: newRefreshToken,
                oldTokensBlacklisted: true,
            };

        } catch (error) {
            console.error('Token rotation failed:', error);
            throw new Error('ไม่สามารถหมุนเวียน tokens ได้');
        }
    }

    /**
     * ตรวจจับการใช้ refresh token ที่ถูก revoke (Refresh Token Reuse Detection)
     * 
     * ถ้ามีการใช้ refresh token ที่อยู่ใน blacklist แสดงว่าอาจมีการโจรกรรม token
     * ระบบจะ revoke tokens ทั้งหมดของ user นั้น
     */
    static async detectRefreshTokenReuse(
        refreshToken: string,
        userId: string
    ): Promise<{ isReuse: boolean; action: 'allow' | 'revoke_all' }> {
        // ตรวจสอบว่า refresh token อยู่ใน blacklist หรือไม่
        const isBlacklisted = await TokenBlacklistService.isTokenBlacklisted(refreshToken);

        if (isBlacklisted) {
            // พบการใช้ token ที่ถูก revoke แล้ว - อาจมีการโจรกรรม
            console.warn(`Refresh token reuse detected for user ${userId}`);

            // Revoke tokens ทั้งหมดของ user
            await TokenBlacklistService.blacklistAllUserTokens(userId, 'revoked');

            return {
                isReuse: true,
                action: 'revoke_all'
            };
        }

        return {
            isReuse: false,
            action: 'allow'
        };
    }

    /**
     * สร้าง sliding session (ขยายอายุ session อัตโนมัติ)
     * 
     * ถ้า user ใช้งานระบบอย่างต่อเนื่อง จะขยายอายุ session ให้อัตโนมัติ
     */
    static async slidingSession(
        userId: string,
        lastActivity: Date,
        slidingWindowMinutes: number = 30
    ): Promise<{ shouldExtend: boolean; newExpiry?: Date }> {
        const now = new Date();
        const timeSinceLastActivity = now.getTime() - lastActivity.getTime();
        const slidingWindowMs = slidingWindowMinutes * 60 * 1000;

        // ถ้าใช้งานภายในช่วงเวลาที่กำหนด ให้ขยายอายุ
        if (timeSinceLastActivity < slidingWindowMs) {
            const newExpiry = new Date(now.getTime() + slidingWindowMs);

            return {
                shouldExtend: true,
                newExpiry
            };
        }

        return {
            shouldExtend: false
        };
    }
}