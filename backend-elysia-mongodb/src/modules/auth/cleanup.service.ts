import { TokenBlacklistService } from "./blacklist.service";
import { RateLimitService } from "./ratelimit.service";
import { SessionService } from "./session.service";

/**
 * Cleanup Service
 * สำหรับทำความสะอาดข้อมูลที่หมดอายุอัตโนมัติ
 */
export class CleanupService {
    private static isRunning = false;
    private static intervalId: NodeJS.Timeout | null = null;

    /**
     * เริ่มต้น cleanup jobs อัตโนมัติ
     */
    static startCleanupJobs(): void {
        if (this.isRunning) {
            console.log('Cleanup jobs are already running');
            return;
        }

        console.log('Starting security cleanup jobs...');
        this.isRunning = true;

        // รันทุก 1 ชั่วโมง
        this.intervalId = setInterval(async () => {
            await this.runCleanup();
        }, 60 * 60 * 1000); // 1 hour

        // รันครั้งแรกทันที
        this.runCleanup();
    }

    /**
     * หยุด cleanup jobs
     */
    static stopCleanupJobs(): void {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
        this.isRunning = false;
        console.log('Cleanup jobs stopped');
    }

    /**
     * รัน cleanup ทั้งหมด
     */
    static async runCleanup(): Promise<{
        blacklistCleaned: number;
        rateLimitCleaned: number;
        sessionsCleaned: number;
        timestamp: string;
    }> {
        const startTime = Date.now();
        console.log('Running security cleanup...');

        try {
            const [blacklistCleaned, rateLimitCleaned, sessionsCleaned] = await Promise.all([
                TokenBlacklistService.cleanupExpiredTokens(),
                RateLimitService.cleanupExpiredRecords(),
                SessionService.cleanupExpiredSessions(),
            ]);

            const duration = Date.now() - startTime;
            const result = {
                blacklistCleaned,
                rateLimitCleaned,
                sessionsCleaned,
                timestamp: new Date().toISOString(),
            };

            console.log(`Cleanup completed in ${duration}ms:`, result);
            return result;

        } catch (error) {
            console.error('Cleanup failed:', error);
            throw error;
        }
    }

    /**
     * ทำความสะอาดข้อมูลเฉพาะ blacklist
     */
    static async cleanupBlacklist(): Promise<number> {
        return await TokenBlacklistService.cleanupExpiredTokens();
    }

    /**
     * ทำความสะอาดข้อมูลเฉพาะ rate limit
     */
    static async cleanupRateLimit(): Promise<number> {
        return await RateLimitService.cleanupExpiredRecords();
    }

    /**
     * ทำความสะอาดข้อมูลเฉพาะ sessions
     */
    static async cleanupSessions(): Promise<number> {
        return await SessionService.cleanupExpiredSessions();
    }

    /**
     * ตรวจสอบสถานะ cleanup jobs
     */
    static getStatus(): {
        isRunning: boolean;
        nextRun?: string;
    } {
        return {
            isRunning: this.isRunning,
            nextRun: this.isRunning
                ? new Date(Date.now() + 60 * 60 * 1000).toISOString()
                : undefined,
        };
    }
}