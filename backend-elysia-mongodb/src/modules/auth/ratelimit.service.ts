import { RateLimit, type IRateLimit } from "./ratelimit.model";

export interface RateLimitConfig {
    maxAttempts: number;
    windowMs: number; // ระยะเวลาใน milliseconds
    blockDurationMs?: number; // ระยะเวลาที่ถูกบล็อก (ถ้าไม่ระบุจะใช้ windowMs)
}

export interface RateLimitResult {
    allowed: boolean;
    remaining: number;
    resetTime: Date;
    isBlocked: boolean;
}

export class RateLimitService {
    // Rate limit configurations สำหรับ endpoints ต่างๆ
    private static readonly configs: Record<string, RateLimitConfig> = {
        '/users/signin': {
            maxAttempts: 5,
            windowMs: 15 * 60 * 1000, // 15 นาที
            blockDurationMs: 30 * 60 * 1000, // บล็อก 30 นาที
        },
        '/users/signup': {
            maxAttempts: 3,
            windowMs: 60 * 60 * 1000, // 1 ชั่วโมง
        },
        '/users/forgot-password': {
            maxAttempts: 3,
            windowMs: 60 * 60 * 1000, // 1 ชั่วโมง
        },
        '/users/refresh-token': {
            maxAttempts: 10,
            windowMs: 5 * 60 * 1000, // 5 นาที
        },
    };

    /**
     * ตรวจสอบและอัปเดต rate limit
     */
    static async checkRateLimit(
        identifier: string,
        endpoint: string,
        customConfig?: RateLimitConfig
    ): Promise<RateLimitResult> {
        const config = customConfig || this.configs[endpoint];

        if (!config) {
            // ถ้าไม่มี config สำหรับ endpoint นี้ ให้ผ่านได้
            return {
                allowed: true,
                remaining: 999,
                resetTime: new Date(Date.now() + 60000),
                isBlocked: false,
            };
        }

        const now = new Date();
        const resetTime = new Date(now.getTime() + config.windowMs);

        try {
            // หาหรือสร้าง rate limit record
            let rateLimitRecord = await RateLimit.findOne({
                identifier,
                endpoint,
                resetTime: { $gt: now },
            });

            if (!rateLimitRecord) {
                // สร้าง record ใหม่
                rateLimitRecord = await RateLimit.create({
                    identifier,
                    endpoint,
                    attempts: 1,
                    resetTime,
                    isBlocked: false,
                });

                return {
                    allowed: true,
                    remaining: config.maxAttempts - 1,
                    resetTime,
                    isBlocked: false,
                };
            }

            // ตรวจสอบว่าถูกบล็อกอยู่หรือไม่
            if (rateLimitRecord.isBlocked) {
                return {
                    allowed: false,
                    remaining: 0,
                    resetTime: rateLimitRecord.resetTime,
                    isBlocked: true,
                };
            }

            // เพิ่มจำนวน attempts
            rateLimitRecord.attempts += 1;

            // ตรวจสอบว่าเกินขีดจำกัดหรือไม่
            if (rateLimitRecord.attempts > config.maxAttempts) {
                rateLimitRecord.isBlocked = true;

                // ถ้ามี blockDurationMs ให้ขยายเวลา reset
                if (config.blockDurationMs) {
                    rateLimitRecord.resetTime = new Date(now.getTime() + config.blockDurationMs);
                }
            }

            await rateLimitRecord.save();

            return {
                allowed: rateLimitRecord.attempts <= config.maxAttempts,
                remaining: Math.max(0, config.maxAttempts - rateLimitRecord.attempts),
                resetTime: rateLimitRecord.resetTime,
                isBlocked: rateLimitRecord.isBlocked,
            };

        } catch (error) {
            console.error('Rate limit check error:', error);
            // ถ้าเกิด error ให้ผ่านได้ (fail open)
            return {
                allowed: true,
                remaining: config.maxAttempts,
                resetTime,
                isBlocked: false,
            };
        }
    }

    /**
     * รีเซ็ต rate limit สำหรับ identifier และ endpoint
     */
    static async resetRateLimit(identifier: string, endpoint: string): Promise<void> {
        await RateLimit.deleteMany({ identifier, endpoint });
    }

    /**
     * รีเซ็ต rate limit ทั้งหมดสำหรับ identifier
     */
    static async resetAllRateLimits(identifier: string): Promise<void> {
        await RateLimit.deleteMany({ identifier });
    }

    /**
     * ดูสถิติ rate limiting
     */
    static async getRateLimitStats(): Promise<{
        totalRecords: number;
        blockedRecords: number;
        topEndpoints: Array<{ endpoint: string; count: number }>;
        topIdentifiers: Array<{ identifier: string; count: number }>;
    }> {
        const [total, blocked, endpoints, identifiers] = await Promise.all([
            RateLimit.countDocuments({}),
            RateLimit.countDocuments({ isBlocked: true }),
            RateLimit.aggregate([
                { $group: { _id: "$endpoint", count: { $sum: 1 } } },
                { $sort: { count: -1 } },
                { $limit: 10 },
                { $project: { endpoint: "$_id", count: 1, _id: 0 } },
            ]),
            RateLimit.aggregate([
                { $group: { _id: "$identifier", count: { $sum: 1 } } },
                { $sort: { count: -1 } },
                { $limit: 10 },
                { $project: { identifier: "$_id", count: 1, _id: 0 } },
            ]),
        ]);

        return {
            totalRecords: total,
            blockedRecords: blocked,
            topEndpoints: endpoints,
            topIdentifiers: identifiers,
        };
    }

    /**
     * ลบ records ที่หมดอายุ (cleanup job)
     */
    static async cleanupExpiredRecords(): Promise<number> {
        const result = await RateLimit.deleteMany({
            resetTime: { $lt: new Date() },
        });

        return result.deletedCount || 0;
    }
}