import { UserSession, type IUserSession } from "./session.model";
import { generateToken } from "@/core/utils/genId";

export interface CreateSessionData {
    userId: string;
    userAgent: string;
    ipAddress: string;
    refreshToken: string;
    expiresAt: Date;
}

export interface SessionInfo {
    sessionId: string;
    deviceInfo: {
        browser: string;
        os: string;
        device: string;
    };
    location: {
        ipAddress: string;
        country?: string;
        city?: string;
    };
    loginTime: Date;
    lastActivity: Date;
    isActive: boolean;
    isCurrent: boolean;
}

/**
 * Session Management Service
 * 
 * Session Management คือการจัดการ sessions ของ users ที่ login เข้าระบบ
 * ช่วยในการ:
 * 1. ติดตาม devices ที่ user login อยู่
 * 2. จำกัดจำนวน concurrent sessions
 * 3. ตรวจจับการ login ที่ผิดปกติ
 * 4. ให้ user สามารถ logout จาก devices อื่นได้
 */
export class SessionService {
    private static readonly MAX_CONCURRENT_SESSIONS = 5; // จำกัด 5 sessions ต่อ user

    /**
     * สร้าง session ใหม่
     */
    static async createSession(data: CreateSessionData): Promise<string> {
        const sessionId = generateToken();

        // Parse user agent เพื่อดึงข้อมูล device
        const deviceInfo = this.parseUserAgent(data.userAgent);

        // ตรวจสอบจำนวน active sessions
        const activeSessions = await UserSession.countDocuments({
            userId: data.userId,
            isActive: true,
        });

        // ถ้าเกินจำนวนที่กำหนด ให้ลบ session เก่าที่สุด
        if (activeSessions >= this.MAX_CONCURRENT_SESSIONS) {
            await this.removeOldestSession(data.userId);
        }

        // สร้าง session ใหม่
        await UserSession.create({
            userId: data.userId,
            sessionId,
            deviceInfo,
            location: {
                ipAddress: data.ipAddress,
                // TODO: เพิ่ม geolocation lookup
            },
            refreshToken: data.refreshToken,
            expiresAt: data.expiresAt,
        });

        return sessionId;
    }

    /**
     * อัปเดต last activity ของ session
     */
    static async updateSessionActivity(sessionId: string): Promise<void> {
        await UserSession.updateOne(
            { sessionId, isActive: true },
            {
                lastActivity: new Date(),
                // ขยายอายุ session อีก 30 วัน
                expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
            }
        );
    }

    /**
     * ดึงข้อมูล sessions ทั้งหมดของ user
     */
    static async getUserSessions(userId: string, currentSessionId?: string): Promise<SessionInfo[]> {
        const sessions = await UserSession.find({
            userId,
            isActive: true,
        }).sort({ lastActivity: -1 });

        return sessions.map(session => ({
            sessionId: session.sessionId,
            deviceInfo: session.deviceInfo,
            location: session.location,
            loginTime: session.loginTime,
            lastActivity: session.lastActivity,
            isActive: session.isActive,
            isCurrent: session.sessionId === currentSessionId,
        }));
    }

    /**
     * ยกเลิก session
     */
    static async revokeSession(sessionId: string, userId?: string): Promise<boolean> {
        const query: any = { sessionId };
        if (userId) {
            query.userId = userId;
        }

        const result = await UserSession.updateOne(
            query,
            { isActive: false }
        );

        return result.modifiedCount > 0;
    }

    /**
     * ยกเลิก sessions ทั้งหมดของ user ยกเว้น session ปัจจุบัน
     */
    static async revokeAllOtherSessions(userId: string, currentSessionId: string): Promise<number> {
        const result = await UserSession.updateMany(
            {
                userId,
                sessionId: { $ne: currentSessionId },
                isActive: true,
            },
            { isActive: false }
        );

        return result.modifiedCount || 0;
    }

    /**
     * ยกเลิก sessions ทั้งหมดของ user
     */
    static async revokeAllSessions(userId: string): Promise<number> {
        const result = await UserSession.updateMany(
            { userId, isActive: true },
            { isActive: false }
        );

        return result.modifiedCount || 0;
    }

    /**
     * ตรวจจับการ login ที่ผิดปกติ
     */
    static async detectSuspiciousLogin(
        userId: string,
        ipAddress: string,
        userAgent: string
    ): Promise<{
        isSuspicious: boolean;
        reasons: string[];
        shouldRequire2FA: boolean;
    }> {
        const reasons: string[] = [];
        let isSuspicious = false;

        // ดึง sessions ล่าสุด 10 รายการ
        const recentSessions = await UserSession.find({
            userId,
            isActive: true,
        })
            .sort({ lastActivity: -1 })
            .limit(10);

        if (recentSessions.length > 0) {
            // ตรวจสอบ IP address ใหม่
            const knownIPs = recentSessions.map(s => s.location.ipAddress);
            if (!knownIPs.includes(ipAddress)) {
                reasons.push('IP address ใหม่');
                isSuspicious = true;
            }

            // ตรวจสอบ device ใหม่
            const deviceInfo = this.parseUserAgent(userAgent);
            const knownDevices = recentSessions.map(s =>
                `${s.deviceInfo.browser}-${s.deviceInfo.os}`
            );
            const currentDevice = `${deviceInfo.browser}-${deviceInfo.os}`;

            if (!knownDevices.includes(currentDevice)) {
                reasons.push('Device ใหม่');
                isSuspicious = true;
            }

            // ตรวจสอบการ login หลายครั้งในเวลาสั้น
            const recentLogins = recentSessions.filter(s =>
                Date.now() - s.loginTime.getTime() < 5 * 60 * 1000 // 5 นาทีที่แล้ว
            );

            if (recentLogins.length > 3) {
                reasons.push('Login หลายครั้งในเวลาสั้น');
                isSuspicious = true;
            }
        }

        return {
            isSuspicious,
            reasons,
            shouldRequire2FA: isSuspicious && reasons.length >= 2,
        };
    }

    /**
     * ลบ session เก่าที่สุด
     */
    private static async removeOldestSession(userId: string): Promise<void> {
        const oldestSession = await UserSession.findOne({
            userId,
            isActive: true,
        }).sort({ lastActivity: 1 });

        if (oldestSession) {
            await UserSession.updateOne(
                { _id: oldestSession._id },
                { isActive: false }
            );
        }
    }

    /**
     * Parse User Agent เพื่อดึงข้อมูล device
     */
    private static parseUserAgent(userAgent: string): {
        browser: string;
        os: string;
        device: string;
    } {
        // Simple user agent parsing (ในการใช้งานจริงควรใช้ library เช่น ua-parser-js)
        let browser = 'Unknown';
        let os = 'Unknown';
        let device = 'Desktop';

        // Browser detection
        if (userAgent.includes('Chrome')) browser = 'Chrome';
        else if (userAgent.includes('Firefox')) browser = 'Firefox';
        else if (userAgent.includes('Safari')) browser = 'Safari';
        else if (userAgent.includes('Edge')) browser = 'Edge';

        // OS detection
        if (userAgent.includes('Windows')) os = 'Windows';
        else if (userAgent.includes('Mac')) os = 'macOS';
        else if (userAgent.includes('Linux')) os = 'Linux';
        else if (userAgent.includes('Android')) os = 'Android';
        else if (userAgent.includes('iOS')) os = 'iOS';

        // Device detection
        if (userAgent.includes('Mobile') || userAgent.includes('Android')) device = 'Mobile';
        else if (userAgent.includes('Tablet') || userAgent.includes('iPad')) device = 'Tablet';

        return { browser, os, device };
    }

    /**
     * ดูสถิติ sessions
     */
    static async getSessionStats(): Promise<{
        totalActiveSessions: number;
        totalUsers: number;
        averageSessionsPerUser: number;
        topDevices: Array<{ device: string; count: number }>;
        topBrowsers: Array<{ browser: string; count: number }>;
    }> {
        const [totalActive, userStats, deviceStats, browserStats] = await Promise.all([
            UserSession.countDocuments({ isActive: true }),
            UserSession.aggregate([
                { $match: { isActive: true } },
                { $group: { _id: "$userId" } },
                { $count: "totalUsers" },
            ]),
            UserSession.aggregate([
                { $match: { isActive: true } },
                { $group: { _id: "$deviceInfo.device", count: { $sum: 1 } } },
                { $sort: { count: -1 } },
                { $limit: 5 },
                { $project: { device: "$_id", count: 1, _id: 0 } },
            ]),
            UserSession.aggregate([
                { $match: { isActive: true } },
                { $group: { _id: "$deviceInfo.browser", count: { $sum: 1 } } },
                { $sort: { count: -1 } },
                { $limit: 5 },
                { $project: { browser: "$_id", count: 1, _id: 0 } },
            ]),
        ]);

        const totalUsers = userStats[0]?.totalUsers || 0;

        return {
            totalActiveSessions: totalActive,
            totalUsers,
            averageSessionsPerUser: totalUsers > 0 ? totalActive / totalUsers : 0,
            topDevices: deviceStats,
            topBrowsers: browserStats,
        };
    }

    /**
     * ลบ sessions ที่หมดอายุ (cleanup job)
     */
    static async cleanupExpiredSessions(): Promise<number> {
        const result = await UserSession.deleteMany({
            $or: [
                { expiresAt: { $lt: new Date() } },
                { isActive: false, updatedAt: { $lt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } } // ลบ inactive sessions ที่เก่ากว่า 7 วัน
            ]
        });

        return result.deletedCount || 0;
    }
}