import { Elysia, error, t } from "elysia";
import {
	customerChangePasswordSchema,
	customerEmailVerificationSchema,
	customerForgotPasswordSchema,
	customerSigninSchema,
	customerRefreshTokenSchema,
	customerSignupSchema,
	customerResetPasswordSchema,
	customerUpdateProfileSchema,
} from "./customer.schemas";
import { CustomerService } from "./customer.service";
import { customerJwtPlugin, customerAuthGuard } from "@/core/plugin";

export const customerRoute = new Elysia({ prefix: "/customers" })
	.use(customerJwtPlugin)

	// Signup
	.post(
		"/signup",
		async ({ body, customerJwt, customerRefreshJwt }) => {
			const data = customerSignupSchema.parse(body);
			const result = await CustomerService.signup(data);

			// Generate tokens
			const accessToken = await customerJwt.sign({ customerId: result.customer._id });
			const refreshToken = await customerRefreshJwt.sign({ customerId: result.customer._id });

			// Save refresh token to customer
			await CustomerService.updateRefreshToken(result.customer._id, refreshToken);

			return {
				success: true,
				message: "ลงทะเบียนสำเร็จ กรุณายืนยันอีเมล",
				data: {
					customer: result.customer,
					accessToken,
					refreshToken,
				},
			};
		},
		{
			body: t.Object({
				email: t.String(),
				password: t.String(),
				confirmPassword: t.String(),
				firstName: t.String(),
				lastName: t.String(),
				phone: t.Optional(t.String()),
			}),
		},
	)

	// Signin
	.post(
		"/signin",
		async ({ body, customerJwt, customerRefreshJwt }) => {
			const data = customerSigninSchema.parse(body);
			const result = await CustomerService.signin(data);

			// Generate tokens
			const accessToken = await customerJwt.sign({ customerId: result.customer._id });
			const refreshToken = await customerRefreshJwt.sign({ customerId: result.customer._id });

			// Save refresh token to customer
			await CustomerService.updateRefreshToken(result.customer._id, refreshToken);

			return {
				success: true,
				message: "เข้าสู่ระบบสำเร็จ",
				data: {
					customer: result.customer,
					accessToken,
					refreshToken,
				},
			};
		},
		{
			body: t.Object({
				email: t.String(),
				password: t.String(),
			}),
		},
	)

	// Verify email
	.post(
		"/verify-email",
		async ({ body }) => {
			const data = customerEmailVerificationSchema.parse(body);
			const result = await CustomerService.verifyEmail(data);

			return {
				success: true,
				message: result.message,
			};
		},
		{
			body: t.Object({
				token: t.String(),
			}),
		},
	)

	// Forgot password
	.post(
		"/forgot-password",
		async ({ body }) => {
			const data = customerForgotPasswordSchema.parse(body);
			const result = await CustomerService.forgotPassword(data);

			return {
				success: true,
				message: result.message,
			};
		},
		{
			body: t.Object({
				email: t.String(),
			}),
		},
	)

	// Reset password
	.post(
		"/reset-password",
		async ({ body }) => {
			const data = customerResetPasswordSchema.parse(body);
			const result = await CustomerService.resetPassword(data);

			return {
				success: true,
				message: result.message,
			};
		},
		{
			body: t.Object({
				token: t.String(),
				password: t.String(),
			}),
		},
	)

	// Refresh token
	.post(
		"/refresh-token",
		async ({ body, customerJwt, customerRefreshJwt }) => {
			const data = customerRefreshTokenSchema.parse(body);

			// Verify refresh token
			const payload = await customerRefreshJwt.verify(data.refreshToken);
			if (!payload || !payload.customerId) {
				throw error(401, { message: "Refresh token ไม่ถูกต้อง" });
			}

			// Generate new tokens
			const accessToken = await customerJwt.sign({ customerId: payload.customerId });
			const newRefreshToken = await customerRefreshJwt.sign({ customerId: payload.customerId });

			// Update refresh token in database
			await CustomerService.updateRefreshToken(payload.customerId as string, newRefreshToken);

			return {
				success: true,
				message: "Token ถูกอัปเดตแล้ว",
				data: {
					accessToken,
					refreshToken: newRefreshToken,
				},
			};
		},
		{
			body: t.Object({
				refreshToken: t.String(),
			}),
		},
	)

	// Protected routes
	.group("/me", (app) =>
		app
			.use(customerAuthGuard)
			.get("/", async ({ customer }) => {
				return {
					success: true,
					data: customer,
				};
			})

			// Update profile
			.put(
				"/",
				async ({ customer, body }) => {
					const data = customerUpdateProfileSchema.parse(body);
					const updatedCustomer = await CustomerService.updateProfile(customer._id, data);

					return {
						success: true,
						message: "อัปเดตโปรไฟล์สำเร็จ",
						data: updatedCustomer,
					};
				},
				{
					body: t.Object({
						firstName: t.Optional(t.String()),
						lastName: t.Optional(t.String()),
						phone: t.Optional(t.String()),
						avatar: t.Optional(t.String()),
						address: t.Optional(
							t.Object({
								street: t.Optional(t.String()),
								city: t.Optional(t.String()),
								province: t.Optional(t.String()),
								postalCode: t.Optional(t.String()),
								country: t.Optional(t.String()),
							}),
						),
						dateOfBirth: t.Optional(t.String()),
						gender: t.Optional(
							t.Union([t.Literal("male"), t.Literal("female"), t.Literal("other")]),
						),
					}),
				},
			)

			// Change password
			.put(
				"/change-password",
				async ({ customer, body }) => {
					const data = customerChangePasswordSchema.parse(body);
					const result = await CustomerService.changePassword(customer._id, data);

					return {
						success: true,
						message: result.message,
					};
				},
				{
					body: t.Object({
						currentPassword: t.String(),
						newPassword: t.String(),
					}),
				},
			)

			// Signout
			.post(
				"/signout",
				async ({ customer }) => {
					const result = await CustomerService.signout(customer._id);

					return {
						success: true,
						message: result.message,
					};
				},
				{
					body: t.Optional(t.Any()),
				},
			),
	)

	// Resend verification email
	.post(
		"/resend-verification",
		async ({ body }) => {
			const { email } = body;
			const result = await CustomerService.resendVerificationEmail(email);

			return {
				success: true,
				message: result.message,
			};
		},
		{
			body: t.Object({
				email: t.String(),
			}),
		},
	);
