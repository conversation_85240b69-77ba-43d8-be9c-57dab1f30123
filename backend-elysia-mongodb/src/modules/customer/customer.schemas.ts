import { z } from "zod";

// Signup schema
export const customerSignupSchema = z
	.object({
		email: z.string().email("รูปแบบอีเมลไม่ถูกต้อง"),
		password: z.string().min(6, "รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร"),
		confirmPassword: z.string(),
		firstName: z.string().min(1, "กรุณากรอกชื่อ"),
		lastName: z.string().min(1, "กรุณากรอกนามสกุล"),
		phone: z.string().optional(),
	})
	.refine((data) => data.password === data.confirmPassword, {
		message: "รหัสผ่านไม่ตรงกัน",
		path: ["confirmPassword"],
	});

// Signin schema
export const customerSigninSchema = z.object({
	email: z.string().email("รูปแบบอีเมลไม่ถูกต้อง"),
	password: z.string().min(1, "กรุณากรอกรหัสผ่าน"),
});

// Email verification schema
export const customerEmailVerificationSchema = z.object({
	token: z.string().min(1, "กรุณากรอก token"),
});

// Forgot password schema
export const customerForgotPasswordSchema = z.object({
	email: z.string().email("รูปแบบอีเมลไม่ถูกต้อง"),
});

// Reset password schema
export const customerResetPasswordSchema = z.object({
	token: z.string().min(1, "กรุณากรอก token"),
	password: z.string().min(6, "รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร"),
});

// Change password schema
export const customerChangePasswordSchema = z.object({
	currentPassword: z.string().min(1, "กรุณากรอกรหัสผ่านปัจจุบัน"),
	newPassword: z.string().min(6, "รหัสผ่านใหม่ต้องมีอย่างน้อย 6 ตัวอักษร"),
});

// Update profile schema
export const customerUpdateProfileSchema = z.object({
	firstName: z.string().min(1, "กรุณากรอกชื่อ").optional(),
	lastName: z.string().min(1, "กรุณากรอกนามสกุล").optional(),
	phone: z.string().optional(),
	avatar: z.string().optional(),
	address: z
		.object({
			street: z.string().optional(),
			city: z.string().optional(),
			province: z.string().optional(),
			postalCode: z.string().optional(),
			country: z.string().optional(),
		})
		.optional(),
	dateOfBirth: z.string().optional(),
	gender: z.enum(["male", "female", "other"]).optional(),
});

// Refresh token schema
export const customerRefreshTokenSchema = z.object({
	refreshToken: z.string().min(1, "กรุณากรอก refresh token"),
});

// Type exports
export type CustomerSignupInput = z.infer<typeof customerSignupSchema>;
export type CustomerSigninInput = z.infer<typeof customerSigninSchema>;
export type CustomerEmailVerificationInput = z.infer<typeof customerEmailVerificationSchema>;
export type CustomerForgotPasswordInput = z.infer<typeof customerForgotPasswordSchema>;
export type CustomerResetPasswordInput = z.infer<typeof customerResetPasswordSchema>;
export type CustomerChangePasswordInput = z.infer<typeof customerChangePasswordSchema>;
export type CustomerUpdateProfileInput = z.infer<typeof customerUpdateProfileSchema>;
export type CustomerRefreshTokenInput = z.infer<typeof customerRefreshTokenSchema>;
