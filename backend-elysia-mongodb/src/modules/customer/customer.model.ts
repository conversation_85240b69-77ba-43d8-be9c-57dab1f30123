import mongoose from "mongoose";
import { generateMedelId } from "@/core/utils/genId";

export interface ICustomer {
	_id: string;
	email: string;
	password: string;
	firstName: string;
	lastName: string;
	phone?: string;
	avatar?: string;
	address?: {
		street?: string;
		city?: string;
		province?: string;
		postalCode?: string;
		country?: string;
	};
	dateOfBirth?: Date;
	gender?: "male" | "female" | "other";
	isEmailVerified: boolean;
	emailVerificationToken?: string;
	emailVerificationExpires?: Date;
	passwordResetToken?: string;
	passwordResetExpires?: Date;
	refreshToken?: string;
	isActive: boolean;
	lastSignin?: Date;
	createdAt: Date;
	updatedAt: Date;
}

const customerSchema = new mongoose.Schema<ICustomer>(
	{
		_id: { type: String, default: () => generateMedelId(5) },
		email: {
			type: String,
			required: true,
			unique: true,
			lowercase: true,
			trim: true,
		},
		password: {
			type: String,
			required: true,
		},
		firstName: {
			type: String,
			required: true,
			trim: true,
		},
		lastName: {
			type: String,
			required: true,
			trim: true,
		},
		phone: {
			type: String,
			trim: true,
		},
		avatar: {
			type: String,
		},
		address: {
			street: { type: String, trim: true },
			city: { type: String, trim: true },
			province: { type: String, trim: true },
			postalCode: { type: String, trim: true },
			country: { type: String, trim: true, default: "Thailand" },
		},
		dateOfBirth: {
			type: Date,
		},
		gender: {
			type: String,
			enum: ["male", "female", "other"],
		},
		isEmailVerified: {
			type: Boolean,
			default: false,
		},
		emailVerificationToken: {
			type: String,
		},
		emailVerificationExpires: {
			type: Date,
		},
		passwordResetToken: {
			type: String,
		},
		passwordResetExpires: {
			type: Date,
		},
		refreshToken: {
			type: String,
		},
		isActive: {
			type: Boolean,
			default: true,
		},
		lastSignin: {
			type: Date,
		},
	},
	{
		timestamps: true,
		versionKey: false,
	},
);

export const Customer = mongoose.model<ICustomer>("Customer", customerSchema);
