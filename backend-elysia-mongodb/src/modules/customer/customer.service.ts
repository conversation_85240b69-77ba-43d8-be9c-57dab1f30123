import * as argon2 from "argon2";
import { generateToken } from "@/core/utils/genId";
import { type ICustomer, Customer } from "./customer.model";
import { sendEmail } from "@/core/utils/email";
import type {
	CustomerChangePasswordInput,
	CustomerEmailVerificationInput,
	CustomerForgotPasswordInput,
	CustomerSigninInput,
	CustomerRefreshTokenInput,
	CustomerSignupInput,
	CustomerResetPasswordInput,
	CustomerUpdateProfileInput,
} from "./customer.schemas";

export class CustomerService {
	// Signup new customer
	static async signup(
		data: CustomerSignupInput,
	): Promise<{ customer: Omit<ICustomer, "password">; verificationToken: string }> {
		const { email, password, firstName, lastName, phone } = data;

		// Check if customer already exists
		const existingCustomer = await Customer.findOne({ email });
		if (existingCustomer) {
			throw new Error("อีเมลนี้ถูกใช้งานแล้ว");
		}

		// Hash password
		const hashedPassword = await argon2.hash(password);

		// Generate email verification token
		const verificationToken = generateToken();
		const verificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

		// Create customer
		const customer = new Customer({
			email,
			password: hashedPassword,
			firstName,
			lastName,
			phone,
			emailVerificationToken: verificationToken,
			emailVerificationExpires: verificationExpires,
		});

		await customer.save();

		// Send verification email
		try {
			await sendEmail({
				to: email,
				subject: "ยืนยันอีเมลของคุณ",
				html: `
					<h2>ยืนยันอีเมลของคุณ</h2>
					<p>สวัสดี ${firstName} ${lastName},</p>
					<p>กรุณาคลิกลิงก์ด้านล่างเพื่อยืนยันอีเมลของคุณ:</p>
					<a href="${process.env.FRONTEND_URL}/verify-email?token=${verificationToken}" 
					   style="background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
						ยืนยันอีเมล
					</a>
					<p>ลิงก์นี้จะหมดอายุใน 24 ชั่วโมง</p>
				`,
			});
		} catch (error) {
			console.error("Failed to send verification email:", error);
		}

		// Return customer without password
		const { password: _, ...customerWithoutPassword } = customer.toObject();
		return { customer: customerWithoutPassword, verificationToken };
	}

	// Signin customer
	static async signin(
		data: CustomerSigninInput,
	): Promise<{ customer: Omit<ICustomer, "password">; accessToken: string; refreshToken: string }> {
		const { email, password } = data;

		// Find customer
		const customer = await Customer.findOne({ email });
		if (!customer) {
			throw new Error("อีเมลหรือรหัสผ่านไม่ถูกต้อง");
		}

		// Check if customer is active
		if (!customer.isActive) {
			throw new Error("บัญชีผู้ใช้ถูกระงับ");
		}

		// Verify password
		const isValidPassword = await argon2.verify(customer.password, password);
		if (!isValidPassword) {
			throw new Error("อีเมลหรือรหัสผ่านไม่ถูกต้อง");
		}

		// Update last signin
		customer.lastSignin = new Date();
		await customer.save();

		// Return customer without password
		const { password: _, ...customerWithoutPassword } = customer.toObject();
		return {
			customer: customerWithoutPassword,
			accessToken: "access_token_placeholder", // Will be set by JWT plugin
			refreshToken: "refresh_token_placeholder", // Will be set by JWT plugin
		};
	}

	// Verify email
	static async verifyEmail(data: CustomerEmailVerificationInput): Promise<{ message: string }> {
		const { token } = data;

		const customer = await Customer.findOne({
			emailVerificationToken: token,
			emailVerificationExpires: { $gt: new Date() },
		});

		if (!customer) {
			throw new Error("Token ไม่ถูกต้องหรือหมดอายุ");
		}

		customer.isEmailVerified = true;
		customer.emailVerificationToken = undefined;
		customer.emailVerificationExpires = undefined;
		await customer.save();

		return { message: "ยืนยันอีเมลสำเร็จ" };
	}

	// Forgot password
	static async forgotPassword(data: CustomerForgotPasswordInput): Promise<{ message: string }> {
		const { email } = data;

		const customer = await Customer.findOne({ email });
		if (!customer) {
			// Don't reveal if email exists or not
			return { message: "หากอีเมลนี้มีอยู่ในระบบ จะได้รับลิงก์รีเซ็ตรหัสผ่าน" };
		}

		// Generate reset token
		const resetToken = generateToken();
		const resetExpires = new Date(Date.now() + 1 * 60 * 60 * 1000); // 1 hour

		customer.passwordResetToken = resetToken;
		customer.passwordResetExpires = resetExpires;
		await customer.save();

		// Send password reset email
		try {
			await sendEmail({
				to: email,
				subject: "รีเซ็ตรหัสผ่าน",
				html: `
					<h2>รีเซ็ตรหัสผ่าน</h2>
					<p>สวัสดี ${customer.firstName} ${customer.lastName},</p>
					<p>กรุณาคลิกลิงก์ด้านล่างเพื่อรีเซ็ตรหัสผ่านของคุณ:</p>
					<a href="${process.env.FRONTEND_URL}/reset-password?token=${resetToken}" 
					   style="background-color: #f44336; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
						รีเซ็ตรหัสผ่าน
					</a>
					<p>ลิงก์นี้จะหมดอายุใน 1 ชั่วโมง</p>
				`,
			});
		} catch (error) {
			console.error("Failed to send password reset email:", error);
		}

		return { message: "หากอีเมลนี้มีอยู่ในระบบ จะได้รับลิงก์รีเซ็ตรหัสผ่าน" };
	}

	// Reset password
	static async resetPassword(data: CustomerResetPasswordInput): Promise<{ message: string }> {
		const { token, password } = data;

		const customer = await Customer.findOne({
			passwordResetToken: token,
			passwordResetExpires: { $gt: new Date() },
		});

		if (!customer) {
			throw new Error("Token ไม่ถูกต้องหรือหมดอายุ");
		}

		// Hash new password
		const hashedPassword = await argon2.hash(password);

		customer.password = hashedPassword;
		customer.passwordResetToken = undefined;
		customer.passwordResetExpires = undefined;
		await customer.save();

		return { message: "รีเซ็ตรหัสผ่านสำเร็จ" };
	}

	// Change password
	static async changePassword(
		customerId: string,
		data: CustomerChangePasswordInput,
	): Promise<{ message: string }> {
		const { currentPassword, newPassword } = data;

		const customer = await Customer.findById(customerId);
		if (!customer) {
			throw new Error("ไม่พบลูกค้า");
		}

		// Verify current password
		const isValidPassword = await argon2.verify(customer.password, currentPassword);
		if (!isValidPassword) {
			throw new Error("รหัสผ่านปัจจุบันไม่ถูกต้อง");
		}

		// Hash new password
		const hashedPassword = await argon2.hash(newPassword);

		customer.password = hashedPassword;
		await customer.save();

		return { message: "เปลี่ยนรหัสผ่านสำเร็จ" };
	}

	// Update profile
	static async updateProfile(
		customerId: string,
		data: CustomerUpdateProfileInput,
	): Promise<Omit<ICustomer, "password">> {
		const customer = await Customer.findById(customerId);
		if (!customer) {
			throw new Error("ไม่พบลูกค้า");
		}

		// Update fields
		if (data.firstName) customer.firstName = data.firstName;
		if (data.lastName) customer.lastName = data.lastName;
		if (data.phone !== undefined) customer.phone = data.phone;
		if (data.avatar !== undefined) customer.avatar = data.avatar;
		if (data.address) {
			customer.address = { ...customer.address, ...data.address };
		}
		if (data.dateOfBirth) customer.dateOfBirth = new Date(data.dateOfBirth);
		if (data.gender) customer.gender = data.gender;

		await customer.save();

		const { password: _, ...customerWithoutPassword } = customer.toObject();
		return customerWithoutPassword;
	}

	// Get profile
	static async getProfile(customerId: string): Promise<Omit<ICustomer, "password">> {
		const customer = await Customer.findById(customerId);
		if (!customer) {
			throw new Error("ไม่พบลูกค้า");
		}

		const { password: _, ...customerWithoutPassword } = customer.toObject();
		return customerWithoutPassword;
	}

	// Update refresh token
	static async updateRefreshToken(customerId: string, refreshToken: string): Promise<void> {
		const customer = await Customer.findById(customerId);
		if (!customer) {
			throw new Error("ไม่พบลูกค้า");
		}

		customer.refreshToken = refreshToken;
		await customer.save();
	}

	// Signout
	static async signout(customerId: string): Promise<{ message: string }> {
		const customer = await Customer.findById(customerId);
		if (!customer) {
			throw new Error("ไม่พบลูกค้า");
		}

		customer.refreshToken = undefined;
		await customer.save();

		return { message: "ออกจากระบบสำเร็จ" };
	}

	// Resend verification email
	static async resendVerificationEmail(email: string): Promise<{ message: string }> {
		const customer = await Customer.findOne({ email });
		if (!customer) {
			throw new Error("ไม่พบลูกค้า");
		}

		if (customer.isEmailVerified) {
			throw new Error("อีเมลได้รับการยืนยันแล้ว");
		}

		// Generate new verification token
		const verificationToken = generateToken();
		const verificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

		customer.emailVerificationToken = verificationToken;
		customer.emailVerificationExpires = verificationExpires;
		await customer.save();

		// Send verification email
		try {
			await sendEmail({
				to: email,
				subject: "ยืนยันอีเมลของคุณ",
				html: `
					<h2>ยืนยันอีเมลของคุณ</h2>
					<p>สวัสดี ${customer.firstName} ${customer.lastName},</p>
					<p>กรุณาคลิกลิงก์ด้านล่างเพื่อยืนยันอีเมลของคุณ:</p>
					<a href="${process.env.FRONTEND_URL}/verify-email?token=${verificationToken}" 
					   style="background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
						ยืนยันอีเมล
					</a>
					<p>ลิงก์นี้จะหมดอายุใน 24 ชั่วโมง</p>
				`,
			});
		} catch (error) {
			console.error("Failed to send verification email:", error);
		}

		return { message: "ส่งอีเมลยืนยันใหม่แล้ว" };
	}

	// Get all customers (for admin)
	static async getAllCustomers(
		page = 1,
		limit = 10,
		search?: string,
	): Promise<{
		customers: Omit<ICustomer, "password">[];
		total: number;
		page: number;
		totalPages: number;
	}> {
		const skip = (page - 1) * limit;
		const query = search
			? {
					$or: [
						{ email: { $regex: search, $options: "i" } },
						{ firstName: { $regex: search, $options: "i" } },
						{ lastName: { $regex: search, $options: "i" } },
					],
				}
			: {};

		const [customers, total] = await Promise.all([
			Customer.find(query).select("-password").sort({ createdAt: -1 }).skip(skip).limit(limit),
			Customer.countDocuments(query),
		]);

		return {
			customers,
			total,
			page,
			totalPages: Math.ceil(total / limit),
		};
	}

	// Toggle customer status (for admin)
	static async toggleCustomerStatus(
		customerId: string,
	): Promise<{ message: string; isActive: boolean }> {
		const customer = await Customer.findById(customerId);
		if (!customer) {
			throw new Error("ไม่พบลูกค้า");
		}

		customer.isActive = !customer.isActive;
		await customer.save();

		return {
			message: customer.isActive ? "เปิดใช้งานบัญชีแล้ว" : "ระงับบัญชีแล้ว",
			isActive: customer.isActive,
		};
	}
}
