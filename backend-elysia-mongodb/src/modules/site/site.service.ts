import { Site, type ISite } from "./site.model";
import { throwError } from "@/core/utils/error";
import { generateMedelId } from "@/core/utils/genId";
import { getPaginationParams, createPaginationResult } from "@/core/utils/pagination";
import { Role } from "../user/role.model";
import { registerDomainVercel } from "@/core/utils/domain";
import { IUser } from "../user/user.model";

export interface PaginationOptions {
	page?: number;
	limit?: number;
}

export interface PaginatedResult<T> {
	data: T[];
	total: number;
	page: number;
	limit: number;
	totalPages: number;
	hasNext: boolean;
	hasPrev: boolean;
}


// แพ็คเกจและราคา
const PACKAGE_PRICES = {
	monthly: { moneyPoint: 99, days: 30 }, 
	yearly: { moneyPoint: 999, days: 365 }, 
	permanent: { moneyPoint: 9999, days: 36500 }, //  (100 ปี)
} as const;

type PackageType = keyof typeof PACKAGE_PRICES;


export function getPackageInfo() {
	return {
		packages: [
			// { id: 'daily', name: 'รายวัน', moneyPoint: PACKAGE_PRICES.daily.moneyPoint, days: PACKAGE_PRICES.daily.days, description: 'เช่าเว็บไซตรายวัน' },
			// { id: 'weekly', name: 'รายสัปดาห์', moneyPoint: PACKAGE_PRICES.weekly.moneyPoint, days: PACKAGE_PRICES.weekly.days, description: 'เช่าเว็บไซตรายสัปดาห์' },
			{
				id: 'monthly',
				name: 'รายเดือน',
				moneyPoint: PACKAGE_PRICES.monthly.moneyPoint,
				days: PACKAGE_PRICES.monthly.days,
				description: 'เช่าเว็บไซตรายเดือน',
			},
			{
				id: 'yearly',
				name: 'รายปี',
				moneyPoint: PACKAGE_PRICES.yearly.moneyPoint,
				days: PACKAGE_PRICES.yearly.days,
				description: 'เช่าเว็บไซตรายปี',
			},
			{
				id: 'permanent',
				name: 'ถาวร',
				moneyPoint: PACKAGE_PRICES.permanent.moneyPoint,
				days: PACKAGE_PRICES.permanent.days,
				description: 'เช่าเว็บไซตถาวร',
			},
		],
	};
}


export class SiteService {
	// Basic CRUD operations
	static async createSite(user: IUser, { name, fullDomain,typeDomain, packageType }: { name: string, fullDomain: string, typeDomain: string, packageType: PackageType }): Promise<any> {
	
		const packageInfo = PACKAGE_PRICES[packageType];
		if (!packageInfo) {
		  console.error('[createSite] Invalid packageType', { packageType });
		  throwError.badRequest('แพ็คเกจไม่ถูกต้อง');
		}
	  
		if (user.moneyPoint < packageInfo.moneyPoint) {
		  console.error('[createSite] Not enough moneyPoint', {
			userMoneyPoint: user.moneyPoint,
			required: packageInfo.moneyPoint,
		  });
		  throwError.badRequest('ยอดเงินไม่เพียงพอ');
		}
	  
		const { resRegisterDomain, resCheck } = await registerDomainVercel(fullDomain);
		console.log('[createSite] resRegisterDomain', resRegisterDomain);
		console.log('[createSite] resCheck', resCheck);
	  
		// ✅ ส่วนของการหักค่าเช่าเว็บไซต์จาก user;
		const expiredAt = packageType === 'permanent'
		  ? new Date(Date.now() + 36500 * 24 * 60 * 60 * 1000)
		  : new Date(Date.now() + packageInfo.days * 24 * 60 * 60 * 1000);
		// user.moneyPoint -= packageInfo.moneyPoint;
		// await user.save();
	  
		let site: any;
	  
		try {
		  site = await Site.create({ name, fullDomain, typeDomain, expiredAt });
	  
		  const { addSiteRole } = await import('@/modules/user/role.service');
		  await addSiteRole(site._id, user._id, 'owner');
		  console.log('[createSite] Owner role created for user:', user._id);
	  
		//   const history = await recordSitePackageHistory({
		// 	userId: user._id,
		// 	siteId: site._id,
		// 	packageType,
		// 	action: 'create',
		// 	expiredAt,
		// 	moneyPointUsed: packageInfo.moneyPoint,
		//   });
		  console.log('[createSite] history', history);
		}
		catch (err: any) {
		  console.error('[createSite] Error creating site', err);
		  if (err.code === 11000) {
			throwError.badRequest(`ไม่สามารถสร้างเว็บไซต์ได้ เนื่องจาก ${fullDomain} ถูกใช้แล้ว`);
		  }
		  throw err;
		}
	  
		// สร้างข้อมูลเริ่มต้น (menu และ page)
		// const defaultContent = await createDefaultSiteContent(site._id, name);
	  
		return {
		  site: site as any,
		//   defaultContent,
		  resRegisterDomain,
		  resCheck,
		};
	}

	static async getUserSites(userId: string, options: PaginationOptions = {}): Promise<PaginatedResult<ISite>> {
		const { page = 1, limit = 10 } = options;
		const skip = (page - 1) * limit;

		// Get total count
		const total = await Site.countDocuments({ ownerId: userId });

		// Get paginated data
		const data = await Site.find({ ownerId: userId })
			.sort({ createdAt: -1 })
			.skip(skip)
			.limit(limit);

		const totalPages = Math.ceil(total / limit);

		return {
			data,
			total,
			page,
			limit,
			totalPages,
			hasNext: page < totalPages,
			hasPrev: page > 1,
		};
	}

	static async getMySites(user: any, paginationParams?: PaginationOptions, search?: string, status?: string) {
		const { page, limit, skip } = getPaginationParams(paginationParams);
		// สร้าง match conditions สำหรับ search และ status
		const matchConditions: any[] = [{ userId: user._id }];
		console.log('matchConditions', matchConditions);
		console.log('search', search);
		console.log('status', status);

		// เพิ่ม search condition ถ้ามี
		if (search) {
			matchConditions.push({
				'siteInfo.name': { $regex: search, $options: 'i' },
			});
		}

		// เพิ่ม status condition ถ้ามี
		if (status) {
			if (status === 'active') {
				matchConditions.push({ 'siteInfo.isActive': true });
			}
			else if (status === 'inactive') {
				matchConditions.push({ 'siteInfo.isActive': false });
			}
		}

		const roles = await Role.aggregate([
			{ $match: { userId: user._id } },
			{
				$lookup: {
					from: 'sites',
					localField: 'siteId',
					foreignField: '_id',
					as: 'siteInfo',
					pipeline: [
						{
							$lookup: {
								from: 'users',
								localField: 'userId',
								foreignField: '_id',
								as: 'ownerInfo',
								pipeline: [
									{
										$project: {
											email: 1,
											firstName: 1,
											lastName: 1,
											moneyPoint: 1,
											goldPoint: 1,
										},
									},
								],
							},
						},
						{ $addFields: { ownerInfo: { $arrayElemAt: ['$ownerInfo', 0] } } },
						{
							$project: {
								_id: 1,
								name: 1,
								typeDomain: 1,
								fullDomain: 1,
								seoSettings: 1,
								isActive: 1,
								expiredAt: 1,
								createdAt: 1,
								updatedAt: 1,
								ownerInfo: 1,
							},
						},
					],
				},
			},
			{ $addFields: { siteInfo: { $arrayElemAt: ['$siteInfo', 0] } } },
			{ $match: { siteInfo: { $ne: null } } },
			// เพิ่ม search และ status filtering
			...(search ? [{ $match: { 'siteInfo.name': { $regex: search, $options: 'i' } } }] : []),
			...(status
				? [
					{
						$match: {
							'siteInfo.isActive': status === 'active' ? true : false,
						},
					},
				]
				: []),
			{ $project: { _id: 1, role: 1, siteId: 1, userId: 1, siteInfo: 1 } },
			{ $sort: { 'siteInfo.createdAt': -1 } },
			{ $skip: skip },
			{ $limit: limit },
		]);
		const totalCount = await Role.aggregate([
			{ $match: { userId: user._id } },
			{
				$lookup: {
					from: 'sites',
					localField: 'siteId',
					foreignField: '_id',
					as: 'siteInfo',
				},
			},
			{ $match: { siteInfo: { $ne: [] } } },
			// เพิ่ม search และ status filtering สำหรับ total count
			...(search ? [{ $match: { 'siteInfo.name': { $regex: search, $options: 'i' } } }] : []),
			...(status
				? [
					{
						$match: {
							'siteInfo.isActive': status === 'active' ? true : false,
						},
					},
				]
				: []),
			{ $count: 'total' },
		]);
		const total = totalCount.length > 0 ? totalCount[0].total : 0;
		const safeSites = roles.map((role: any) => ({
			_id: role.siteInfo._id,
			name: role.siteInfo.name,
			typeDomain: role.siteInfo.typeDomain,
			fullDomain: role.siteInfo.fullDomain,
			seoSettings: role.siteInfo.seoSettings,
			isActive: role.siteInfo.isActive,
			expiredAt: role.siteInfo.expiredAt,
			createdAt: role.siteInfo.createdAt,
			updatedAt: role.siteInfo.updatedAt,
			userRole: role.role,
			owner: role.siteInfo.ownerInfo,
		}));
		const paginationResult = createPaginationResult(page, limit, total);
		return { sites: safeSites, pagination: paginationResult };
	}

	static async getSiteById(siteId: string, userId: string): Promise<ISite> {
		const site = await Site.findOne({ _id: siteId, ownerId: userId });
		if (!site) {
			throw throwError.notFound("ไม่พบเว็บไซต์");
		}
		return site;
	}

	static async updateSite(siteId: string, userId: string, data: any): Promise<ISite> {
		const site = await Site.findOneAndUpdate(
			{ _id: siteId, ownerId: userId },
			{ $set: data },
			{ new: true },
		);
		if (!site) {
			throw throwError.notFound("ไม่พบเว็บไซต์");
		}
		return site;
	}

	static async deleteSite(siteId: string, userId: string): Promise<{ message: string }> {
		const site = await Site.findOneAndDelete({ _id: siteId, ownerId: userId });
		if (!site) {
			throw throwError.notFound("ไม่พบเว็บไซต์");
		}
		return { message: "ลบเว็บไซต์สำเร็จ" };
	}

	// Statistics and Analytics
	static async getSiteStats(siteId: string, userId: string): Promise<any> {
		const site = await SiteService.getSiteById(siteId, userId);
		return site.stats;
	}

	static async getSiteAnalytics(siteId: string, userId: string, period: string): Promise<any> {
		const site = await SiteService.getSiteById(siteId, userId);
		// TODO: Implement analytics logic based on period
		return {
			period,
			stats: site.stats,
			trends: {
				visitors: { change: 5.2, trend: "up" },
				pageViews: { change: 3.1, trend: "up" },
				orders: { change: 12.5, trend: "up" },
				revenue: { change: 8.7, trend: "up" },
			},
		};
	}

	// Content Management
	static async getSitePages(siteId: string, userId: string): Promise<any[]> {
		const site = await SiteService.getSiteById(siteId, userId);
		return site.pages || [];
	}

	static async getSiteContent(siteId: string, userId: string): Promise<any> {
		const site = await SiteService.getSiteById(siteId, userId);
		return {
			pages: site.pages || [],
			menus: site.menus || [],
			settings: site.settings,
		};
	}

	static async getSiteMedia(siteId: string, userId: string): Promise<any[]> {
		const site = await SiteService.getSiteById(siteId, userId);
		// TODO: Implement media management
		return [];
	}

	// Settings Management
	static async updateSiteSettings(siteId: string, userId: string, settings: any): Promise<ISite> {
		const site = await Site.findOneAndUpdate(
			{ _id: siteId, ownerId: userId },
			{ $set: { settings } },
			{ new: true },
		);
		if (!site) {
			throw throwError.notFound("ไม่พบเว็บไซต์");
		}
		return site;
	}

	// Publishing
	static async publishSite(siteId: string, userId: string): Promise<ISite> {
		const site = await Site.findOneAndUpdate(
			{ _id: siteId, ownerId: userId },
			{
				$set: {
					isPublished: true,
					status: "published",
					lastPublished: new Date(),
				},
			},
			{ new: true },
		);
		if (!site) {
			throw throwError.notFound("ไม่พบเว็บไซต์");
		}
		return site;
	}

	static async unpublishSite(siteId: string, userId: string): Promise<ISite> {
		const site = await Site.findOneAndUpdate(
			{ _id: siteId, ownerId: userId },
			{
				$set: {
					isPublished: false,
					status: "draft",
				},
			},
			{ new: true },
		);
		if (!site) {
			throw throwError.notFound("ไม่พบเว็บไซต์");
		}
		return site;
	}

	// Page Management
	static async addPage(siteId: string, userId: string, pageData: any): Promise<ISite> {
		const page = {
			_id: generateMedelId(5),
			...pageData,
		};

		const site = await Site.findOneAndUpdate(
			{ _id: siteId, ownerId: userId },
			{ $push: { pages: page } },
			{ new: true },
		);
		if (!site) {
			throw throwError.notFound("ไม่พบเว็บไซต์");
		}
		return site;
	}

	static async updatePage(
		siteId: string,
		userId: string,
		pageId: string,
		pageData: any,
	): Promise<ISite> {
		const site = await Site.findOneAndUpdate(
			{
				_id: siteId,
				ownerId: userId,
				"pages._id": pageId,
			},
			{
				$set: {
					"pages.$": { ...pageData, _id: pageId },
				},
			},
			{ new: true },
		);
		if (!site) {
			throw throwError.notFound("ไม่พบเว็บไซต์หรือหน้า");
		}
		return site;
	}

	static async deletePage(siteId: string, userId: string, pageId: string): Promise<ISite> {
		const site = await Site.findOneAndUpdate(
			{ _id: siteId, ownerId: userId },
			{ $pull: { pages: { _id: pageId } } },
			{ new: true },
		);
		if (!site) {
			throw throwError.notFound("ไม่พบเว็บไซต์");
		}
		return site;
	}

	// Menu Management
	static async addMenu(siteId: string, userId: string, menuData: any): Promise<ISite> {
		const menu = {
			_id: generateMedelId(5),
			...menuData,
		};

		const site = await Site.findOneAndUpdate(
			{ _id: siteId, ownerId: userId },
			{ $push: { menus: menu } },
			{ new: true },
		);
		if (!site) {
			throw throwError.notFound("ไม่พบเว็บไซต์");
		}
		return site;
	}

	static async updateMenu(
		siteId: string,
		userId: string,
		menuId: string,
		menuData: any,
	): Promise<ISite> {
		const site = await Site.findOneAndUpdate(
			{
				_id: siteId,
				ownerId: userId,
				"menus._id": menuId,
			},
			{
				$set: {
					"menus.$": { ...menuData, _id: menuId },
				},
			},
			{ new: true },
		);
		if (!site) {
			throw throwError.notFound("ไม่พบเว็บไซต์หรือเมนู");
		}
		return site;
	}

	static async deleteMenu(siteId: string, userId: string, menuId: string): Promise<ISite> {
		const site = await Site.findOneAndUpdate(
			{ _id: siteId, ownerId: userId },
			{ $pull: { menus: { _id: menuId } } },
			{ new: true },
		);
		if (!site) {
			throw throwError.notFound("ไม่พบเว็บไซต์");
		}
		return site;
	}

	// Menu Item Management
	static async addMenuItem(
		siteId: string,
		userId: string,
		menuId: string,
		itemData: any,
	): Promise<ISite> {
		const menuItem = {
			_id: generateMedelId(5),
			...itemData,
		};

		const site = await Site.findOneAndUpdate(
			{
				_id: siteId,
				ownerId: userId,
				"menus._id": menuId,
			},
			{ $push: { "menus.$.items": menuItem } },
			{ new: true },
		);
		if (!site) {
			throw throwError.notFound("ไม่พบเว็บไซต์หรือเมนู");
		}
		return site;
	}

	static async updateMenuItem(
		siteId: string,
		userId: string,
		menuId: string,
		itemId: string,
		itemData: any,
	): Promise<ISite> {
		const site = await Site.findOneAndUpdate(
			{
				_id: siteId,
				ownerId: userId,
				"menus._id": menuId,
				"menus.items._id": itemId,
			},
			{
				$set: {
					"menus.$[menu].items.$[item]": { ...itemData, _id: itemId },
				},
			},
			{
				new: true,
				arrayFilters: [{ "menu._id": menuId }, { "item._id": itemId }],
			},
		);
		if (!site) {
			throw throwError.notFound("ไม่พบเว็บไซต์หรือรายการเมนู");
		}
		return site;
	}

	static async deleteMenuItem(
		siteId: string,
		userId: string,
		menuId: string,
		itemId: string,
	): Promise<ISite> {
		const site = await Site.findOneAndUpdate(
			{
				_id: siteId,
				ownerId: userId,
				"menus._id": menuId,
			},
			{ $pull: { "menus.$.items": { _id: itemId } } },
			{ new: true },
		);
		if (!site) {
			throw throwError.notFound("ไม่พบเว็บไซต์หรือเมนู");
		}
		return site;
	}

	// Theme Management
	static async updateTheme(siteId: string, userId: string, themeData: any): Promise<ISite> {
		const site = await Site.findOneAndUpdate(
			{ _id: siteId, ownerId: userId },
			{ $set: { theme: themeData } },
			{ new: true },
		);
		if (!site) {
			throw throwError.notFound("ไม่พบเว็บไซต์");
		}
		return site;
	}

	// Features Management
	static async updateFeatures(siteId: string, userId: string, featuresData: any): Promise<ISite> {
		const site = await Site.findOneAndUpdate(
			{ _id: siteId, ownerId: userId },
			{ $set: { features: featuresData } },
			{ new: true },
		);
		if (!site) {
			throw throwError.notFound("ไม่พบเว็บไซต์");
		}
		return site;
	}

	// Package Management
	static async updatePackage(siteId: string, userId: string, packageData: any): Promise<ISite> {
		const site = await Site.findOneAndUpdate(
			{ _id: siteId, ownerId: userId },
			{ $set: { package: packageData } },
			{ new: true },
		);
		if (!site) {
			throw throwError.notFound("ไม่พบเว็บไซต์");
		}
		return site;
	}

	// Public Access
	static async getPublicSite(domain: string): Promise<ISite | null> {
		return await Site.findOne({
			domain,
			isPublished: true,
			isActive: true,
			status: { $in: ["active", "published"] },
		});
	}

	static async getPublicSiteBySubdomain(subdomain: string): Promise<ISite | null> {
		return await Site.findOne({
			subdomain,
			isPublished: true,
			isActive: true,
			status: { $in: ["active", "published"] },
		});
	}

	// Statistics Update
	static async updateSiteStats(siteId: string, statsData: any): Promise<void> {
		await Site.findByIdAndUpdate(siteId, {
			$inc: {
				"stats.visitors": statsData.visitors || 0,
				"stats.pageViews": statsData.pageViews || 0,
				"stats.orders": statsData.orders || 0,
				"stats.revenue": statsData.revenue || 0,
			},
			$set: {
				"stats.lastVisit": new Date(),
			},
		});
	}

}
