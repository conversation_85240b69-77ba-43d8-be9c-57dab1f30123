import { Elysia, t } from 'elysia';
import { SiteService, getPackageInfo } from './site.service';
import { Site } from './site.model';
import { userAuthGuard } from '@/core/plugin';
import type { IUser } from '@/modules/user/user.model';
import { throwError } from '@/core/utils/error';
import { getFullDomain } from '@/core/utils/domain';
import { checkDomainAvailability } from '@/core/utils/domain';
// import { createSite } from './site.service';

// Interface สำหรับ pagination
// interface PaginationOptions {
// 	page: number;
// 	limit: number;
// }

// interface PaginatedResult<T> {
// 	data: T[];
// 	total: number;
// 	page: number;
// 	limit: number;
// 	totalPages: number;
// 	hasNext: boolean;
// 	hasPrev: boolean;
// }

export const siteRoute = new Elysia({ prefix: '/sites' })
	.use(userAuthGuard)
	.get('/', async ({ user, query }) => {
		const page = parseInt(query.page as string) || 1;
		const limit = parseInt(query.limit as string) || 10;

		// const result = await SiteService.getUserSites((user as IUser)._id, { page, limit });
		const result = await SiteService.getMySites(user as IUser, { page, limit }, query.search as string, query.status as string);

		return {
			success: true,
			data: result.sites,
			pagination: result.pagination,
		};
	})
	.post('/', async ({ user, body }) => {
		// const siteData = {
		// 	...body,
		// 	userId: (user as IUser)._id,
		// };
		try {

			const { typeDomain, subDomain, mainDomain, customDomain, packageType } = body;

			console.log('body', body);


			// Validate domain fields based on typeDomain
			if (typeDomain === 'subdomain') {
				if (!subDomain || subDomain.trim() === '') {
					throwError.badRequest('กรุณากรอกซับโดเมน');
				}
				if (!mainDomain || mainDomain.trim() === '') {
					throwError.badRequest('กรุณากรอกโดเมนหลัก');
				}
				if (customDomain && customDomain.trim() !== '') {
					throwError.badRequest('ไม่ควรมีโดเมนส่วนตัวเมื่อเลือกซับโดเมน');
				}
				// Set customDomain to undefined for subdomain
				body.customDomain = undefined;
			}
			else if (typeDomain === 'custom') {
				if (!customDomain || customDomain.trim() === '') {
					throwError.badRequest('กรุณากรอกโดเมนส่วนตัว');
				}
				if ((subDomain && subDomain.trim() !== '') || (mainDomain && mainDomain.trim() !== '')) {
					throwError.badRequest('ไม่ควรมีซับโดเมนหรือโดเมนหลักเมื่อเลือกโดเมนส่วนตัว');
				}
				// Set subDomain and mainDomain to undefined for custom domain
				body.subDomain = undefined;
				body.mainDomain = undefined;
			}

			const fullDomain = await getFullDomain(typeDomain, subDomain, mainDomain, customDomain);

			const { available } = await checkDomainAvailability(fullDomain);

			if (!available) {
				throwError.badRequest(`ไม่สามารถสร้างเว็บไซต์ได้ เนื่องจาก ${fullDomain} ถูกใช้แล้ว`);
			}

			//   const { site, resRegisterDomain, resCheck } = await createSite({
			// 		packageType: packageType,
			// 	name: body.name	,
			// 	typeDomain,
			// 	fullDomain,
			// 	user: (user as IUser)._id,
			//   });

			// console.log('siteData', siteData);
			// console.log('user', user);

			const site = await SiteService.createSite(user as IUser, {
				name: body.name,
				fullDomain: fullDomain,
				typeDomain: typeDomain,
				packageType: packageType
			});

			return {
				success: true,
				message: 'สร้างเว็บไซต์สำเร็จ',
				data: site,
			};


		} catch (error) {
			return {
				success: false,
				message: 'สร้างเว็บไซต์ไม่สำเร็จ',
				error: error,
			};
		}
	})
	.post('/check-domain', async ({ body }) => {
		const { domain } = body as { domain: string };

		// ตรวจสอบว่าโดเมนว่างหรือไม่
		const existingSite = await Site.findOne({
			$or: [
				{ domain: domain },
				{ subdomain: domain }
			]
		});

		return {
			success: true,
			available: !existingSite,
			message: existingSite ? 'โดเมนนี้ถูกใช้งานแล้ว' : 'โดเมนนี้สามารถใช้งานได้',
		};
	})
	.get('/packages', async () => {
		// ส่งกลับข้อมูลแพ็คเกจจาก service
		const packageInfo = getPackageInfo();

		return {
			success: true,
			data: packageInfo.packages || [],
		};
	})
	.get('/:id', async ({ params, user }) => {
		const site = await SiteService.getSiteById(params.id, (user as IUser)._id);

		if (!site) {
			throw new Error('ไม่พบเว็บไซต์');
		}

		return {
			success: true,
			data: site,
		};
	})
	.put('/:id', async ({ params, body, user }) => {
		const site = await SiteService.updateSite(params.id, (user as IUser)._id, body);

		return {
			success: true,
			message: 'อัปเดตเว็บไซต์สำเร็จ',
			data: site,
		};
	})
	.delete('/:id', async ({ params, user }) => {
		await SiteService.deleteSite(params.id, (user as IUser)._id);

		return {
			success: true,
			message: 'ลบเว็บไซต์สำเร็จ',
		};
	});

// Discount routes
export const discountRoute = new Elysia({ prefix: '/discounts' })
	.use(userAuthGuard)
	.post('/check', async ({ body, user }) => {
		const { code, orderAmount } = body as { code: string; orderAmount: number };

		// จำลองการตรวจสอบส่วนลด
		const discounts = {
			'WELCOME': {
				code: 'WELCOME',
				name: 'ส่วนลดต้อนรับ',
				description: 'ส่วนลดสำหรับลูกค้าใหม่',
				type: 'percentage',
				value: 10,
				conditions: {
					minOrderAmount: 100,
					firstTimeOnly: true
				},
				maxDiscountAmount: 500,
				endDate: new Date('2025-12-31')
			},
			'SAVE10': {
				code: 'SAVE10',
				name: 'ประหยัด 10%',
				description: 'ส่วนลด 10% สำหรับทุกออเดอร์',
				type: 'percentage',
				value: 10,
				conditions: {
					minOrderAmount: 0
				},
				maxDiscountAmount: 1000,
				endDate: new Date('2025-12-31')
			},
			'SAVE50': {
				code: 'SAVE50',
				name: 'ประหยัด 50 บาท',
				description: 'ส่วนลด 50 บาท',
				type: 'fixed',
				value: 50,
				conditions: {
					minOrderAmount: 200
				},
				maxDiscountAmount: 50,
				endDate: new Date('2025-12-31')
			}
		};

		const discount = discounts[code as keyof typeof discounts];

		if (!discount) {
			return {
				success: true,
				valid: false,
				message: 'โค้ดส่วนลดไม่ถูกต้อง',
			};
		}

		// ตรวจสอบเงื่อนไข
		if (orderAmount < discount.conditions.minOrderAmount) {
			return {
				success: true,
				valid: false,
				message: `ต้องสั่งซื้อขั้นต่ำ ฿${discount.conditions.minOrderAmount.toLocaleString()}`,
			};
		}

		// ตรวจสอบวันหมดอายุ
		if (discount.endDate && new Date() > discount.endDate) {
			return {
				success: true,
				valid: false,
				message: 'โค้ดส่วนลดหมดอายุแล้ว',
			};
		}

		return {
			success: true,
			valid: true,
			discount: discount,
			message: 'โค้ดส่วนลดถูกต้อง',
		};
	});
