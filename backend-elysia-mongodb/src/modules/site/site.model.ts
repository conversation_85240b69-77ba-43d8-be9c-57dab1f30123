import mongoose from "mongoose";
import { generateMedelId } from "@/core/utils/genId";

export interface ISite {
	_id: string;
	name: string;
	typeDomain: string;
	fullDomain: string; 
	description?: string;
	logo?: string;
	favicon?: string;
	coverImage?: string;
	theme: {
		primaryColor: string;
		secondaryColor: string;
		fontFamily: string;
		layout: "modern" | "classic" | "minimal";
		customTheme?: string;
	};
	status: "active" | "inactive" | "maintenance" | "published" | "draft";
	isPublished: boolean;
	isActive: boolean;
	ownerId: string; // User ID ที่เป็นเจ้าของ
	type: "ecommerce" | "website" | "blog" | "portfolio";

	// E-commerce specific fields
	settings: {
		seo?: {
			metaTitle?: string;
			metaDescription?: string;
			keywords?: string[];
			ogImage?: string;
		};
		analytics?: {
			googleAnalyticsId?: string;
			facebookPixelId?: string;
		};
		social?: {
			facebook?: string;
			twitter?: string;
			instagram?: string;
			youtube?: string;
		};
		contact?: {
			email?: string;
			phone?: string;
			address?: string;
		};
		ecommerce?: {
			currency?: string;
			taxRate?: number;
			shippingOptions?: Array<{
				name: string;
				price: number;
				description?: string;
			}>;
		};
	};

	// Website builder specific fields
	features: {
		blog: boolean;
		news: boolean;
		contact: boolean;
		about: boolean;
		services: boolean;
		ecommerce: boolean;
		portfolio: boolean;
	};

	pages: Array<{
		_id: string;
		title: string;
		slug: string;
		content: string;
		isPublished: boolean;
		order: number;
		type: "page" | "post" | "product" | "category";
		meta?: {
			title?: string;
			description?: string;
			keywords?: string[];
		};
	}>;

	menus: Array<{
		_id: string;
		name: string;
		location: "header" | "footer" | "sidebar";
		items: Array<{
			_id: string;
			title: string;
			url: string;
			target: "_self" | "_blank";
			order: number;
			type: "link" | "page" | "category" | "product";
			children?: Array<{
				_id: string;
				title: string;
				url: string;
				target: "_self" | "_blank";
				order: number;
			}>;
		}>;
	}>;

	// Package/Pricing system
	package: {
		type: "free" | "basic" | "premium" | "enterprise";
		billingCycle: "monthly" | "yearly" | "permanent";
		price: number;
		startDate: Date;
		endDate?: Date;
		isActive: boolean;
		features: string[];
	};

	// Statistics
	stats: {
		visitors: number;
		pageViews: number;
		orders: number;
		revenue: number;
		conversionRate: number;
		avgOrderValue: number;
		performance: number;
		lastVisit?: Date;
	};

	createdAt: Date;
	updatedAt: Date;
	lastPublished?: Date;
}

const siteSchema = new mongoose.Schema<ISite>(
	{
		_id: { type: String, default: () => generateMedelId(8) },
		name: {
			type: String,
			required: true,
			trim: true,
		},
		typeDomain: {
			type: String,
			enum: ["subdomain", "custom"],
			default: "subdomain",
		},
		fullDomain: {
			type: String,
			required: true,
			unique: true,
			lowercase: true,
			trim: true,
		}, 
		description: {
			type: String,
			trim: true,
		},
		logo: {
			type: String,
			default: "",
		},
		favicon: {
			type: String,
			default: "",
		},
		coverImage: {
			type: String,
			default: "",
		},
		theme: {
			primaryColor: {
				type: String,
				default: "#3B82F6",
			},
			secondaryColor: {
				type: String,
				default: "#1F2937",
			},
			fontFamily: {
				type: String,
				default: "Inter",
			},
			layout: {
				type: String,
				enum: ["modern", "classic", "minimal"],
				default: "modern",
			},
			customTheme: String,
		},
		status: {
			type: String,
			enum: ["active", "inactive", "maintenance", "published", "draft"],
			default: "draft",
		},
		isPublished: {
			type: Boolean,
			default: false,
		},
		isActive: {
			type: Boolean,
			default: true,
		},
		ownerId: {
			type: String,
			required: true,
			ref: "User",
		},
		type: {
			type: String,
			enum: ["ecommerce", "website", "blog", "portfolio"],
			default: "website",
		},
		settings: {
			seo: {
				metaTitle: String,
				metaDescription: String,
				keywords: [String],
				ogImage: String,
			},
			analytics: {
				googleAnalyticsId: String,
				facebookPixelId: String,
			},
			social: {
				facebook: String,
				twitter: String,
				instagram: String,
				youtube: String,
			},
			contact: {
				email: String,
				phone: String,
				address: String,
			},
			ecommerce: {
				currency: { type: String, default: "THB" },
				taxRate: { type: Number, default: 0 },
				shippingOptions: [
					{
						name: String,
						price: Number,
						description: String,
					},
				],
			},
		},
		features: {
			blog: { type: Boolean, default: false },
			news: { type: Boolean, default: false },
			contact: { type: Boolean, default: true },
			about: { type: Boolean, default: true },
			services: { type: Boolean, default: false },
			ecommerce: { type: Boolean, default: false },
			portfolio: { type: Boolean, default: false },
		},
		pages: [
			{
				_id: { type: String, default: () => generateMedelId(5) },
				title: { type: String, required: true },
				slug: { type: String, required: true },
				content: { type: String, default: "" },
				isPublished: { type: Boolean, default: false },
				order: { type: Number, default: 0 },
				type: {
					type: String,
					enum: ["page", "post", "product", "category"],
					default: "page",
				},
				meta: {
					title: String,
					description: String,
					keywords: [String],
				},
			},
		],
		menus: [
			{
				_id: { type: String, default: () => generateMedelId(5) },
				name: { type: String, required: true },
				location: {
					type: String,
					enum: ["header", "footer", "sidebar"],
					default: "header",
				},
				items: [
					{
						_id: { type: String, default: () => generateMedelId(5) },
						title: { type: String, required: true },
						url: { type: String, required: true },
						target: {
							type: String,
							enum: ["_self", "_blank"],
							default: "_self",
						},
						order: { type: Number, default: 0 },
						type: {
							type: String,
							enum: ["link", "page", "category", "product"],
							default: "link",
						},
						children: [
							{
								_id: { type: String, default: () => generateMedelId(5) },
								title: { type: String, required: true },
								url: { type: String, required: true },
								target: {
									type: String,
									enum: ["_self", "_blank"],
									default: "_self",
								},
								order: { type: Number, default: 0 },
							},
						],
					},
				],
			},
		],
		stats: {
			visitors: { type: Number, default: 0 },
			pageViews: { type: Number, default: 0 },
			orders: { type: Number, default: 0 },
			revenue: { type: Number, default: 0 },
			conversionRate: { type: Number, default: 0 },
			avgOrderValue: { type: Number, default: 0 },
			performance: { type: Number, default: 100 },
			lastVisit: Date,
		},
		lastPublished: {
			type: Date,
		},
	},
	{
		timestamps: true,
		versionKey: false,
	},
);

// Indexes
// siteSchema.index({ ownerId: 1 });
// siteSchema.index({ domain: 1 });
// siteSchema.index({ subdomain: 1 });
// siteSchema.index({ status: 1 });
// siteSchema.index({ type: 1 });
// siteSchema.index({ "package.isActive": 1 });

export const Site = mongoose.model<ISite>("Site", siteSchema);
