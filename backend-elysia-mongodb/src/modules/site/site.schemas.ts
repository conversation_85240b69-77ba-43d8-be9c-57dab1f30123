import { z } from "zod";

// Create site schema
export const createSiteSchema = z.object({
	name: z.string().min(1, "ชื่อเว็บไซต์ไม่สามารถว่างได้"),
	packageType: z.enum(["monthly", "yearly", "permanent"]).default("monthly"),
	typeDomain: z.enum(["subdomain", "custom"]).default("subdomain"),
	subDomain: z.string().optional(),
	mainDomain: z.string().optional(),
	customDomain: z.string().optional(),
	
});

// Update site schema
export const updateSiteSchema = z.object({
	name: z.string().min(1, "ชื่อเว็บไซต์ไม่สามารถว่างได้").optional(),
	domain: z.string().min(1, "โดเมนไม่สามารถว่างได้").optional(),
	subdomain: z.string().min(1, "ซับโดเมนไม่สามารถว่างได้").optional(), 
	description: z.string().optional(),
	logo: z.string().optional(),
	favicon: z.string().optional(),
	coverImage: z.string().optional(),
	type: z.enum(["ecommerce", "website", "blog", "portfolio"]).optional(),
	status: z.enum(["active", "inactive", "maintenance", "published", "draft"]).optional(),
	isPublished: z.boolean().optional(),
	isActive: z.boolean().optional(),
	theme: z
		.object({
			primaryColor: z.string().optional(),
			secondaryColor: z.string().optional(),
			fontFamily: z.string().optional(),
			layout: z.enum(["modern", "classic", "minimal"]).optional(),
			customTheme: z.string().optional(),
		})
		.optional(),
	features: z
		.object({
			blog: z.boolean().optional(),
			news: z.boolean().optional(),
			contact: z.boolean().optional(),
			about: z.boolean().optional(),
			services: z.boolean().optional(),
			ecommerce: z.boolean().optional(),
			portfolio: z.boolean().optional(),
		})
		.optional(),
	package: z
		.object({
			type: z.enum(["free", "basic", "premium", "enterprise"]).optional(),
			billingCycle: z.enum(["monthly", "yearly", "permanent"]).optional(),
			price: z.number().optional(),
			features: z.array(z.string()).optional(),
		})
		.optional(),
});

// Update site settings schema
export const updateSiteSettingsSchema = z.object({
	seo: z
		.object({
			metaTitle: z.string().optional(),
			metaDescription: z.string().optional(),
			keywords: z.array(z.string()).optional(),
			ogImage: z.string().optional(),
		})
		.optional(),
	analytics: z
		.object({
			googleAnalyticsId: z.string().optional(),
			facebookPixelId: z.string().optional(),
		})
		.optional(),
	social: z
		.object({
			facebook: z.string().optional(),
			twitter: z.string().optional(),
			instagram: z.string().optional(),
			youtube: z.string().optional(),
		})
		.optional(),
	contact: z
		.object({
			email: z.string().optional(),
			phone: z.string().optional(),
			address: z.string().optional(),
		})
		.optional(),
	ecommerce: z
		.object({
			currency: z.string().optional(),
			taxRate: z.number().optional(),
			shippingOptions: z
				.array(
					z.object({
						name: z.string(),
						price: z.number(),
						description: z.string().optional(),
					}),
				)
				.optional(),
		})
		.optional(),
});

// Page schemas
export const createPageSchema = z.object({
	title: z.string().min(1, "หัวข้อไม่สามารถว่างได้"),
	slug: z.string().min(1, "slug ไม่สามารถว่างได้"),
	content: z.string().optional(),
	isPublished: z.boolean().default(false),
	order: z.number().default(0),
	type: z.enum(["page", "post", "product", "category"]).default("page"),
	meta: z
		.object({
			title: z.string().optional(),
			description: z.string().optional(),
			keywords: z.array(z.string()).optional(),
		})
		.optional(),
});

export const updatePageSchema = z.object({
	title: z.string().min(1, "หัวข้อไม่สามารถว่างได้").optional(),
	slug: z.string().min(1, "slug ไม่สามารถว่างได้").optional(),
	content: z.string().optional(),
	isPublished: z.boolean().optional(),
	order: z.number().optional(),
	type: z.enum(["page", "post", "product", "category"]).optional(),
	meta: z
		.object({
			title: z.string().optional(),
			description: z.string().optional(),
			keywords: z.array(z.string()).optional(),
		})
		.optional(),
});

// Menu schemas
export const createMenuSchema = z.object({
	name: z.string().min(1, "ชื่อเมนูไม่สามารถว่างได้"),
	location: z.enum(["header", "footer", "sidebar"]).default("header"),
	items: z
		.array(
			z.object({
				title: z.string().min(1, "หัวข้อไม่สามารถว่างได้"),
				url: z.string().min(1, "URL ไม่สามารถว่างได้"),
				target: z.enum(["_self", "_blank"]).default("_self"),
				order: z.number().default(0),
				type: z.enum(["link", "page", "category", "product"]).default("link"),
				children: z
					.array(
						z.object({
							title: z.string().min(1, "หัวข้อไม่สามารถว่างได้"),
							url: z.string().min(1, "URL ไม่สามารถว่างได้"),
							target: z.enum(["_self", "_blank"]).default("_self"),
							order: z.number().default(0),
						}),
					)
					.optional(),
			}),
		)
		.optional(),
});

export const updateMenuSchema = z.object({
	name: z.string().min(1, "ชื่อเมนูไม่สามารถว่างได้").optional(),
	location: z.enum(["header", "footer", "sidebar"]).optional(),
	items: z
		.array(
			z.object({
				title: z.string().min(1, "หัวข้อไม่สามารถว่างได้"),
				url: z.string().min(1, "URL ไม่สามารถว่างได้"),
				target: z.enum(["_self", "_blank"]).default("_self"),
				order: z.number().default(0),
				type: z.enum(["link", "page", "category", "product"]).default("link"),
				children: z
					.array(
						z.object({
							title: z.string().min(1, "หัวข้อไม่สามารถว่างได้"),
							url: z.string().min(1, "URL ไม่สามารถว่างได้"),
							target: z.enum(["_self", "_blank"]).default("_self"),
							order: z.number().default(0),
						}),
					)
					.optional(),
			}),
		)
		.optional(),
});

// Menu item schemas
export const createMenuItemSchema = z.object({
	title: z.string().min(1, "หัวข้อไม่สามารถว่างได้"),
	url: z.string().min(1, "URL ไม่สามารถว่างได้"),
	target: z.enum(["_self", "_blank"]).default("_self"),
	order: z.number().default(0),
	children: z
		.array(
			z.object({
				title: z.string().min(1, "หัวข้อไม่สามารถว่างได้"),
				url: z.string().min(1, "URL ไม่สามารถว่างได้"),
				target: z.enum(["_self", "_blank"]).default("_self"),
				order: z.number().default(0),
			}),
		)
		.optional(),
});

export const updateMenuItemSchema = z.object({
	title: z.string().min(1, "หัวข้อไม่สามารถว่างได้").optional(),
	url: z.string().min(1, "URL ไม่สามารถว่างได้").optional(),
	target: z.enum(["_self", "_blank"]).optional(),
	order: z.number().optional(),
	children: z
		.array(
			z.object({
				title: z.string().min(1, "หัวข้อไม่สามารถว่างได้"),
				url: z.string().min(1, "URL ไม่สามารถว่างได้"),
				target: z.enum(["_self", "_blank"]).default("_self"),
				order: z.number().default(0),
			}),
		)
		.optional(),
});

// Package schemas
export const updatePackageSchema = z.object({
	type: z.enum(["free", "basic", "premium", "enterprise"]),
	billingCycle: z.enum(["monthly", "yearly", "permanent"]),
	price: z.number(),
	features: z.array(z.string()),
});

// Theme schemas
export const updateThemeSchema = z.object({
	primaryColor: z.string(),
	secondaryColor: z.string(),
	fontFamily: z.string().optional(),
	layout: z.enum(["modern", "classic", "minimal"]).optional(),
	customTheme: z.string().optional(),
});

// Features schemas
export const updateFeaturesSchema = z.object({
	blog: z.boolean().optional(),
	news: z.boolean().optional(),
	contact: z.boolean().optional(),
	about: z.boolean().optional(),
	services: z.boolean().optional(),
	ecommerce: z.boolean().optional(),
	portfolio: z.boolean().optional(),
});

// Type exports
export type CreateSiteInput = z.infer<typeof createSiteSchema>;
export type UpdateSiteInput = z.infer<typeof updateSiteSchema>;
export type UpdateSiteSettingsInput = z.infer<typeof updateSiteSettingsSchema>;
