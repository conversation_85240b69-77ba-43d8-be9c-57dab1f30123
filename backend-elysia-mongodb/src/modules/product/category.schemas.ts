import { z } from "zod";
import { CategoryStatus } from "./category.model";

// Schema สำหรับการสร้างหมวดหมู่
export const createCategorySchema = z.object({
	name: z.string().min(1, "กรุณากรอกชื่อหมวดหมู่").max(100, "ชื่อหมวดหมู่ต้องไม่เกิน 100 ตัวอักษร"),
	description: z.string().max(1000, "รายละเอียดต้องไม่เกิน 1000 ตัวอักษร").optional(),
	parentId: z.string().optional(),
	imageId: z.string().optional(),
	iconClass: z.string().optional(),
	sortOrder: z.number().default(0),
	isVisible: z.boolean().default(true),
	isFeatured: z.boolean().default(false),
	seo: z.object({
		metaTitle: z.string().optional(),
		metaDescription: z.string().optional(),
		keywords: z.array(z.string()).optional(),
		slug: z.string().optional(),
	}).optional(),
});

// Schema สำหรับการอัพเดทหมวดหมู่
export const updateCategorySchema = createCategorySchema.partial().extend({
	status: z.nativeEnum(CategoryStatus).optional(),
});

// Schema สำหรับการค้นหาหมวดหมู่
export const searchCategoriesSchema = z.object({
	search: z.string().optional(),
	parentId: z.string().optional(),
	level: z.number().min(0).optional(),
	status: z.nativeEnum(CategoryStatus).optional(),
	isVisible: z.boolean().optional(),
	isFeatured: z.boolean().optional(),
	createdBy: z.string().optional(),
	page: z.number().min(1).default(1),
	limit: z.number().min(1).max(100).default(20),
	sortBy: z.string().default("sortOrder"),
	sortOrder: z.enum(["asc", "desc"]).default("asc"),
});

// Schema สำหรับการย้ายหมวดหมู่
export const moveCategorySchema = z.object({
	newParentId: z.string().nullable(),
});

// Schema สำหรับการเรียงลำดับหมวดหมู่
export const reorderCategoriesSchema = z.object({
	categoryOrders: z.array(z.object({
		id: z.string().min(1, "กรุณากรอก ID หมวดหมู่"),
		sortOrder: z.number(),
	})).min(1, "กรุณากรอกข้อมูลการเรียงลำดับ"),
});

// Schema สำหรับ query parameters
export const categoryParamsSchema = z.object({
	id: z.string().min(1, "กรุณากรอก ID หมวดหมู่"),
});

export const categorySlugParamsSchema = z.object({
	slug: z.string().min(1, "กรุณากรอก slug หมวดหมู่"),
});

export const folderParamsSchema = z.object({
	folder: z.string().min(1, "กรุณากรอกชื่อ folder"),
});

export const tagsParamsSchema = z.object({
	tags: z.string().min(1, "กรุณากรอก tags"),
});

// Type exports
export type CreateCategoryInput = z.infer<typeof createCategorySchema>;
export type UpdateCategoryInput = z.infer<typeof updateCategorySchema>;
export type SearchCategoriesInput = z.infer<typeof searchCategoriesSchema>;
export type MoveCategoryInput = z.infer<typeof moveCategorySchema>;
export type ReorderCategoriesInput = z.infer<typeof reorderCategoriesSchema>;
export type CategoryParamsInput = z.infer<typeof categoryParamsSchema>;
export type CategorySlugParamsInput = z.infer<typeof categorySlugParamsSchema>;
export type FolderParamsInput = z.infer<typeof folderParamsSchema>;
export type TagsParamsInput = z.infer<typeof tagsParamsSchema>;