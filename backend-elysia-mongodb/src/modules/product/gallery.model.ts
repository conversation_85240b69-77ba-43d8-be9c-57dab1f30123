import { Schema, model, type Document } from "mongoose";

// Enum สำหรับประเภทไฟล์
export enum FileType {
	IMAGE = "image",
	VIDEO = "video",
	DOCUMENT = "document",
	AUDIO = "audio",
	OTHER = "other",
}

// Enum สำหรับสถานะไฟล์
export enum FileStatus {
	ACTIVE = "active",
	INACTIVE = "inactive",
	PROCESSING = "processing",
	ERROR = "error",
}

// Interface สำหรับข้อมูล metadata ของไฟล์
export interface IFileMetadata {
	width?: number;
	height?: number;
	duration?: number; // สำหรับวิดีโอ/เสียง (วินาที)
	format: string;
	size: number; // bytes
	colorSpace?: string;
	hasAlpha?: boolean;
	orientation?: number;
}

// Interface สำหรับการแปลงรูปภาพ (transformations)
export interface ITransformation {
	name: string; // เช่น "thumbnail", "medium", "large"
	width?: number;
	height?: number;
	crop?: string;
	quality?: string | number;
	format?: string;
	url: string;
	publicId: string;
}

// Interface สำหรับ Gallery
export interface IGallery extends Document {
	// ข้อมูลพื้นฐาน
	title: string;
	description?: string;
	altText?: string; // สำหรับ SEO และ accessibility
	
	// ข้อมูลไฟล์
	originalName: string;
	fileName: string;
	fileType: FileType;
	mimeType: string;
	metadata: IFileMetadata;
	
	// Cloudinary
	cloudinaryPublicId: string;
	cloudinaryUrl: string;
	cloudinarySecureUrl: string;
	
	// รูปภาพที่แปลงแล้ว (thumbnails, different sizes)
	transformations: ITransformation[];
	
	// การจัดหมวดหมู่
	folder: string; // เช่น "products", "profiles", "covers"
	tags: string[];
	
	// สถานะ
	status: FileStatus;
	
	// การใช้งาน
	usageCount: number; // จำนวนครั้งที่ถูกใช้
	usedBy: string[]; // Array ของ entity IDs ที่ใช้รูปนี้
	
	// การจัดการ
	uploadedBy: string; // User ID
	
	// สถิติ
	viewCount: number;
	downloadCount: number;
	
	// วันที่
	createdAt: Date;
	updatedAt: Date;
	lastUsedAt?: Date;
}

// Schema สำหรับ metadata
const metadataSchema = new Schema<IFileMetadata>({
	width: { type: Number },
	height: { type: Number },
	duration: { type: Number },
	format: { type: String, required: true },
	size: { type: Number, required: true },
	colorSpace: { type: String },
	hasAlpha: { type: Boolean },
	orientation: { type: Number },
});

// Schema สำหรับ transformations
const transformationSchema = new Schema<ITransformation>({
	name: { type: String, required: true },
	width: { type: Number },
	height: { type: Number },
	crop: { type: String },
	quality: { type: Schema.Types.Mixed },
	format: { type: String },
	url: { type: String, required: true },
	publicId: { type: String, required: true },
});

// Main Gallery Schema
const gallerySchema = new Schema<IGallery>({
	// ข้อมูลพื้นฐาน
	title: { 
		type: String, 
		required: true,
		trim: true,
		maxlength: 200 
	},
	description: { 
		type: String,
		maxlength: 1000 
	},
	altText: { 
		type: String,
		maxlength: 200 
	},
	
	// ข้อมูลไฟล์
	originalName: { 
		type: String, 
		required: true 
	},
	fileName: { 
		type: String, 
		required: true 
	},
	fileType: { 
		type: String, 
		enum: Object.values(FileType),
		required: true 
	},
	mimeType: { 
		type: String, 
		required: true 
	},
	metadata: { 
		type: metadataSchema, 
		required: true 
	},
	
	// Cloudinary
	cloudinaryPublicId: { 
		type: String, 
		required: true,
		unique: true 
	},
	cloudinaryUrl: { 
		type: String, 
		required: true 
	},
	cloudinarySecureUrl: { 
		type: String, 
		required: true 
	},
	
	// รูปภาพที่แปลงแล้ว
	transformations: [transformationSchema],
	
	// การจัดหมวดหมู่
	folder: { 
		type: String, 
		required: true,
		default: "general" 
	},
	tags: [{ 
		type: String,
		trim: true 
	}],
	
	// สถานะ
	status: { 
		type: String, 
		enum: Object.values(FileStatus),
		default: FileStatus.ACTIVE 
	},
	
	// การใช้งาน
	usageCount: { 
		type: Number, 
		default: 0 
	},
	usedBy: [{ 
		type: String 
	}],
	
	// การจัดการ
	uploadedBy: { 
		type: String,
		required: true,
		ref: "User" 
	},
	
	// สถิติ
	viewCount: { 
		type: Number, 
		default: 0 
	},
	downloadCount: { 
		type: Number, 
		default: 0 
	},
	
	// วันที่
	lastUsedAt: { type: Date },
}, {
	timestamps: true,
	toJSON: { virtuals: true },
	toObject: { virtuals: true },
});

// Indexes สำหรับประสิทธิภาพ
gallerySchema.index({ cloudinaryPublicId: 1 });
gallerySchema.index({ fileType: 1, status: 1 });
gallerySchema.index({ folder: 1 });
gallerySchema.index({ tags: 1 });
gallerySchema.index({ uploadedBy: 1 });
gallerySchema.index({ createdAt: -1 });
gallerySchema.index({ usageCount: -1 });
gallerySchema.index({ viewCount: -1 });

// Virtual สำหรับ URL ของ thumbnail
gallerySchema.virtual("thumbnailUrl").get(function() {
	const thumbnail = this.transformations.find(t => t.name === "thumbnail");
	return thumbnail ? thumbnail.url : this.cloudinarySecureUrl;
});

// Virtual สำหรับ URL ของรูปขนาดกลาง
gallerySchema.virtual("mediumUrl").get(function() {
	const medium = this.transformations.find(t => t.name === "medium");
	return medium ? medium.url : this.cloudinarySecureUrl;
});

// Virtual สำหรับตรวจสอบว่าเป็นรูปภาพหรือไม่
gallerySchema.virtual("isImage").get(function() {
	return this.fileType === FileType.IMAGE;
});

// Virtual สำหรับตรวจสอบว่าเป็นวิดีโอหรือไม่
gallerySchema.virtual("isVideo").get(function() {
	return this.fileType === FileType.VIDEO;
});

// Method สำหรับเพิ่มการใช้งาน
gallerySchema.methods.addUsage = function(entityId: string) {
	if (!this.usedBy.includes(entityId)) {
		this.usedBy.push(entityId);
		this.usageCount += 1;
		this.lastUsedAt = new Date();
	}
	return this.save();
};

// Method สำหรับลดการใช้งาน
gallerySchema.methods.removeUsage = function(entityId: string) {
	const index = this.usedBy.indexOf(entityId);
	if (index > -1) {
		this.usedBy.splice(index, 1);
		this.usageCount = Math.max(0, this.usageCount - 1);
	}
	return this.save();
};

// Method สำหรับเพิ่ม view count
gallerySchema.methods.incrementView = function() {
	this.viewCount += 1;
	return this.save();
};

// Method สำหรับเพิ่ม download count
gallerySchema.methods.incrementDownload = function() {
	this.downloadCount += 1;
	return this.save();
};

// Static method สำหรับค้นหาไฟล์ที่ไม่ได้ใช้
gallerySchema.statics.findUnused = function() {
	return this.find({ usageCount: 0 });
};

// Static method สำหรับค้นหาตาม folder
gallerySchema.statics.findByFolder = function(folder: string) {
	return this.find({ folder, status: FileStatus.ACTIVE });
};

// Static method สำหรับค้นหาตาม tags
gallerySchema.statics.findByTags = function(tags: string[]) {
	return this.find({ 
		tags: { $in: tags }, 
		status: FileStatus.ACTIVE 
	});
};

export const Gallery = model<IGallery>("Gallery", gallerySchema);