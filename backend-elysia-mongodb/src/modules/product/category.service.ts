import { Category, type ICategory, CategoryStatus } from "./category.model";
import { Gallery } from "./gallery.model";
import { throwError } from "@/core/utils/error";

// Interface สำหรับการสร้างหมวดหมู่
export interface ICreateCategoryData {
	name: string;
	description?: string;
	parentId?: string;
	imageId?: string;
	iconClass?: string;
	sortOrder?: number;
	isVisible?: boolean;
	isFeatured?: boolean;
	seo?: {
		metaTitle?: string;
		metaDescription?: string;
		keywords?: string[];
		slug?: string;
	};
}

// Interface สำหรับการอัพเดทหมวดหมู่
export interface IUpdateCategoryData extends Partial<ICreateCategoryData> {
	status?: CategoryStatus;
}

// Interface สำหรับการค้นหาหมวดหมู่
export interface ICategoryFilter {
	search?: string;
	parentId?: string;
	level?: number;
	status?: CategoryStatus;
	isVisible?: boolean;
	isFeatured?: boolean;
	createdBy?: string;
	page?: number;
	limit?: number;
	sortBy?: string;
	sortOrder?: "asc" | "desc";
}

export class CategoryService {
	/**
	 * สร้างหมวดหมู่ใหม่
	 */
	async createCategory(data: ICreateCategoryData, createdBy: string): Promise<ICategory> {
		try {
			// ตรวจสอบหมวดหมู่แม่ (ถ้ามี)
			let parentCategory = null;
			if (data.parentId) {
				parentCategory = await Category.findById(data.parentId);
				if (!parentCategory) {
					throw throwError.notFound("ไม่พบหมวดหมู่แม่");
				}
				if (parentCategory.status !== CategoryStatus.ACTIVE) {
					throw throwError.badRequest("หมวดหมู่แม่ไม่ได้เปิดใช้งาน");
				}
			}

			// ตรวจสอบรูปภาพ (ถ้ามี)
			if (data.imageId) {
				const image = await Gallery.findById(data.imageId);
				if (!image) {
					throw throwError.notFound("ไม่พบรูปภาพ");
				}
			}

			// สร้าง slug อัตโนมัติถ้าไม่มี
			let slug = data.seo?.slug;
			if (!slug) {
				slug = data.name
					.toLowerCase()
					.replace(/[^a-z0-9\u0E00-\u0E7F]+/g, "-")
					.replace(/^-+|-+$/g, "");
			}

			// ตรวจสอบ slug ซ้ำ
			const existingCategory = await Category.findOne({ "seo.slug": slug });
			if (existingCategory) {
				slug = `${slug}-${Date.now()}`;
			}

			// กำหนดค่าเริ่มต้น
			const categoryData = {
				...data,
				createdBy,
				status: CategoryStatus.ACTIVE,
				productCount: 0,
				seo: {
					...data.seo,
					slug,
				},
			};

			const category = new Category(categoryData);
			await category.save();

			// อัพเดทการใช้งานรูปภาพ
			if (data.imageId) {
				const image = await Gallery.findById(data.imageId);
				if (image) {
					await image.addUsage(category._id.toString());
				}
			}

			return category;
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถสร้างหมวดหมู่ได้: ${error.message}`);
		}
	}

	/**
	 * อัพเดทหมวดหมู่
	 */
	async updateCategory(
		categoryId: string, 
		data: IUpdateCategoryData, 
		updatedBy: string
	): Promise<ICategory> {
		try {
			const category = await Category.findById(categoryId);
			if (!category) {
				throw throwError.notFound("ไม่พบหมวดหมู่");
			}

			// ตรวจสอบหมวดหมู่แม่ใหม่ (ถ้ามี)
			if (data.parentId && data.parentId !== category.parentId) {
				// ป้องกันการสร้าง circular reference
				if (data.parentId === categoryId) {
					throw throwError.badRequest("ไม่สามารถตั้งตัวเองเป็นหมวดหมู่แม่ได้");
				}

				const parentCategory = await Category.findById(data.parentId);
				if (!parentCategory) {
					throw throwError.notFound("ไม่พบหมวดหมู่แม่");
				}

				// ตรวจสอบว่าหมวดหมู่แม่ไม่ใช่ลูกของหมวดหมู่นี้
				const isDescendant = await this.isDescendant(data.parentId, categoryId);
				if (isDescendant) {
					throw throwError.badRequest("ไม่สามารถตั้งหมวดหมู่ลูกเป็นหมวดหมู่แม่ได้");
				}
			}

			// ตรวจสอบรูปภาพใหม่ (ถ้ามี)
			if (data.imageId && data.imageId !== category.imageId) {
				const image = await Gallery.findById(data.imageId);
				if (!image) {
					throw throwError.notFound("ไม่พบรูปภาพ");
				}
			}

			// ตรวจสอบ slug ซ้ำ
			if (data.seo?.slug && data.seo.slug !== category.seo.slug) {
				const existingCategory = await Category.findOne({ 
					"seo.slug": data.seo.slug,
					_id: { $ne: categoryId }
				});
				if (existingCategory) {
					throw throwError.badRequest("Slug นี้ถูกใช้แล้ว");
				}
			}

			// เก็บข้อมูลเก่าไว้
			const oldImageId = category.imageId;

			// อัพเดทข้อมูล
			Object.assign(category, data, { updatedBy });
			await category.save();

			// อัพเดทการใช้งานรูปภาพ
			if (oldImageId !== category.imageId) {
				// ลบการใช้งานรูปเก่า
				if (oldImageId) {
					const oldImage = await Gallery.findById(oldImageId);
					if (oldImage) {
						await oldImage.removeUsage(categoryId);
					}
				}

				// เพิ่มการใช้งานรูปใหม่
				if (category.imageId) {
					const newImage = await Gallery.findById(category.imageId);
					if (newImage) {
						await newImage.addUsage(categoryId);
					}
				}
			}

			return category;
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถอัพเดทหมวดหมู่ได้: ${error.message}`);
		}
	}

	/**
	 * ลบหมวดหมู่
	 */
	async deleteCategory(categoryId: string): Promise<void> {
		try {
			const category = await Category.findById(categoryId);
			if (!category) {
				throw throwError.notFound("ไม่พบหมวดหมู่");
			}

			// ตรวจสอบว่ามีหมวดหมู่ลูกหรือไม่
			const childCategories = await Category.find({ parentId: categoryId });
			if (childCategories.length > 0) {
				throw throwError.badRequest("ไม่สามารถลบหมวดหมู่ที่มีหมวดหมู่ลูกได้");
			}

			// ตรวจสอบว่ามีสินค้าในหมวดหมู่หรือไม่
			if (category.productCount > 0) {
				throw throwError.badRequest("ไม่สามารถลบหมวดหมู่ที่มีสินค้าได้");
			}

			// ลบการใช้งานรูปภาพ
			if (category.imageId) {
				const image = await Gallery.findById(category.imageId);
				if (image) {
					await image.removeUsage(categoryId);
				}
			}

			await Category.findByIdAndDelete(categoryId);
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถลบหมวดหมู่ได้: ${error.message}`);
		}
	}

	/**
	 * ดึงหมวดหมู่ตาม ID
	 */
	async getCategoryById(categoryId: string): Promise<ICategory | null> {
		try {
			return await Category.findById(categoryId)
				.populate("imageId")
				.populate("parentId", "name seo.slug");
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถดึงข้อมูลหมวดหมู่ได้: ${error.message}`);
		}
	}

	/**
	 * ดึงหมวดหมู่ตาม slug
	 */
	async getCategoryBySlug(slug: string): Promise<ICategory | null> {
		try {
			return await Category.findOne({ "seo.slug": slug })
				.populate("imageId")
				.populate("parentId", "name seo.slug");
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถดึงข้อมูลหมวดหมู่ได้: ${error.message}`);
		}
	}

	/**
	 * ค้นหาหมวดหมู่
	 */
	async searchCategories(filter: ICategoryFilter) {
		try {
			const {
				search,
				parentId,
				level,
				status,
				isVisible,
				isFeatured,
				createdBy,
				page = 1,
				limit = 20,
				sortBy = "sortOrder",
				sortOrder = "asc"
			} = filter;

			// สร้าง query
			const query: any = {};

			// ค้นหาข้อความ
			if (search) {
				query.$or = [
					{ name: { $regex: search, $options: "i" } },
					{ description: { $regex: search, $options: "i" } }
				];
			}

			// กรองตามเงื่อนไข
			if (parentId !== undefined) {
				query.parentId = parentId === "null" ? null : parentId;
			}
			if (level !== undefined) query.level = level;
			if (status) query.status = status;
			if (isVisible !== undefined) query.isVisible = isVisible;
			if (isFeatured !== undefined) query.isFeatured = isFeatured;
			if (createdBy) query.createdBy = createdBy;

			// การเรียงลำดับ
			const sort: any = {};
			sort[sortBy] = sortOrder === "asc" ? 1 : -1;

			// คำนวณ pagination
			const skip = (page - 1) * limit;

			// ดึงข้อมูล
			const [categories, total] = await Promise.all([
				Category.find(query)
					.populate("imageId")
					.populate("parentId", "name seo.slug")
					.sort(sort)
					.skip(skip)
					.limit(limit),
				Category.countDocuments(query)
			]);

			return {
				categories,
				pagination: {
					page,
					limit,
					total,
					pages: Math.ceil(total / limit)
				}
			};
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถค้นหาหมวดหมู่ได้: ${error.message}`);
		}
	}

	/**
	 * ดึงหมวดหมู่หลัก
	 */
	async getRootCategories(): Promise<ICategory[]> {
		try {
			return await Category.getRootCategories().populate("imageId");
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถดึงหมวดหมู่หลักได้: ${error.message}`);
		}
	}

	/**
	 * ดึงหมวดหมู่เด่น
	 */
	async getFeaturedCategories(): Promise<ICategory[]> {
		try {
			return await Category.getFeaturedCategories().populate("imageId");
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถดึงหมวดหมู่เด่นได้: ${error.message}`);
		}
	}

	/**
	 * ดึงโครงสร้างหมวดหมู่แบบ tree
	 */
	async getCategoryTree() {
		try {
			return await Category.getTree();
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถดึงโครงสร้างหมวดหมู่ได้: ${error.message}`);
		}
	}

	/**
	 * ดึงหมวดหมู่ลูก
	 */
	async getChildCategories(parentId: string): Promise<ICategory[]> {
		try {
			return await Category.find({ 
				parentId,
				status: CategoryStatus.ACTIVE,
				isVisible: true 
			})
			.populate("imageId")
			.sort({ sortOrder: 1 });
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถดึงหมวดหมู่ลูกได้: ${error.message}`);
		}
	}

	/**
	 * ดึง breadcrumb ของหมวดหมู่
	 */
	async getCategoryBreadcrumb(categoryId: string) {
		try {
			const category = await Category.findById(categoryId);
			if (!category) {
				throw throwError.notFound("ไม่พบหมวดหมู่");
			}

			return await category.getBreadcrumb();
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถดึง breadcrumb ได้: ${error.message}`);
		}
	}

	/**
	 * ย้ายหมวดหมู่
	 */
	async moveCategory(categoryId: string, newParentId: string | null): Promise<ICategory> {
		try {
			const category = await Category.findById(categoryId);
			if (!category) {
				throw throwError.notFound("ไม่พบหมวดหมู่");
			}

			// ตรวจสอบหมวดหมู่แม่ใหม่
			if (newParentId) {
				if (newParentId === categoryId) {
					throw throwError.badRequest("ไม่สามารถตั้งตัวเองเป็นหมวดหมู่แม่ได้");
				}

				const newParent = await Category.findById(newParentId);
				if (!newParent) {
					throw throwError.notFound("ไม่พบหมวดหมู่แม่ใหม่");
				}

				// ตรวจสอบ circular reference
				const isDescendant = await this.isDescendant(newParentId, categoryId);
				if (isDescendant) {
					throw throwError.badRequest("ไม่สามารถย้ายไปยังหมวดหมู่ลูกได้");
				}
			}

			// อัพเดทหมวดหมู่
			category.parentId = newParentId;
			await category.save(); // จะทำการอัพเดท level และ path อัตโนมัติ

			// อัพเดทหมวดหมู่ลูกทั้งหมด
			await this.updateChildrenPaths(categoryId);

			return category;
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถย้ายหมวดหมู่ได้: ${error.message}`);
		}
	}

	/**
	 * เรียงลำดับหมวดหมู่
	 */
	async reorderCategories(categoryOrders: Array<{ id: string; sortOrder: number }>): Promise<void> {
		try {
			await Promise.all(
				categoryOrders.map(async ({ id, sortOrder }) => {
					await Category.findByIdAndUpdate(id, { sortOrder });
				})
			);
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถเรียงลำดับหมวดหมู่ได้: ${error.message}`);
		}
	}

	/**
	 * ตรวจสอบว่าหมวดหมู่หนึ่งเป็นลูกของอีกหมวดหมู่หนึ่งหรือไม่
	 */
	private async isDescendant(ancestorId: string, descendantId: string): Promise<boolean> {
		const descendant = await Category.findById(descendantId);
		if (!descendant || !descendant.parentId) {
			return false;
		}

		if (descendant.parentId === ancestorId) {
			return true;
		}

		return await this.isDescendant(ancestorId, descendant.parentId);
	}

	/**
	 * อัพเดท path ของหมวดหมู่ลูกทั้งหมด
	 */
	private async updateChildrenPaths(parentId: string): Promise<void> {
		const children = await Category.find({ parentId });
		
		for (const child of children) {
			// บังคับให้ทำการอัพเดท path
			child.markModified('parentId');
			await child.save();
			
			// อัพเดทลูกของลูก
			await this.updateChildrenPaths(child._id.toString());
		}
	}

	/**
	 * ดึงสถิติหมวดหมู่
	 */
	async getCategoryStats() {
		try {
			const [
				totalCategories,
				activeCategories,
				categoriesByLevel,
				topCategories,
				emptyCategories
			] = await Promise.all([
				Category.countDocuments(),
				Category.countDocuments({ status: CategoryStatus.ACTIVE }),
				Category.aggregate([
					{ $group: { _id: "$level", count: { $sum: 1 } } },
					{ $sort: { _id: 1 } }
				]),
				Category.find({ status: CategoryStatus.ACTIVE })
					.sort({ productCount: -1 })
					.limit(10)
					.select("name productCount"),
				Category.find({ 
					status: CategoryStatus.ACTIVE,
					productCount: 0 
				}).countDocuments()
			]);

			return {
				totalCategories,
				activeCategories,
				categoriesByLevel,
				topCategories,
				emptyCategories
			};
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถดึงสถิติหมวดหมู่ได้: ${error.message}`);
		}
	}
}

export const categoryService = new CategoryService();