import { z } from "zod";
import { FileType, FileStatus } from "./gallery.model";

// Schema สำหรับการอัพโหลดไฟล์เดี่ยว
export const uploadFileSchema = z.object({
	title: z.string().min(1, "กรุณากรอกชื่อไฟล์").max(200, "ชื่อไฟล์ต้องไม่เกิน 200 ตัวอักษร"),
	description: z.string().max(1000, "รายละเอียดต้องไม่เกิน 1000 ตัวอักษร").optional(),
	altText: z.string().max(200, "Alt text ต้องไม่เกิน 200 ตัวอักษร").optional(),
	folder: z.string().default("general"),
	tags: z.array(z.string()).optional(),
	generateThumbnails: z.boolean().default(true),
});

// Schema สำหรับการอัพโหลดหลายไฟล์
export const uploadMultipleFilesSchema = z.object({
	titles: z.array(z.string().min(1, "กรุณากรอกชื่อไฟล์").max(200)),
	descriptions: z.array(z.string().max(1000)).optional(),
	altTexts: z.array(z.string().max(200)).optional(),
	folder: z.string().default("general"),
	tags: z.array(z.string()).optional(),
	generateThumbnails: z.boolean().default(true),
});

// Schema สำหรับการค้นหาไฟล์
export const searchFilesSchema = z.object({
	search: z.string().optional(),
	fileType: z.nativeEnum(FileType).optional(),
	folder: z.string().optional(),
	tags: z.array(z.string()).optional(),
	uploadedBy: z.string().optional(),
	status: z.nativeEnum(FileStatus).optional(),
	page: z.number().min(1).default(1),
	limit: z.number().min(1).max(100).default(20),
	sortBy: z.string().default("createdAt"),
	sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

// Schema สำหรับการอัพเดทไฟล์
export const updateFileSchema = z.object({
	title: z.string().min(1, "กรุณากรอกชื่อไฟล์").max(200).optional(),
	description: z.string().max(1000).optional(),
	altText: z.string().max(200).optional(),
	tags: z.array(z.string()).optional(),
	status: z.nativeEnum(FileStatus).optional(),
});

// Schema สำหรับการสร้าง transformation
export const createTransformationSchema = z.object({
	name: z.string().min(1, "กรุณากรอกชื่อ transformation"),
	width: z.number().min(1, "ความกว้างต้องมากกว่า 0").optional(),
	height: z.number().min(1, "ความสูงต้องมากกว่า 0").optional(),
	crop: z.string().optional(),
	quality: z.union([z.string(), z.number()]).optional(),
	format: z.string().optional(),
});

// Schema สำหรับการลบหลายไฟล์
export const deleteMultipleFilesSchema = z.object({
	fileIds: z.array(z.string()).min(1, "กรุณาเลือกไฟล์ที่ต้องการลบ"),
});

// Schema สำหรับการทำความสะอาดไฟล์
export const cleanupFilesSchema = z.object({
	days: z.number().min(1).default(30),
});

// Schema สำหรับการดึงไฟล์ตาม folder หรือ tags
export const getFilesByFolderSchema = z.object({
	limit: z.number().min(1).max(100).default(50),
});

export const getFilesByTagsSchema = z.object({
	tags: z.string().min(1, "กรุณากรอก tags (คั่นด้วย comma)"),
	limit: z.number().min(1).max(100).default(50),
});

// Type exports
export type UploadFileInput = z.infer<typeof uploadFileSchema>;
export type UploadMultipleFilesInput = z.infer<typeof uploadMultipleFilesSchema>;
export type SearchFilesInput = z.infer<typeof searchFilesSchema>;
export type UpdateFileInput = z.infer<typeof updateFileSchema>;
export type CreateTransformationInput = z.infer<typeof createTransformationSchema>;
export type DeleteMultipleFilesInput = z.infer<typeof deleteMultipleFilesSchema>;
export type CleanupFilesInput = z.infer<typeof cleanupFilesSchema>;
export type GetFilesByFolderInput = z.infer<typeof getFilesByFolderSchema>;
export type GetFilesByTagsInput = z.infer<typeof getFilesByTagsSchema>;