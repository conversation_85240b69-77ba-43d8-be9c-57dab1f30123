import { Product, type IProduct, ProductStatus, ProductType } from "./product.model";
import { Gallery, type IGallery } from "./gallery.model";
import { Category } from "./category.model";
import { cloudinaryUpload } from "@/core/utils/cloudinary-upload";
import { throwError } from "@/core/utils/error";

// Interface สำหรับการสร้างสินค้า
export interface ICreateProductData {
	name: string;
	description: string;
	shortDescription?: string;
	type: ProductType;
	pricing: {
		basePrice: number;
		salePrice?: number;
		costPrice?: number;
		currency?: string;
		taxRate?: number;
		isOnSale?: boolean;
		saleStartDate?: Date;
		saleEndDate?: Date;
	};
	inventory: {
		trackQuantity?: boolean;
		quantity?: number;
		lowStockThreshold?: number;
		allowBackorder?: boolean;
	};
	shipping: {
		weight?: number;
		dimensions?: {
			length: number;
			width: number;
			height: number;
		};
		shippingClass?: string;
		requiresShipping?: boolean;
	};
	categoryIds?: string[];
	tags?: string[];
	features?: string[];
	specifications?: Record<string, any>;
	variants?: Array<{
		name: string;
		value: string;
		priceAdjustment?: number;
		stockQuantity?: number;
		sku?: string;
		isDefault?: boolean;
	}>;
	seo?: {
		metaTitle?: string;
		metaDescription?: string;
		keywords?: string[];
		slug?: string;
	};
}

// Interface สำหรับการอัพเดทสินค้า
export interface IUpdateProductData extends Partial<ICreateProductData> {
	status?: ProductStatus;
}

// Interface สำหรับการค้นหาสินค้า
export interface IProductFilter {
	search?: string;
	type?: ProductType;
	status?: ProductStatus;
	categoryIds?: string[];
	tags?: string[];
	priceMin?: number;
	priceMax?: number;
	inStock?: boolean;
	isOnSale?: boolean;
	createdBy?: string;
	page?: number;
	limit?: number;
	sortBy?: string;
	sortOrder?: "asc" | "desc";
}

// Interface สำหรับการอัพโหลดไฟล์ดิจิทัล
export interface IDigitalFileUpload {
	file: string | Buffer | File;
	name: string;
	fileType: string;
	isPreviewable?: boolean;
}

export class ProductService {
	/**
	 * สร้างสินค้าใหม่
	 */
	async createProduct(data: ICreateProductData, createdBy: string): Promise<IProduct> {
		try {
			// ตรวจสอบหมวดหมู่
			if (data.categoryIds && data.categoryIds.length > 0) {
				const categories = await Category.find({ 
					_id: { $in: data.categoryIds },
					status: "active" 
				});
				
				if (categories.length !== data.categoryIds.length) {
					throw throwError.badRequest("หมวดหมู่บางรายการไม่ถูกต้อง");
				}
			}

			// สร้าง slug อัตโนมัติถ้าไม่มี
			let slug = data.seo?.slug;
			if (!slug) {
				slug = data.name
					.toLowerCase()
					.replace(/[^a-z0-9\u0E00-\u0E7F]+/g, "-")
					.replace(/^-+|-+$/g, "");
			}

			// ตรวจสอบ slug ซ้ำ
			const existingProduct = await Product.findOne({ "seo.slug": slug });
			if (existingProduct) {
				slug = `${slug}-${Date.now()}`;
			}

			const productData = {
				...data,
				createdBy,
				seo: {
					...data.seo,
					slug,
				},
				galleryImageIds: [],
				digitalFiles: [],
				viewCount: 0,
				purchaseCount: 0,
				rating: {
					average: 0,
					count: 0,
				},
			};

			const product = new Product(productData);
			await product.save();

			// อัพเดทจำนวนสินค้าในหมวดหมู่
			if (data.categoryIds) {
				await Promise.all(
					data.categoryIds.map(async (categoryId) => {
						const category = await Category.findById(categoryId);
						if (category) {
							await category.updateProductCount();
						}
					})
				);
			}

			return product;
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถสร้างสินค้าได้: ${error.message}`);
		}
	}

	/**
	 * อัพเดทสินค้า
	 */
	async updateProduct(
		productId: string, 
		data: IUpdateProductData, 
		updatedBy: string
	): Promise<IProduct> {
		try {
			const product = await Product.findById(productId);
			if (!product) {
				throw throwError.notFound("ไม่พบสินค้า");
			}

			// ตรวจสอบหมวดหมู่ใหม่
			if (data.categoryIds && data.categoryIds.length > 0) {
				const categories = await Category.find({ 
					_id: { $in: data.categoryIds },
					status: "active" 
				});
				
				if (categories.length !== data.categoryIds.length) {
					throw throwError.badRequest("หมวดหมู่บางรายการไม่ถูกต้อง");
				}
			}

			// ตรวจสอบ slug ซ้ำ
			if (data.seo?.slug && data.seo.slug !== product.seo.slug) {
				const existingProduct = await Product.findOne({ 
					"seo.slug": data.seo.slug,
					_id: { $ne: productId }
				});
				if (existingProduct) {
					throw throwError.badRequest("Slug นี้ถูกใช้แล้ว");
				}
			}

			// เก็บหมวดหมู่เก่าไว้สำหรับอัพเดท count
			const oldCategoryIds = product.categoryIds;

			// อัพเดทข้อมูล
			Object.assign(product, data, { updatedBy });
			await product.save();

			// อัพเดทจำนวนสินค้าในหมวดหมู่
			const allCategoryIds = [
				...new Set([...oldCategoryIds, ...(data.categoryIds || [])])
			];
			
			await Promise.all(
				allCategoryIds.map(async (categoryId) => {
					const category = await Category.findById(categoryId);
					if (category) {
						await category.updateProductCount();
					}
				})
			);

			return product;
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถอัพเดทสินค้าได้: ${error.message}`);
		}
	}

	/**
	 * ลบสินค้า
	 */
	async deleteProduct(productId: string): Promise<void> {
		try {
			const product = await Product.findById(productId);
			if (!product) {
				throw throwError.notFound("ไม่พบสินค้า");
			}

			// ลบการเชื่อมโยงกับรูปภาพ
			if (product.galleryImageIds.length > 0) {
				await Promise.all(
					product.galleryImageIds.map(async (imageId) => {
						const image = await Gallery.findById(imageId);
						if (image) {
							await image.removeUsage(productId);
						}
					})
				);
			}

			// ลบไฟล์ดิจิทัลจาก Cloudinary
			if (product.digitalFiles.length > 0) {
				await Promise.all(
					product.digitalFiles.map(async (file) => {
						if (file.cloudinaryPublicId) {
							try {
								await cloudinaryUpload.deleteFile(file.cloudinaryPublicId, "raw");
							} catch (error) {
								console.error("Error deleting digital file:", error);
							}
						}
					})
				);
			}

			await Product.findByIdAndDelete(productId);

			// อัพเดทจำนวนสินค้าในหมวดหมู่
			await Promise.all(
				product.categoryIds.map(async (categoryId) => {
					const category = await Category.findById(categoryId);
					if (category) {
						await category.updateProductCount();
					}
				})
			);
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถลบสินค้าได้: ${error.message}`);
		}
	}

	/**
	 * ดึงสินค้าตาม ID
	 */
	async getProductById(productId: string, incrementView = false): Promise<IProduct | null> {
		try {
			const product = await Product.findById(productId)
				.populate("categoryIds", "name seo.slug")
				.populate("coverImageId")
				.populate("galleryImageIds");

			if (!product) {
				return null;
			}

			// เพิ่ม view count
			if (incrementView) {
				product.viewCount += 1;
				await product.save();
			}

			return product;
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถดึงข้อมูลสินค้าได้: ${error.message}`);
		}
	}

	/**
	 * ดึงสินค้าตาม slug
	 */
	async getProductBySlug(slug: string, incrementView = false): Promise<IProduct | null> {
		try {
			const product = await Product.findOne({ "seo.slug": slug })
				.populate("categoryIds", "name seo.slug")
				.populate("coverImageId")
				.populate("galleryImageIds");

			if (!product) {
				return null;
			}

			// เพิ่ม view count
			if (incrementView) {
				product.viewCount += 1;
				await product.save();
			}

			return product;
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถดึงข้อมูลสินค้าได้: ${error.message}`);
		}
	}

	/**
	 * ค้นหาสินค้า
	 */
	async searchProducts(filter: IProductFilter) {
		try {
			const {
				search,
				type,
				status,
				categoryIds,
				tags,
				priceMin,
				priceMax,
				inStock,
				isOnSale,
				createdBy,
				page = 1,
				limit = 20,
				sortBy = "createdAt",
				sortOrder = "desc"
			} = filter;

			// สร้าง query
			const query: any = {};

			// ค้นหาข้อความ
			if (search) {
				query.$or = [
					{ name: { $regex: search, $options: "i" } },
					{ description: { $regex: search, $options: "i" } },
					{ tags: { $in: [new RegExp(search, "i")] } }
				];
			}

			// กรองตามประเภท
			if (type) query.type = type;
			if (status) query.status = status;
			if (createdBy) query.createdBy = createdBy;

			// กรองตามหมวดหมู่
			if (categoryIds && categoryIds.length > 0) {
				query.categoryIds = { $in: categoryIds };
			}

			// กรองตามแท็ก
			if (tags && tags.length > 0) {
				query.tags = { $in: tags };
			}

			// กรองตามราคา
			if (priceMin !== undefined || priceMax !== undefined) {
				query["pricing.basePrice"] = {};
				if (priceMin !== undefined) query["pricing.basePrice"].$gte = priceMin;
				if (priceMax !== undefined) query["pricing.basePrice"].$lte = priceMax;
			}

			// กรองตามสต็อก
			if (inStock !== undefined) {
				if (inStock) {
					query["inventory.stockStatus"] = { $ne: "out_of_stock" };
				} else {
					query["inventory.stockStatus"] = "out_of_stock";
				}
			}

			// กรองสินค้าลดราคา
			if (isOnSale !== undefined) {
				query["pricing.isOnSale"] = isOnSale;
			}

			// การเรียงลำดับ
			const sort: any = {};
			sort[sortBy] = sortOrder === "asc" ? 1 : -1;

			// คำนวณ pagination
			const skip = (page - 1) * limit;

			// ดึงข้อมูล
			const [products, total] = await Promise.all([
				Product.find(query)
					.populate("categoryIds", "name seo.slug")
					.populate("coverImageId")
					.sort(sort)
					.skip(skip)
					.limit(limit),
				Product.countDocuments(query)
			]);

			return {
				products,
				pagination: {
					page,
					limit,
					total,
					pages: Math.ceil(total / limit)
				}
			};
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถค้นหาสินค้าได้: ${error.message}`);
		}
	}

	/**
	 * เพิ่มรูปภาพให้สินค้า
	 */
	async addProductImages(
		productId: string, 
		imageIds: string[], 
		setCover = false
	): Promise<IProduct> {
		try {
			const product = await Product.findById(productId);
			if (!product) {
				throw throwError.notFound("ไม่พบสินค้า");
			}

			// ตรวจสอบว่ารูปภาพมีอยู่จริง
			const images = await Gallery.find({ _id: { $in: imageIds } });
			if (images.length !== imageIds.length) {
				throw throwError.badRequest("รูปภาพบางรายการไม่ถูกต้อง");
			}

			// เพิ่มรูปภาพ
			const newImageIds = imageIds.filter(id => !product.galleryImageIds.includes(id));
			product.galleryImageIds.push(...newImageIds);

			// ตั้งรูปปกถ้าต้องการ
			if (setCover && imageIds.length > 0) {
				product.coverImageId = imageIds[0];
			}

			await product.save();

			// อัพเดทการใช้งานในรูปภาพ
			await Promise.all(
				newImageIds.map(async (imageId) => {
					const image = await Gallery.findById(imageId);
					if (image) {
						await image.addUsage(productId);
					}
				})
			);

			return product;
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถเพิ่มรูปภาพได้: ${error.message}`);
		}
	}

	/**
	 * ลบรูปภาพจากสินค้า
	 */
	async removeProductImages(productId: string, imageIds: string[]): Promise<IProduct> {
		try {
			const product = await Product.findById(productId);
			if (!product) {
				throw throwError.notFound("ไม่พบสินค้า");
			}

			// ลบรูปภาพ
			product.galleryImageIds = product.galleryImageIds.filter(
				id => !imageIds.includes(id)
			);

			// ลบรูปปกถ้าถูกลบ
			if (product.coverImageId && imageIds.includes(product.coverImageId)) {
				product.coverImageId = undefined;
			}

			await product.save();

			// อัพเดทการใช้งานในรูปภาพ
			await Promise.all(
				imageIds.map(async (imageId) => {
					const image = await Gallery.findById(imageId);
					if (image) {
						await image.removeUsage(productId);
					}
				})
			);

			return product;
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถลบรูปภาพได้: ${error.message}`);
		}
	}

	/**
	 * อัพโหลดไฟล์ดิจิทัล
	 */
	async uploadDigitalFile(
		productId: string, 
		fileData: IDigitalFileUpload
	): Promise<IProduct> {
		try {
			const product = await Product.findById(productId);
			if (!product) {
				throw throwError.notFound("ไม่พบสินค้า");
			}

			// อัพโหลดไฟล์ไป Cloudinary
			const uploadResult = await cloudinaryUpload.uploadPublic(fileData.file, {
				folder: `products/${productId}/files`,
				resourceType: "raw"
			});

			// เพิ่มไฟล์ดิจิทัลในสินค้า
			const digitalFile = {
				name: fileData.name,
				originalName: fileData.name,
				fileType: fileData.fileType as any,
				mimeType: uploadResult.format,
				size: uploadResult.bytes,
				downloadUrl: uploadResult.secureUrl,
				cloudinaryPublicId: uploadResult.publicId,
				isPreviewable: fileData.isPreviewable || false,
				downloadCount: 0,
				uploadedAt: new Date()
			};

			product.digitalFiles.push(digitalFile);
			await product.save();

			return product;
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถอัพโหลดไฟล์ได้: ${error.message}`);
		}
	}

	/**
	 * ลบไฟล์ดิจิทัล
	 */
	async removeDigitalFile(productId: string, fileIndex: number): Promise<IProduct> {
		try {
			const product = await Product.findById(productId);
			if (!product) {
				throw throwError.notFound("ไม่พบสินค้า");
			}

			if (fileIndex < 0 || fileIndex >= product.digitalFiles.length) {
				throw throwError.badRequest("ไฟล์ไม่ถูกต้อง");
			}

			const file = product.digitalFiles[fileIndex];

			// ลบไฟล์จาก Cloudinary
			if (file.cloudinaryPublicId) {
				try {
					await cloudinaryUpload.deleteFile(file.cloudinaryPublicId, "raw");
				} catch (error) {
					console.error("Error deleting file from Cloudinary:", error);
				}
			}

			// ลบไฟล์จากสินค้า
			product.digitalFiles.splice(fileIndex, 1);
			await product.save();

			return product;
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถลบไฟล์ได้: ${error.message}`);
		}
	}

	/**
	 * อัพเดทสถิติการซื้อ
	 */
	async updatePurchaseStats(productId: string): Promise<void> {
		try {
			await Product.findByIdAndUpdate(productId, {
				$inc: { purchaseCount: 1 }
			});
		} catch (error: any) {
			console.error("Error updating purchase stats:", error);
		}
	}

	/**
	 * อัพเดทคะแนนรีวิว
	 */
	async updateRating(productId: string, newRating: number): Promise<void> {
		try {
			const product = await Product.findById(productId);
			if (!product) return;

			const currentAverage = product.rating.average;
			const currentCount = product.rating.count;
			
			const newCount = currentCount + 1;
			const newAverage = ((currentAverage * currentCount) + newRating) / newCount;

			product.rating.average = Math.round(newAverage * 10) / 10; // ปัดเศษ 1 ตำแหน่ง
			product.rating.count = newCount;

			await product.save();
		} catch (error: any) {
			console.error("Error updating rating:", error);
		}
	}
}

export const productService = new ProductService();