import { User } from "@/modules/user/user.model";
import { throwError } from "@/core/utils/error";

// Interface สำหรับ MoneyPoint Transaction
export interface IMoneyPointTransaction {
	userId: string;
	amount: number;
	type: 'deduct' | 'add';
	reason: string;
	feature: string;
	metadata?: Record<string, any>;
}

// ราคา AI Features (เป็น points)
export const AI_FEATURE_COSTS = {
	AUTO_TAG: 2,           // 2 points ต่อรูป
	AUTO_ALT_TEXT: 1,      // 1 point ต่อรูป
	OBJECT_DETECTION: 3,   // 3 points ต่อรูป
	FACE_DETECTION: 2,     // 2 points ต่อรูป
	CONTENT_MODERATION: 1, // 1 point ต่อรูป
	BACKGROUND_REMOVAL: 5, // 5 points ต่อรูป
	BATCH_PROCESSING: 1,   // 1 point เพิ่มเติมสำหรับ batch
};

export class MoneyPointService {
	/**
	 * ตรวจสอบยอด MoneyPoint ของ User
	 */
	async getUserBalance(userId: string): Promise<number> {
		try {
			const user = await User.findById(userId);
			if (!user) {
				throw throwError.notFound("ไม่พบผู้ใช้งาน");
			}
			
			// ใช้ field moneyPoint ตาม User model
			return user.moneyPoint || 0;
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถดึงยอด MoneyPoint ได้: ${error.message}`);
		}
	}

	/**
	 * หัก MoneyPoint จาก User
	 */
	async deductPoints(transaction: IMoneyPointTransaction): Promise<{
		success: boolean;
		newBalance: number;
		transactionId: string;
	}> {
		try {
			const user = await User.findById(transaction.userId);
			if (!user) {
				throw throwError.notFound("ไม่พบผู้ใช้งาน");
			}

			const currentBalance = user.moneyPoint || 0;
			
			// ตรวจสอบยอดเพียงพอ
			if (currentBalance < transaction.amount) {
				throw throwError.badRequest(
					`MoneyPoint ไม่เพียงพอ (มี ${currentBalance} points ต้องการ ${transaction.amount} points)`
				);
			}

			// หัก points
			const newBalance = currentBalance - transaction.amount;
			user.moneyPoint = newBalance;

			// บันทึก transaction log (สมมติว่ามี field moneyPointTransactions)
			const transactionLog = {
				id: `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
				amount: transaction.amount,
				type: transaction.type,
				reason: transaction.reason,
				feature: transaction.feature,
				metadata: transaction.metadata,
				timestamp: new Date(),
				balanceAfter: newBalance
			};

			if (!user.moneyPointTransactions) {
				user.moneyPointTransactions = [];
			}
			user.moneyPointTransactions.push(transactionLog);

			await user.save();

			return {
				success: true,
				newBalance,
				transactionId: transactionLog.id
			};
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถหัก MoneyPoint ได้: ${error.message}`);
		}
	}

	/**
	 * เพิ่ม MoneyPoint ให้ User
	 */
	async addPoints(transaction: IMoneyPointTransaction): Promise<{
		success: boolean;
		newBalance: number;
		transactionId: string;
	}> {
		try {
			const user = await User.findById(transaction.userId);
			if (!user) {
				throw throwError.notFound("ไม่พบผู้ใช้งาน");
			}

			const currentBalance = user.moneyPoint || 0;
			const newBalance = currentBalance + transaction.amount;
			user.moneyPoint = newBalance;

			// บันทึก transaction log
			const transactionLog = {
				id: `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
				amount: transaction.amount,
				type: transaction.type,
				reason: transaction.reason,
				feature: transaction.feature,
				metadata: transaction.metadata,
				timestamp: new Date(),
				balanceAfter: newBalance
			};

			if (!user.moneyPointTransactions) {
				user.moneyPointTransactions = [];
			}
			user.moneyPointTransactions.push(transactionLog);

			await user.save();

			return {
				success: true,
				newBalance,
				transactionId: transactionLog.id
			};
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถเพิ่ม MoneyPoint ได้: ${error.message}`);
		}
	}

	/**
	 * คำนวณค่าใช้จ่าย AI Features
	 */
	calculateAICost(features: string[], imageCount: number = 1): number {
		let totalCost = 0;
		
		features.forEach(feature => {
			const cost = AI_FEATURE_COSTS[feature as keyof typeof AI_FEATURE_COSTS];
			if (cost) {
				totalCost += cost * imageCount;
			}
		});

		// เพิ่มค่า batch processing ถ้ามีหลายรูป
		if (imageCount > 1) {
			totalCost += AI_FEATURE_COSTS.BATCH_PROCESSING * (imageCount - 1);
		}

		return totalCost;
	}

	/**
	 * ตรวจสอบและหัก points สำหรับ AI Features
	 */
	async processAIFeaturePayment(
		userId: string,
		features: string[],
		imageCount: number = 1,
		metadata?: Record<string, any>
	): Promise<{
		success: boolean;
		cost: number;
		newBalance: number;
		transactionId: string;
	}> {
		try {
			const cost = this.calculateAICost(features, imageCount);
			
			if (cost === 0) {
				// ไม่มีค่าใช้จ่าย
				const balance = await this.getUserBalance(userId);
				return {
					success: true,
					cost: 0,
					newBalance: balance,
					transactionId: 'free'
				};
			}

			const result = await this.deductPoints({
				userId,
				amount: cost,
				type: 'deduct',
				reason: `AI Features: ${features.join(', ')}`,
				feature: 'AI_PROCESSING',
				metadata: {
					features,
					imageCount,
					costPerFeature: AI_FEATURE_COSTS,
					...metadata
				}
			});

			return {
				success: result.success,
				cost,
				newBalance: result.newBalance,
				transactionId: result.transactionId
			};
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถประมวลผลการชำระเงินได้: ${error.message}`);
		}
	}

	/**
	 * ดึงประวัติการใช้งาน MoneyPoint
	 */
	async getTransactionHistory(
		userId: string,
		options: {
			page?: number;
			limit?: number;
			feature?: string;
			type?: 'deduct' | 'add';
		} = {}
	): Promise<{
		transactions: any[];
		pagination: {
			page: number;
			limit: number;
			total: number;
			pages: number;
		};
	}> {
		try {
			const { page = 1, limit = 20, feature, type } = options;
			
			const user = await User.findById(userId);
			if (!user) {
				throw throwError.notFound("ไม่พบผู้ใช้งาน");
			}

			let transactions = user.moneyPointTransactions || [];

			// กรองตามเงื่อนไข
			if (feature) {
				transactions = transactions.filter(t => t.feature === feature);
			}
			if (type) {
				transactions = transactions.filter(t => t.type === type);
			}

			// เรียงลำดับตามวันที่ล่าสุด
			transactions.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

			// Pagination
			const total = transactions.length;
			const skip = (page - 1) * limit;
			const paginatedTransactions = transactions.slice(skip, skip + limit);

			return {
				transactions: paginatedTransactions,
				pagination: {
					page,
					limit,
					total,
					pages: Math.ceil(total / limit)
				}
			};
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถดึงประวัติการใช้งานได้: ${error.message}`);
		}
	}

	/**
	 * ดึงสถิติการใช้งาน MoneyPoint
	 */
	async getUserStats(userId: string): Promise<{
		currentBalance: number;
		totalSpent: number;
		totalEarned: number;
		mostUsedFeature: string;
		transactionCount: number;
	}> {
		try {
			const user = await User.findById(userId);
			if (!user) {
				throw throwError.notFound("ไม่พบผู้ใช้งาน");
			}

			const transactions = user.moneyPointTransactions || [];
			const currentBalance = user.moneyPoint || 0;

			// คำนวณสถิติ
			const totalSpent = transactions
				.filter(t => t.type === 'deduct')
				.reduce((sum, t) => sum + t.amount, 0);

			const totalEarned = transactions
				.filter(t => t.type === 'add')
				.reduce((sum, t) => sum + t.amount, 0);

			// หา feature ที่ใช้บ่อยที่สุด
			const featureUsage: Record<string, number> = {};
			transactions.forEach(t => {
				if (t.type === 'deduct') {
					featureUsage[t.feature] = (featureUsage[t.feature] || 0) + 1;
				}
			});

			const mostUsedFeature = Object.keys(featureUsage).reduce((a, b) => 
				featureUsage[a] > featureUsage[b] ? a : b, 'NONE'
			);

			return {
				currentBalance,
				totalSpent,
				totalEarned,
				mostUsedFeature,
				transactionCount: transactions.length
			};
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถดึงสถิติได้: ${error.message}`);
		}
	}
}

export const moneyPointService = new MoneyPointService();