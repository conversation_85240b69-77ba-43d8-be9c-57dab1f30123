import { Schema, model, type Document } from "mongoose";

// Enum สำหรับสถานะหมวดหมู่
export enum CategoryStatus {
	ACTIVE = "active",
	INACTIVE = "inactive",
}

// Interface สำหรับ SEO ของหมวดหมู่
export interface ICategorySEO {
	metaTitle?: string;
	metaDescription?: string;
	keywords?: string[];
	slug: string;
}

// Interface สำหรับหมวดหมู่สินค้า
export interface ICategory extends Document {
	// ข้อมูลพื้นฐาน
	name: string;
	description?: string;
	
	// โครงสร้างแบบ hierarchical (หมวดหมู่ย่อย)
	parentId?: string; // ID ของหมวดหมู่แม่
	level: number; // ระดับของหมวดหมู่ (0 = หมวดหมู่หลัก)
	path: string; // เส้นทางแบบ "/electronics/smartphones"
	
	// รูปภาพ
	imageId?: string; // เชื่อมกับ Gallery
	iconClass?: string; // CSS class สำหรับไอคอน
	
	// การเรียงลำดับ
	sortOrder: number;
	
	// สถานะ
	status: CategoryStatus;
	
	// SEO
	seo: ICategorySEO;
	
	// การตั้งค่า
	isVisible: boolean; // แสดงในเมนูหรือไม่
	isFeatured: boolean; // หมวดหมู่เด่น
	
	// สถิติ
	productCount: number; // จำนวนสินค้าในหมวดหมู่
	
	// การจัดการ
	createdBy: string; // User ID
	updatedBy?: string; // User ID
	
	// วันที่
	createdAt: Date;
	updatedAt: Date;
}

// Schema สำหรับ SEO
const categorySEOSchema = new Schema<ICategorySEO>({
	metaTitle: { type: String },
	metaDescription: { type: String },
	keywords: [{ type: String }],
	slug: { type: String, required: true, unique: true },
});

// Main Category Schema
const categorySchema = new Schema<ICategory>({
	// ข้อมูลพื้นฐาน
	name: { 
		type: String, 
		required: true,
		trim: true,
		maxlength: 100 
	},
	description: { 
		type: String,
		maxlength: 1000 
	},
	
	// โครงสร้างแบบ hierarchical
	parentId: { 
		type: String,
		ref: "Category",
		default: null 
	},
	level: { 
		type: Number, 
		default: 0,
		min: 0 
	},
	path: { 
		type: String, 
		required: true 
	},
	
	// รูปภาพ
	imageId: { 
		type: String,
		ref: "Gallery" 
	},
	iconClass: { 
		type: String 
	},
	
	// การเรียงลำดับ
	sortOrder: { 
		type: Number, 
		default: 0 
	},
	
	// สถานะ
	status: { 
		type: String, 
		enum: Object.values(CategoryStatus),
		default: CategoryStatus.ACTIVE 
	},
	
	// SEO
	seo: { 
		type: categorySEOSchema, 
		required: true 
	},
	
	// การตั้งค่า
	isVisible: { 
		type: Boolean, 
		default: true 
	},
	isFeatured: { 
		type: Boolean, 
		default: false 
	},
	
	// สถิติ
	productCount: { 
		type: Number, 
		default: 0 
	},
	
	// การจัดการ
	createdBy: { 
		type: String,
		required: true,
		ref: "User" 
	},
	updatedBy: { 
		type: String,
		ref: "User" 
	},
}, {
	timestamps: true,
	toJSON: { virtuals: true },
	toObject: { virtuals: true },
});

// Indexes สำหรับประสิทธิภาพ
categorySchema.index({ "seo.slug": 1 });
categorySchema.index({ parentId: 1, sortOrder: 1 });
categorySchema.index({ level: 1 });
categorySchema.index({ status: 1, isVisible: 1 });
categorySchema.index({ isFeatured: 1 });
categorySchema.index({ path: 1 });

// Virtual สำหรับตรวจสอบว่าเป็นหมวดหมู่หลักหรือไม่
categorySchema.virtual("isRoot").get(function() {
	return this.level === 0 && !this.parentId;
});

// Virtual สำหรับตรวจสอบว่ามีหมวดหมู่ย่อยหรือไม่
categorySchema.virtual("hasChildren").get(function() {
	// จะต้องใช้ใน populate หรือ aggregate
	return false; // placeholder
});

// Method สำหรับอัพเดทจำนวนสินค้า
categorySchema.methods.updateProductCount = async function() {
	const Product = model("Product");
	const count = await Product.countDocuments({ 
		categoryIds: this._id,
		status: "active" 
	});
	this.productCount = count;
	return this.save();
};

// Method สำหรับดึงหมวดหมู่ย่อยทั้งหมด
categorySchema.methods.getChildren = function() {
	return model("Category").find({ parentId: this._id }).sort({ sortOrder: 1 });
};

// Method สำหรับดึงหมวดหมู่แม่
categorySchema.methods.getParent = function() {
	if (!this.parentId) return null;
	return model("Category").findById(this.parentId);
};

// Method สำหรับดึงเส้นทางทั้งหมด (breadcrumb)
categorySchema.methods.getBreadcrumb = async function() {
	const breadcrumb = [];
	let current = this;
	
	while (current) {
		breadcrumb.unshift({
			id: current._id,
			name: current.name,
			slug: current.seo.slug,
			level: current.level
		});
		
		if (current.parentId) {
			current = await model("Category").findById(current.parentId);
		} else {
			current = null;
		}
	}
	
	return breadcrumb;
};

// Static method สำหรับดึงหมวดหมู่หลัก
categorySchema.statics.getRootCategories = function() {
	return this.find({ 
		level: 0, 
		parentId: null,
		status: CategoryStatus.ACTIVE,
		isVisible: true 
	}).sort({ sortOrder: 1 });
};

// Static method สำหรับดึงหมวดหมู่เด่น
categorySchema.statics.getFeaturedCategories = function() {
	return this.find({ 
		isFeatured: true,
		status: CategoryStatus.ACTIVE,
		isVisible: true 
	}).sort({ sortOrder: 1 });
};

// Static method สำหรับสร้างโครงสร้างแบบ tree
categorySchema.statics.getTree = async function() {
	const categories = await this.find({ 
		status: CategoryStatus.ACTIVE,
		isVisible: true 
	}).sort({ level: 1, sortOrder: 1 });
	
	const tree = [];
	const map = new Map();
	
	// สร้าง map สำหรับ reference
	categories.forEach(cat => {
		map.set(cat._id.toString(), { ...cat.toObject(), children: [] });
	});
	
	// สร้าง tree structure
	categories.forEach(cat => {
		const catObj = map.get(cat._id.toString());
		if (cat.parentId) {
			const parent = map.get(cat.parentId.toString());
			if (parent) {
				parent.children.push(catObj);
			}
		} else {
			tree.push(catObj);
		}
	});
	
	return tree;
};

// Middleware สำหรับสร้าง slug อัตโนมัติ
categorySchema.pre("save", async function(next) {
	if (this.isModified("name") && !this.seo.slug) {
		this.seo.slug = this.name
			.toLowerCase()
			.replace(/[^a-z0-9\u0E00-\u0E7F]+/g, "-") // รองรับภาษาไทย
			.replace(/^-+|-+$/g, "");
	}
	
	// สร้าง path
	if (this.isModified("name") || this.isModified("parentId")) {
		if (this.parentId) {
			const parent = await model("Category").findById(this.parentId);
			if (parent) {
				this.level = parent.level + 1;
				this.path = `${parent.path}/${this.seo.slug}`;
			}
		} else {
			this.level = 0;
			this.path = `/${this.seo.slug}`;
		}
	}
	
	next();
});

// Middleware สำหรับอัพเดทจำนวนสินค้าเมื่อมีการเปลี่ยนแปลง
categorySchema.post("save", async function() {
	await this.updateProductCount();
});

export const Category = model<ICategory>("Category", categorySchema);