import { Elysia, t } from "elysia";
import { categoryService } from "./category.service";
import { userAuthGuard } from "@/core/plugin/auth";
import {
	createCategorySchema,
	updateCategorySchema,
	searchCategoriesSchema,
	moveCategorySchema,
	reorderCategoriesSchema,
} from "./category.schemas";

export const categoryRoute = new Elysia({ prefix: "/categories" })
	.use(userAuthGuard)
	
	// สร้างหมวดหมู่ใหม่
	.post("/", async ({ body, user }) => {
		const data = createCategorySchema.parse(body);
		const category = await categoryService.createCategory(data, user._id);
		
		return {
			success: true,
			message: "สร้างหมวดหมู่สำเร็จ",
			data: category
		};
	}, {
		body: t.Object({
			name: t.String(),
			description: t.Optional(t.String()),
			parentId: t.Optional(t.String()),
			imageId: t.Optional(t.String()),
			iconClass: t.Optional(t.String()),
			sortOrder: t.Optional(t.Number()),
			isVisible: t.<PERSON>tional(t.<PERSON>()),
			isFeatured: t.<PERSON>(t.<PERSON>()),
			seo: t.Optional(t.Object({
				metaTitle: t.Optional(t.String()),
				metaDescription: t.Optional(t.String()),
				keywords: t.Optional(t.Array(t.String())),
				slug: t.Optional(t.String()),
			})),
		})
	})
	
	// ดึงรายการหมวดหมู่
	.get("/", async ({ query }) => {
		const data = searchCategoriesSchema.parse(query);
		const result = await categoryService.searchCategories(data);
		
		return {
			success: true,
			message: "ดึงรายการหมวดหมู่สำเร็จ",
			data: result.categories,
			pagination: result.pagination
		};
	}, {
		query: t.Object({
			search: t.Optional(t.String()),
			parentId: t.Optional(t.String()),
			level: t.Optional(t.Number()),
			status: t.Optional(t.String()),
			isVisible: t.Optional(t.Boolean()),
			isFeatured: t.Optional(t.Boolean()),
			createdBy: t.Optional(t.String()),
			page: t.Optional(t.Number()),
			limit: t.Optional(t.Number()),
			sortBy: t.Optional(t.String()),
			sortOrder: t.Optional(t.String()),
		})
	})
	
	// ดึงหมวดหมู่หลัก
	.get("/root", async () => {
		const categories = await categoryService.getRootCategories();
		
		return {
			success: true,
			message: "ดึงหมวดหมู่หลักสำเร็จ",
			data: categories
		};
	})
	
	// ดึงหมวดหมู่เด่น
	.get("/featured", async () => {
		const categories = await categoryService.getFeaturedCategories();
		
		return {
			success: true,
			message: "ดึงหมวดหมู่เด่นสำเร็จ",
			data: categories
		};
	})
	
	// ดึงโครงสร้างหมวดหมู่แบบ tree
	.get("/tree", async () => {
		const tree = await categoryService.getCategoryTree();
		
		return {
			success: true,
			message: "ดึงโครงสร้างหมวดหมู่สำเร็จ",
			data: tree
		};
	})
	
	// ดึงหมวดหมู่ตาม ID
	.get("/:id", async ({ params: { id } }) => {
		const category = await categoryService.getCategoryById(id);
		
		if (!category) {
			return {
				success: false,
				message: "ไม่พบหมวดหมู่",
				data: null
			};
		}
		
		return {
			success: true,
			message: "ดึงข้อมูลหมวดหมู่สำเร็จ",
			data: category
		};
	}, {
		params: t.Object({
			id: t.String()
		})
	})
	
	// ดึงหมวดหมู่ตาม slug
	.get("/slug/:slug", async ({ params: { slug } }) => {
		const category = await categoryService.getCategoryBySlug(slug);
		
		if (!category) {
			return {
				success: false,
				message: "ไม่พบหมวดหมู่",
				data: null
			};
		}
		
		return {
			success: true,
			message: "ดึงข้อมูลหมวดหมู่สำเร็จ",
			data: category
		};
	}, {
		params: t.Object({
			slug: t.String()
		})
	})
	
	// ดึงหมวดหมู่ลูก
	.get("/:id/children", async ({ params: { id } }) => {
		const children = await categoryService.getChildCategories(id);
		
		return {
			success: true,
			message: "ดึงหมวดหมู่ลูกสำเร็จ",
			data: children
		};
	}, {
		params: t.Object({
			id: t.String()
		})
	})
	
	// ดึง breadcrumb ของหมวดหมู่
	.get("/:id/breadcrumb", async ({ params: { id } }) => {
		const breadcrumb = await categoryService.getCategoryBreadcrumb(id);
		
		return {
			success: true,
			message: "ดึง breadcrumb สำเร็จ",
			data: breadcrumb
		};
	}, {
		params: t.Object({
			id: t.String()
		})
	})
	
	// อัพเดทหมวดหมู่
	.put("/:id", async ({ params: { id }, body, user }) => {
		const data = updateCategorySchema.parse(body);
		const category = await categoryService.updateCategory(id, data, user._id);
		
		return {
			success: true,
			message: "อัพเดทหมวดหมู่สำเร็จ",
			data: category
		};
	}, {
		params: t.Object({
			id: t.String()
		}),
		body: t.Partial(t.Object({
			name: t.String(),
			description: t.Optional(t.String()),
			parentId: t.Optional(t.String()),
			imageId: t.Optional(t.String()),
			iconClass: t.Optional(t.String()),
			sortOrder: t.Optional(t.Number()),
			status: t.Optional(t.String()),
			isVisible: t.Optional(t.Boolean()),
			isFeatured: t.Optional(t.Boolean()),
			seo: t.Optional(t.Object({
				metaTitle: t.Optional(t.String()),
				metaDescription: t.Optional(t.String()),
				keywords: t.Optional(t.Array(t.String())),
				slug: t.Optional(t.String()),
			})),
		}))
	})
	
	// ย้ายหมวดหมู่
	.put("/:id/move", async ({ params: { id }, body }) => {
		const data = moveCategorySchema.parse(body);
		const category = await categoryService.moveCategory(id, data.newParentId);
		
		return {
			success: true,
			message: "ย้ายหมวดหมู่สำเร็จ",
			data: category
		};
	}, {
		params: t.Object({
			id: t.String()
		}),
		body: t.Object({
			newParentId: t.Union([t.String(), t.Null()]),
		})
	})
	
	// เรียงลำดับหมวดหมู่
	.put("/reorder", async ({ body }) => {
		const data = reorderCategoriesSchema.parse(body);
		await categoryService.reorderCategories(data.categoryOrders);
		
		return {
			success: true,
			message: "เรียงลำดับหมวดหมู่สำเร็จ",
			data: null
		};
	}, {
		body: t.Object({
			categoryOrders: t.Array(t.Object({
				id: t.String(),
				sortOrder: t.Number(),
			})),
		})
	})
	
	// ลบหมวดหมู่
	.delete("/:id", async ({ params: { id } }) => {
		await categoryService.deleteCategory(id);
		
		return {
			success: true,
			message: "ลบหมวดหมู่สำเร็จ",
			data: null
		};
	}, {
		params: t.Object({
			id: t.String()
		})
	})
	
	// ดึงสถิติหมวดหมู่
	.get("/stats/overview", async () => {
		const stats = await categoryService.getCategoryStats();
		
		return {
			success: true,
			message: "ดึงสถิติหมวดหมู่สำเร็จ",
			data: stats
		};
	});