import { Elysia, t } from "elysia";
import { galleryService } from "./gallery.service";
import { userAuthGuard } from "@/core/plugin/auth";
import {
	uploadFileSchema,
	uploadMultipleFilesSchema,
	searchFilesSchema,
	updateFileSchema,
	createTransformationSchema,
	deleteMultipleFilesSchema,
	cleanupFilesSchema,
	getFilesByFolderSchema,
	getFilesByTagsSchema,
} from "./gallery.schemas";

export const galleryRoute = new Elysia({ prefix: "/gallery" })
	.use(userAuthGuard)
	
	// อัพโหลดไฟล์เดี่ยว
	.post("/upload", async ({ body, user }) => {
		const data = uploadFileSchema.parse({
			title: body.title,
			description: body.description,
			altText: body.altText,
			folder: body.folder,
			tags: body.tags,
			generateThumbnails: body.generateThumbnails
		});
		
		const file = await galleryService.uploadFile({
			file: body.file,
			...data
		}, user._id);
		
		return {
			success: true,
			message: "อัพโหลดไฟล์สำเร็จ",
			data: file
		};
	}, {
		body: t.Object({
			file: t.File(),
			title: t.String(),
			description: t.Optional(t.String()),
			altText: t.Optional(t.String()),
			folder: t.Optional(t.String()),
			tags: t.Optional(t.Array(t.String())),
			generateThumbnails: t.Optional(t.Boolean()),
		})
	})

	// อัพโหลดไฟล์พร้อม progress tracking
	.post("/upload-with-progress", async ({ body, user }) => {
		const data = uploadFileSchema.parse({
			title: body.title,
			description: body.description,
			altText: body.altText,
			folder: body.folder,
			tags: body.tags,
			generateThumbnails: body.generateThumbnails
		});
		
		// Note: ใน production ควรใช้ WebSocket หรือ Server-Sent Events สำหรับ real-time progress
		const file = await galleryService.uploadFileWithProgress({
			file: body.file,
			...data
		}, user._id, (progress) => {
			// Progress callback - ใน production ส่งผ่าน WebSocket
			console.log(`Upload progress: ${progress.percentage}% - ${progress.stage}`);
		});
		
		return {
			success: true,
			message: "อัพโหลดไฟล์พร้อม progress tracking สำเร็จ",
			data: file
		};
	}, {
		body: t.Object({
			file: t.File(),
			title: t.String(),
			description: t.Optional(t.String()),
			altText: t.Optional(t.String()),
			folder: t.Optional(t.String()),
			tags: t.Optional(t.Array(t.String())),
			generateThumbnails: t.Optional(t.Boolean()),
		})
	})
	
	// อัพโหลดหลายไฟล์
	.post("/upload-multiple", async ({ body, user }) => {
		const { files, titles, descriptions, altTexts, folder, tags, generateThumbnails } = body;
		
		const data = uploadMultipleFilesSchema.parse({
			titles,
			descriptions,
			altTexts,
			folder,
			tags,
			generateThumbnails
		});
		
		// เตรียมข้อมูลสำหรับแต่ละไฟล์
		const fileDataArray = files.map((file, index) => ({
			file,
			title: data.titles[index],
			description: data.descriptions?.[index],
			altText: data.altTexts?.[index],
			folder: data.folder,
			tags: data.tags,
			generateThumbnails: data.generateThumbnails
		}));
		
		const uploadedFiles = await galleryService.uploadMultipleFiles(fileDataArray, user._id);
		
		return {
			success: true,
			message: `อัพโหลด ${uploadedFiles.length} ไฟล์สำเร็จ`,
			data: uploadedFiles
		};
	}, {
		body: t.Object({
			files: t.Array(t.File()),
			titles: t.Array(t.String()),
			descriptions: t.Optional(t.Array(t.String())),
			altTexts: t.Optional(t.Array(t.String())),
			folder: t.Optional(t.String()),
			tags: t.Optional(t.Array(t.String())),
			generateThumbnails: t.Optional(t.Boolean()),
		})
	})
	
	// ดึงรายการไฟล์
	.get("/", async ({ query }) => {
		const data = searchFilesSchema.parse(query);
		const result = await galleryService.searchFiles(data);
		
		return {
			success: true,
			message: "ดึงรายการไฟล์สำเร็จ",
			data: result.files,
			pagination: result.pagination
		};
	}, {
		query: t.Object({
			search: t.Optional(t.String()),
			fileType: t.Optional(t.String()),
			folder: t.Optional(t.String()),
			tags: t.Optional(t.Array(t.String())),
			uploadedBy: t.Optional(t.String()),
			status: t.Optional(t.String()),
			page: t.Optional(t.Number()),
			limit: t.Optional(t.Number()),
			sortBy: t.Optional(t.String()),
			sortOrder: t.Optional(t.String()),
		})
	})
	
	// ดึงไฟล์ตาม ID
	.get("/:id", async ({ params: { id }, query }) => {
		const incrementView = query.view === "true";
		const file = await galleryService.getFileById(id, incrementView);
		
		if (!file) {
			return {
				success: false,
				message: "ไม่พบไฟล์",
				data: null
			};
		}
		
		return {
			success: true,
			message: "ดึงข้อมูลไฟล์สำเร็จ",
			data: file
		};
	}, {
		params: t.Object({
			id: t.String()
		}),
		query: t.Object({
			view: t.Optional(t.String())
		})
	})
	
	// ดึงไฟล์ตาม folder
	.get("/folder/:folder", async ({ params: { folder }, query }) => {
		const data = getFilesByFolderSchema.parse(query);
		const files = await galleryService.getFilesByFolder(folder, data.limit);
		
		return {
			success: true,
			message: "ดึงไฟล์ตาม folder สำเร็จ",
			data: files
		};
	}, {
		params: t.Object({
			folder: t.String()
		}),
		query: t.Object({
			limit: t.Optional(t.Number()),
		})
	})
	
	// ดึงไฟล์ตาม tags
	.get("/tags/:tags", async ({ params: { tags }, query }) => {
		const data = getFilesByTagsSchema.parse({ tags, ...query });
		const tagArray = data.tags.split(",");
		const files = await galleryService.getFilesByTags(tagArray, data.limit);
		
		return {
			success: true,
			message: "ดึงไฟล์ตาม tags สำเร็จ",
			data: files
		};
	}, {
		params: t.Object({
			tags: t.String()
		}),
		query: t.Object({
			limit: t.Optional(t.Number()),
		})
	})
	
	// อัพเดทข้อมูลไฟล์
	.put("/:id", async ({ params: { id }, body }) => {
		const data = updateFileSchema.parse(body);
		const file = await galleryService.updateFile(id, data);
		
		return {
			success: true,
			message: "อัพเดทไฟล์สำเร็จ",
			data: file
		};
	}, {
		params: t.Object({
			id: t.String()
		}),
		body: t.Object({
			title: t.Optional(t.String()),
			description: t.Optional(t.String()),
			altText: t.Optional(t.String()),
			tags: t.Optional(t.Array(t.String())),
			status: t.Optional(t.String()),
		})
	})
	
	// ลบไฟล์
	.delete("/:id", async ({ params: { id } }) => {
		await galleryService.deleteFile(id);
		
		return {
			success: true,
			message: "ลบไฟล์สำเร็จ",
			data: null
		};
	}, {
		params: t.Object({
			id: t.String()
		})
	})
	
	// ลบหลายไฟล์
	.delete("/", async ({ body }) => {
		const data = deleteMultipleFilesSchema.parse(body);
		await galleryService.deleteMultipleFiles(data.fileIds);
		
		return {
			success: true,
			message: `ลบ ${data.fileIds.length} ไฟล์สำเร็จ`,
			data: null
		};
	}, {
		body: t.Object({
			fileIds: t.Array(t.String()),
		})
	})
	
	// สร้าง transformation ใหม่
	.post("/:id/transformations", async ({ params: { id }, body }) => {
		const data = createTransformationSchema.parse(body);
		const file = await galleryService.createTransformation(id, data.name, {
			width: data.width,
			height: data.height,
			crop: data.crop,
			quality: data.quality,
			format: data.format
		});
		
		return {
			success: true,
			message: "สร้าง transformation สำเร็จ",
			data: file
		};
	}, {
		params: t.Object({
			id: t.String()
		}),
		body: t.Object({
			name: t.String(),
			width: t.Optional(t.Number()),
			height: t.Optional(t.Number()),
			crop: t.Optional(t.String()),
			quality: t.Optional(t.Union([t.String(), t.Number()])),
			format: t.Optional(t.String()),
		})
	})
	
	// ดึงไฟล์ที่ไม่ได้ใช้งาน
	.get("/unused/files", async () => {
		const files = await galleryService.getUnusedFiles();
		
		return {
			success: true,
			message: "ดึงไฟล์ที่ไม่ได้ใช้งานสำเร็จ",
			data: files
		};
	})
	
	// ทำความสะอาดไฟล์ที่ไม่ได้ใช้งาน
	.delete("/unused/cleanup", async ({ query }) => {
		const data = cleanupFilesSchema.parse(query);
		const deletedCount = await galleryService.cleanupUnusedFiles(data.days);
		
		return {
			success: true,
			message: `ทำความสะอาดไฟล์สำเร็จ ลบไป ${deletedCount} ไฟล์`,
			data: { deletedCount }
		};
	}, {
		query: t.Object({
			days: t.Optional(t.Number()),
		})
	})
	
	// อัพโหลดหลายไฟล์พร้อม progress tracking
	.post("/upload-batch-with-progress", async ({ body, user }) => {
		const { files, titles, descriptions, altTexts, folder, tags } = body;
		
		// เตรียมข้อมูลสำหรับแต่ละไฟล์
		const fileDataArray = files.map((file, index) => ({
			file,
			title: titles[index],
			description: descriptions?.[index],
			altText: altTexts?.[index],
			folder: folder || "general",
			tags: tags || []
		}));
		
		// Note: ใน production ควรใช้ WebSocket สำหรับ real-time progress
		const result = await galleryService.uploadBatch(fileDataArray, user._id, (progress) => {
			console.log(`Batch upload progress: ${progress.overallPercentage}% - File ${progress.currentFile}/${progress.totalFiles}`);
		});
		
		return {
			success: true,
			message: `อัพโหลด batch สำเร็จ: ${result.successCount}/${result.total} ไฟล์`,
			data: result
		};
	}, {
		body: t.Object({
			files: t.Array(t.File()),
			titles: t.Array(t.String()),
			descriptions: t.Optional(t.Array(t.String())),
			altTexts: t.Optional(t.Array(t.String())),
			folder: t.Optional(t.String()),
			tags: t.Optional(t.Array(t.String())),
		})
	})

	// สร้าง responsive images
	.post("/:id/responsive", async ({ params: { id }, body }) => {
		const breakpoints = body.breakpoints || [
			{ width: 320, suffix: 'mobile' },
			{ width: 768, suffix: 'tablet' },
			{ width: 1200, suffix: 'desktop' }
		];
		
		const file = await galleryService.createResponsiveImages(id, breakpoints);
		
		return {
			success: true,
			message: "สร้าง responsive images สำเร็จ",
			data: file
		};
	}, {
		params: t.Object({
			id: t.String()
		}),
		body: t.Object({
			breakpoints: t.Optional(t.Array(t.Object({
				width: t.Number(),
				suffix: t.String()
			})))
		})
	})

	// อัพโหลดพร้อมลายน้ำ
	.post("/upload-with-watermark", async ({ body, user }) => {
		const file = await galleryService.uploadWithWatermark({
			file: body.file,
			title: body.title,
			description: body.description,
			altText: body.altText,
			folder: body.folder || "general",
			tags: body.tags || [],
			watermark: {
				text: body.watermarkText,
				image: body.watermarkImage,
				position: body.watermarkPosition || "south_east",
				opacity: body.watermarkOpacity || 70
			}
		}, user._id);
		
		return {
			success: true,
			message: "อัพโหลดไฟล์พร้อมลายน้ำสำเร็จ",
			data: file
		};
	}, {
		body: t.Object({
			file: t.File(),
			title: t.String(),
			description: t.Optional(t.String()),
			altText: t.Optional(t.String()),
			folder: t.Optional(t.String()),
			tags: t.Optional(t.Array(t.String())),
			watermarkText: t.Optional(t.String()),
			watermarkImage: t.Optional(t.String()),
			watermarkPosition: t.Optional(t.String()),
			watermarkOpacity: t.Optional(t.Number()),
		})
	})

	// สร้าง archive จากหลายไฟล์
	.post("/create-archive", async ({ body }) => {
		const archive = await galleryService.createArchive(body.fileIds, {
			type: body.type || 'zip',
			targetFormat: body.targetFormat
		});
		
		return {
			success: true,
			message: "สร้าง archive สำเร็จ",
			data: archive
		};
	}, {
		body: t.Object({
			fileIds: t.Array(t.String()),
			type: t.Optional(t.String()),
			targetFormat: t.Optional(t.String()),
		})
	})

	// ค้นหาไฟล์ใน Cloudinary
	.get("/search-cloudinary", async ({ query }) => {
		const result = await galleryService.searchCloudinaryFiles({
			expression: query.expression,
			sortBy: query.sortBy,
			maxResults: query.maxResults ? parseInt(query.maxResults) : 50,
			nextCursor: query.nextCursor
		});
		
		return {
			success: true,
			message: "ค้นหาไฟล์ใน Cloudinary สำเร็จ",
			data: result
		};
	}, {
		query: t.Object({
			expression: t.Optional(t.String()),
			sortBy: t.Optional(t.String()),
			maxResults: t.Optional(t.String()),
			nextCursor: t.Optional(t.String()),
		})
	})

	// ดึงข้อมูล metadata จาก Cloudinary
	.get("/:id/cloudinary-info", async ({ params: { id } }) => {
		const info = await galleryService.getCloudinaryFileInfo(id);
		
		return {
			success: true,
			message: "ดึงข้อมูล Cloudinary สำเร็จ",
			data: info
		};
	}, {
		params: t.Object({
			id: t.String()
		})
	})

	// สร้าง transformation URL แบบ dynamic
	.post("/:id/transformation-url", async ({ params: { id }, body }) => {
		const url = await galleryService.generateTransformationUrl(id, body.transformations);
		
		return {
			success: true,
			message: "สร้าง transformation URL สำเร็จ",
			data: { url }
		};
	}, {
		params: t.Object({
			id: t.String()
		}),
		body: t.Object({
			transformations: t.Record(t.String(), t.Any())
		})
	})

	// ลบไฟล์แบบ batch พร้อม error handling
	.delete("/batch", async ({ body }) => {
		const result = await galleryService.deleteBatch(body.fileIds);
		
		return {
			success: true,
			message: `ลบ batch สำเร็จ: ${result.successful.length}/${result.total} ไฟล์`,
			data: result
		};
	}, {
		body: t.Object({
			fileIds: t.Array(t.String()),
		})
	})

	// ทำความสะอาดไฟล์ที่เสียหาย (orphaned files)
	.delete("/cleanup-orphaned", async () => {
		const result = await galleryService.cleanupOrphanedFiles();
		
		return {
			success: true,
			message: `ทำความสะอาดไฟล์เสียหายสำเร็จ: ${result.totalCleaned} ไฟล์`,
			data: result
		};
	})
	
	// 🤖 AI Features พร้อม MoneyPoint System

	// วิเคราะห์รูปภาพด้วย AI พร้อมหัก MoneyPoint
	.post("/:id/ai-analyze", async ({ params: { id }, body, user }) => {
		const result = await galleryService.analyzeImageWithAI(id, user._id, {
			autoTag: body.autoTag,
			autoAltText: body.autoAltText,
			objectDetection: body.objectDetection,
			faceDetection: body.faceDetection,
			contentModeration: body.contentModeration,
			backgroundRemoval: body.backgroundRemoval
		});
		
		return {
			success: true,
			message: `วิเคราะห์รูปภาพด้วย AI สำเร็จ (ใช้ ${result.pointsUsed} points)`,
			data: result
		};
	}, {
		params: t.Object({
			id: t.String()
		}),
		body: t.Object({
			autoTag: t.Optional(t.Boolean()),
			autoAltText: t.Optional(t.Boolean()),
			objectDetection: t.Optional(t.Boolean()),
			faceDetection: t.Optional(t.Boolean()),
			contentModeration: t.Optional(t.Boolean()),
			backgroundRemoval: t.Optional(t.Boolean()),
		})
	})

	// ลบพื้นหลังรูปภาพด้วย AI พร้อมหัก MoneyPoint
	.post("/:id/remove-background", async ({ params: { id }, body, user }) => {
		const result = await galleryService.removeImageBackground(id, user._id, {
			outputFormat: body.outputFormat || 'png',
			quality: body.quality || 'auto:good',
			saveAsNew: body.saveAsNew || false
		});
		
		return {
			success: true,
			message: `ลบพื้นหลังสำเร็จ (ใช้ ${result.pointsUsed} points)`,
			data: result
		};
	}, {
		params: t.Object({
			id: t.String()
		}),
		body: t.Object({
			outputFormat: t.Optional(t.String()),
			quality: t.Optional(t.String()),
			saveAsNew: t.Optional(t.Boolean()),
		})
	})

	// ปรับปรุงคุณภาพรูปภาพด้วย AI พร้อมหัก MoneyPoint
	.post("/:id/enhance", async ({ params: { id }, body, user }) => {
		const result = await galleryService.enhanceImage(id, user._id, {
			enhance: body.enhance,
			upscale: body.upscale,
			denoise: body.denoise,
			sharpen: body.sharpen,
			colorize: body.colorize,
			saveAsNew: body.saveAsNew || false
		});
		
		return {
			success: true,
			message: `ปรับปรุงรูปภาพสำเร็จ (ใช้ ${result.pointsUsed} points)`,
			data: result
		};
	}, {
		params: t.Object({
			id: t.String()
		}),
		body: t.Object({
			enhance: t.Optional(t.Boolean()),
			upscale: t.Optional(t.Boolean()),
			denoise: t.Optional(t.Boolean()),
			sharpen: t.Optional(t.Boolean()),
			colorize: t.Optional(t.Boolean()),
			saveAsNew: t.Optional(t.Boolean()),
		})
	})

	// วิเคราะห์หลายรูปภาพพร้อมกันด้วย AI (Batch AI Analysis)
	.post("/batch-ai-analyze", async ({ body, user }) => {
		const result = await galleryService.batchAnalyzeWithAI(body.fileIds, user._id, {
			autoTag: body.autoTag,
			autoAltText: body.autoAltText,
			objectDetection: body.objectDetection,
			faceDetection: body.faceDetection,
			contentModeration: body.contentModeration
		});
		
		return {
			success: true,
			message: `วิเคราะห์ batch สำเร็จ: ${result.successCount}/${result.results.length} รูป (ใช้ ${result.totalPointsUsed} points)`,
			data: result
		};
	}, {
		body: t.Object({
			fileIds: t.Array(t.String()),
			autoTag: t.Optional(t.Boolean()),
			autoAltText: t.Optional(t.Boolean()),
			objectDetection: t.Optional(t.Boolean()),
			faceDetection: t.Optional(t.Boolean()),
			contentModeration: t.Optional(t.Boolean()),
		})
	})
	
	// ดึงสถิติการใช้งาน
	.get("/stats/usage", async () => {
		const stats = await galleryService.getUsageStats();
		
		return {
			success: true,
			message: "ดึงสถิติการใช้งานสำเร็จ",
			data: stats
		};
	});