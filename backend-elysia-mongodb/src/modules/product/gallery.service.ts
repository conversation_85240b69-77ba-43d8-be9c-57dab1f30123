import { Gallery, type IGallery, FileType, FileStatus } from "./gallery.model";
import { cloudinaryUpload, type UploadResult } from "@/core/utils/cloudinary-upload";
import { throwError } from "@/core/utils/error";

// Interface สำหรับการอัพโหลดไฟล์
export interface IUploadFileData {
	file: string | Buffer | File;
	title: string;
	description?: string;
	altText?: string;
	folder?: string;
	tags?: string[];
	generateThumbnails?: boolean;
}

// Interface สำหรับการค้นหาไฟล์
export interface IGalleryFilter {
	search?: string;
	fileType?: FileType;
	folder?: string;
	tags?: string[];
	uploadedBy?: string;
	status?: FileStatus;
	page?: number;
	limit?: number;
	sortBy?: string;
	sortOrder?: "asc" | "desc";
}

// Interface สำหรับการอัพเดทไฟล์
export interface IUpdateGalleryData {
	title?: string;
	description?: string;
	altText?: string;
	tags?: string[];
	status?: FileStatus;
}

// Interface สำหรับ AI Features
export interface IAIFeatureOptions {
	autoTag?: boolean;
	autoAltText?: boolean;
	objectDetection?: boolean;
	contentModeration?: boolean;
	backgroundRemoval?: boolean;
	faceDetection?: boolean;
}

// Interface สำหรับ AI Results
export interface IAIAnalysisResult {
	tags: string[];
	altText: string;
	objects: Array<{
		name: string;
		confidence: number;
		coordinates?: { x: number; y: number; width: number; height: number };
	}>;
	faces: Array<{
		confidence: number;
		coordinates: { x: number; y: number; width: number; height: number };
		emotions?: Record<string, number>;
	}>;
	moderation: {
		safe: boolean;
		categories: Record<string, number>;
	};
	pointsUsed: number;
}

export class GalleryService {
	/**
	 * อัพโหลดไฟล์ใหม่
	 */
	async uploadFile(data: IUploadFileData, uploadedBy: string): Promise<IGallery> {
		try {
			// ตรวจสอบไฟล์แบบละเอียด
			const validation = await cloudinaryUpload.validateFile(data.file, {
				maxFileSize: 10 * 1024 * 1024, // 10MB
				allowedMimeTypes: [
					'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
					'video/mp4', 'video/avi', 'video/mov', 'video/wmv',
					'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
				],
				allowedExtensions: ['jpg', 'jpeg', 'png', 'gif', 'webp', 'mp4', 'avi', 'mov', 'wmv', 'pdf', 'doc', 'docx']
			});

			if (!validation.isValid) {
				throw throwError.badRequest(`ไฟล์ไม่ถูกต้อง: ${validation.errors.join(', ')}`);
			}

			const folder = data.folder || "general";
			
			// อัพโหลดไฟล์หลัก
			const uploadResult = await cloudinaryUpload.uploadPublic(data.file, {
				folder: `gallery/${folder}`,
				resourceType: "auto",
				quality: "auto:good"
			});

			// กำหนดประเภทไฟล์
			let fileType = FileType.OTHER;
			if (uploadResult.resourceType === "image") {
				fileType = FileType.IMAGE;
			} else if (uploadResult.resourceType === "video") {
				fileType = FileType.VIDEO;
			}

			// สร้าง transformations สำหรับรูปภาพ
			const transformations = [];
			if (fileType === FileType.IMAGE && data.generateThumbnails !== false) {
				// Thumbnail (150x150)
				const thumbnailResult = await cloudinaryUpload.uploadPublic(data.file, {
					folder: `gallery/${folder}/thumbnails`,
					publicId: `${uploadResult.publicId}_thumb`,
					resourceType: "image",
					transformation: {
						width: 150,
						height: 150,
						crop: "fill",
						quality: "auto:good",
						format: "webp"
					}
				});

				transformations.push({
					name: "thumbnail",
					width: 150,
					height: 150,
					crop: "fill",
					quality: "auto:good",
					format: "webp",
					url: thumbnailResult.secureUrl,
					publicId: thumbnailResult.publicId
				});

				// Medium (400x400)
				const mediumResult = await cloudinaryUpload.uploadPublic(data.file, {
					folder: `gallery/${folder}/medium`,
					publicId: `${uploadResult.publicId}_medium`,
					resourceType: "image",
					transformation: {
						width: 400,
						height: 400,
						crop: "limit",
						quality: "auto:good",
						format: "webp"
					}
				});

				transformations.push({
					name: "medium",
					width: 400,
					height: 400,
					crop: "limit",
					quality: "auto:good",
					format: "webp",
					url: mediumResult.secureUrl,
					publicId: mediumResult.publicId
				});

				// Large (800x800)
				const largeResult = await cloudinaryUpload.uploadPublic(data.file, {
					folder: `gallery/${folder}/large`,
					publicId: `${uploadResult.publicId}_large`,
					resourceType: "image",
					transformation: {
						width: 800,
						height: 800,
						crop: "limit",
						quality: "auto:good",
						format: "webp"
					}
				});

				transformations.push({
					name: "large",
					width: 800,
					height: 800,
					crop: "limit",
					quality: "auto:good",
					format: "webp",
					url: largeResult.secureUrl,
					publicId: largeResult.publicId
				});
			}

			// สร้างข้อมูลไฟล์ในฐานข้อมูล
			const galleryData = {
				title: data.title,
				description: data.description,
				altText: data.altText || data.title,
				originalName: data.title,
				fileName: uploadResult.publicId,
				fileType,
				mimeType: uploadResult.format,
				metadata: {
					width: uploadResult.width,
					height: uploadResult.height,
					format: uploadResult.format,
					size: uploadResult.bytes
				},
				cloudinaryPublicId: uploadResult.publicId,
				cloudinaryUrl: uploadResult.url,
				cloudinarySecureUrl: uploadResult.secureUrl,
				transformations,
				folder,
				tags: data.tags || [],
				status: FileStatus.ACTIVE,
				uploadedBy,
				usageCount: 0,
				usedBy: [],
				viewCount: 0,
				downloadCount: 0
			};

			const gallery = new Gallery(galleryData);
			await gallery.save();

			return gallery;
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถอัพโหลดไฟล์ได้: ${error.message}`);
		}
	}

	/**
	 * อัพโหลดหลายไฟล์พร้อมกัน
	 */
	async uploadMultipleFiles(
		files: IUploadFileData[], 
		uploadedBy: string
	): Promise<IGallery[]> {
		try {
			const results = await Promise.all(
				files.map(file => this.uploadFile(file, uploadedBy))
			);
			return results;
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถอัพโหลดไฟล์ได้: ${error.message}`);
		}
	}

	/**
	 * ดึงไฟล์ตาม ID
	 */
	async getFileById(fileId: string, incrementView = false): Promise<IGallery | null> {
		try {
			const file = await Gallery.findById(fileId);
			if (!file) {
				return null;
			}

			// เพิ่ม view count
			if (incrementView) {
				await file.incrementView();
			}

			return file;
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถดึงข้อมูลไฟล์ได้: ${error.message}`);
		}
	}

	/**
	 * ค้นหาไฟล์
	 */
	async searchFiles(filter: IGalleryFilter) {
		try {
			const {
				search,
				fileType,
				folder,
				tags,
				uploadedBy,
				status,
				page = 1,
				limit = 20,
				sortBy = "createdAt",
				sortOrder = "desc"
			} = filter;

			// สร้าง query
			const query: any = {};

			// ค้นหาข้อความ
			if (search) {
				query.$or = [
					{ title: { $regex: search, $options: "i" } },
					{ description: { $regex: search, $options: "i" } },
					{ tags: { $in: [new RegExp(search, "i")] } }
				];
			}

			// กรองตามประเภทไฟล์
			if (fileType) query.fileType = fileType;
			if (folder) query.folder = folder;
			if (uploadedBy) query.uploadedBy = uploadedBy;
			if (status) query.status = status;

			// กรองตามแท็ก
			if (tags && tags.length > 0) {
				query.tags = { $in: tags };
			}

			// การเรียงลำดับ
			const sort: any = {};
			sort[sortBy] = sortOrder === "asc" ? 1 : -1;

			// คำนวณ pagination
			const skip = (page - 1) * limit;

			// ดึงข้อมูล
			const [files, total] = await Promise.all([
				Gallery.find(query)
					.sort(sort)
					.skip(skip)
					.limit(limit),
				Gallery.countDocuments(query)
			]);

			return {
				files,
				pagination: {
					page,
					limit,
					total,
					pages: Math.ceil(total / limit)
				}
			};
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถค้นหาไฟล์ได้: ${error.message}`);
		}
	}

	/**
	 * อัพเดทข้อมูลไฟล์
	 */
	async updateFile(fileId: string, data: IUpdateGalleryData): Promise<IGallery> {
		try {
			const file = await Gallery.findById(fileId);
			if (!file) {
				throw throwError.notFound("ไม่พบไฟล์");
			}

			Object.assign(file, data);
			await file.save();

			return file;
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถอัพเดทไฟล์ได้: ${error.message}`);
		}
	}

	/**
	 * ลบไฟล์
	 */
	async deleteFile(fileId: string): Promise<void> {
		try {
			const file = await Gallery.findById(fileId);
			if (!file) {
				throw throwError.notFound("ไม่พบไฟล์");
			}

			// ตรวจสอบว่าไฟล์ยังถูกใช้งานอยู่หรือไม่
			if (file.usageCount > 0) {
				throw throwError.badRequest("ไฟล์นี้ยังถูกใช้งานอยู่ ไม่สามารถลบได้");
			}

			// ลบไฟล์หลักจาก Cloudinary
			try {
				await cloudinaryUpload.deleteFile(
					file.cloudinaryPublicId, 
					file.fileType === FileType.IMAGE ? "image" : "raw"
				);
			} catch (error) {
				console.error("Error deleting main file from Cloudinary:", error);
			}

			// ลบ transformations จาก Cloudinary
			if (file.transformations.length > 0) {
				await Promise.all(
					file.transformations.map(async (transformation) => {
						try {
							await cloudinaryUpload.deleteFile(transformation.publicId, "image");
						} catch (error) {
							console.error("Error deleting transformation from Cloudinary:", error);
						}
					})
				);
			}

			// ลบจากฐานข้อมูล
			await Gallery.findByIdAndDelete(fileId);
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถลบไฟล์ได้: ${error.message}`);
		}
	}

	/**
	 * ลบหลายไฟล์พร้อมกัน
	 */
	async deleteMultipleFiles(fileIds: string[]): Promise<void> {
		try {
			await Promise.all(
				fileIds.map(fileId => this.deleteFile(fileId))
			);
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถลบไฟล์ได้: ${error.message}`);
		}
	}

	/**
	 * ดึงไฟล์ตาม folder
	 */
	async getFilesByFolder(folder: string, limit = 50): Promise<IGallery[]> {
		try {
			return await Gallery.findByFolder(folder).limit(limit);
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถดึงไฟล์ได้: ${error.message}`);
		}
	}

	/**
	 * ดึงไฟล์ตาม tags
	 */
	async getFilesByTags(tags: string[], limit = 50): Promise<IGallery[]> {
		try {
			return await Gallery.findByTags(tags).limit(limit);
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถดึงไฟล์ได้: ${error.message}`);
		}
	}

	/**
	 * ดึงไฟล์ที่ไม่ได้ใช้งาน
	 */
	async getUnusedFiles(): Promise<IGallery[]> {
		try {
			return await Gallery.findUnused();
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถดึงไฟล์ที่ไม่ได้ใช้งานได้: ${error.message}`);
		}
	}

	/**
	 * ทำความสะอาดไฟล์ที่ไม่ได้ใช้งาน
	 */
	async cleanupUnusedFiles(olderThanDays = 30): Promise<number> {
		try {
			const cutoffDate = new Date();
			cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

			const unusedFiles = await Gallery.find({
				usageCount: 0,
				createdAt: { $lt: cutoffDate }
			});

			if (unusedFiles.length === 0) {
				return 0;
			}

			// ลบไฟล์ทีละไฟล์
			let deletedCount = 0;
			for (const file of unusedFiles) {
				try {
					await this.deleteFile(file._id.toString());
					deletedCount++;
				} catch (error) {
					console.error(`Error deleting unused file ${file._id}:`, error);
				}
			}

			return deletedCount;
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถทำความสะอาดไฟล์ได้: ${error.message}`);
		}
	}

	/**
	 * สร้าง transformation ใหม่สำหรับรูปภาพ
	 */
	async createTransformation(
		fileId: string, 
		name: string, 
		options: {
			width?: number;
			height?: number;
			crop?: string;
			quality?: string | number;
			format?: string;
		}
	): Promise<IGallery> {
		try {
			const file = await Gallery.findById(fileId);
			if (!file) {
				throw throwError.notFound("ไม่พบไฟล์");
			}

			if (file.fileType !== FileType.IMAGE) {
				throw throwError.badRequest("สามารถสร้าง transformation ได้เฉพาะรูปภาพเท่านั้น");
			}

			// ตรวจสอบว่า transformation นี้มีอยู่แล้วหรือไม่
			const existingTransformation = file.transformations.find(t => t.name === name);
			if (existingTransformation) {
				throw throwError.badRequest("Transformation นี้มีอยู่แล้ว");
			}

			// สร้าง transformation ใหม่
			const transformationResult = await cloudinaryUpload.uploadPublic(
				file.cloudinarySecureUrl, 
				{
					folder: `gallery/${file.folder}/${name}`,
					publicId: `${file.cloudinaryPublicId}_${name}`,
					resourceType: "image",
					transformation: options
				}
			);

			// เพิ่ม transformation ในไฟล์
			file.transformations.push({
				name,
				width: options.width,
				height: options.height,
				crop: options.crop,
				quality: options.quality,
				format: options.format,
				url: transformationResult.secureUrl,
				publicId: transformationResult.publicId
			});

			await file.save();
			return file;
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถสร้าง transformation ได้: ${error.message}`);
		}
	}

	/**
	 * อัพโหลดหลายไฟล์แบบ batch พร้อม progress tracking
	 */
	async uploadBatch(
		files: Array<{ file: string | Buffer | File; title: string; folder?: string; tags?: string[] }>,
		uploadedBy: string,
		onProgress?: (progress: any) => void
	): Promise<{
		successful: IGallery[];
		failed: Array<{ file: string; error: string }>;
		total: number;
		successCount: number;
		failureCount: number;
	}> {
		try {
			const results = {
				successful: [] as IGallery[],
				failed: [] as Array<{ file: string; error: string }>,
				total: files.length,
				successCount: 0,
				failureCount: 0
			};

			// อัพโหลดทีละไฟล์เพื่อติดตาม progress
			for (let i = 0; i < files.length; i++) {
				const fileData = files[i];
				
				try {
					// Progress callback สำหรับแต่ละไฟล์
					const fileProgress = (progress: any) => {
						onProgress?.({
							...progress,
							fileName: fileData.title,
							currentFile: i + 1,
							totalFiles: files.length,
							overallPercentage: Math.round(((i / files.length) * 100) + (progress.percentage / files.length))
						});
					};

					const gallery = await this.uploadFileWithProgress({
						file: fileData.file,
						title: fileData.title,
						folder: fileData.folder || "general",
						tags: fileData.tags || []
					}, uploadedBy, fileProgress);
					
					results.successful.push(gallery);
					results.successCount++;
				} catch (error: any) {
					results.failed.push({
						file: fileData.title,
						error: error.message
					});
					results.failureCount++;
				}
			}

			// Final progress
			onProgress?.({
				loaded: 100,
				total: 100,
				percentage: 100,
				stage: 'complete',
				currentFile: files.length,
				totalFiles: files.length,
				overallPercentage: 100
			});

			return results;
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถอัพโหลด batch ได้: ${error.message}`);
		}
	}

	/**
	 * อัพโหลดไฟล์พร้อม progress tracking
	 */
	async uploadFileWithProgress(
		data: IUploadFileData, 
		uploadedBy: string,
		onProgress?: (progress: any) => void
	): Promise<IGallery> {
		try {
			// ตรวจสอบไฟล์แบบละเอียด
			const validation = await cloudinaryUpload.validateFile(data.file, {
				maxFileSize: 50 * 1024 * 1024, // 50MB
				allowedMimeTypes: [
					'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
					'video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/webm',
					'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
				],
				allowedExtensions: ['jpg', 'jpeg', 'png', 'gif', 'webp', 'mp4', 'avi', 'mov', 'wmv', 'webm', 'pdf', 'doc', 'docx']
			});

			if (!validation.isValid) {
				throw throwError.badRequest(`ไฟล์ไม่ถูกต้อง: ${validation.errors.join(', ')}`);
			}

			const folder = data.folder || "general";
			let fileType = FileType.OTHER;
			let uploadResult: any;

			// ตรวจสอบประเภทไฟล์และอัพโหลดตามประเภท
			if (validation.fileInfo?.mimeType?.startsWith('video/')) {
				// อัพโหลดวิดีโอพร้อม thumbnails
				const videoResult = await cloudinaryUpload.uploadVideo(data.file, {
					folder: `gallery/${folder}`,
					generateThumbnails: true,
					thumbnailTimes: [0, 5, 10],
					videoFormats: ['mp4', 'webm'],
					quality: 'auto:good'
				}, onProgress);

				uploadResult = videoResult.video;
				fileType = FileType.VIDEO;

				// บันทึก thumbnails เป็น transformations
				const transformations = videoResult.thumbnails.map((thumb, index) => ({
					name: `thumbnail_${index * 5}s`,
					width: 400,
					height: 300,
					crop: 'fill',
					quality: 'auto:good',
					format: 'jpg',
					url: thumb.secureUrl,
					publicId: thumb.publicId
				}));

				// สร้างข้อมูลไฟล์ในฐานข้อมูล
				const galleryData = {
					title: data.title,
					description: data.description,
					altText: data.altText || data.title,
					originalName: data.title,
					fileName: uploadResult.publicId,
					fileType,
					mimeType: uploadResult.format,
					metadata: {
						width: uploadResult.width,
						height: uploadResult.height,
						format: uploadResult.format,
						size: uploadResult.bytes
					},
					cloudinaryPublicId: uploadResult.publicId,
					cloudinaryUrl: uploadResult.url,
					cloudinarySecureUrl: uploadResult.secureUrl,
					transformations,
					folder,
					tags: data.tags || [],
					status: FileStatus.ACTIVE,
					uploadedBy,
					usageCount: 0,
					usedBy: [],
					viewCount: 0,
					downloadCount: 0
				};

				const gallery = new Gallery(galleryData);
				await gallery.save();
				return gallery;

			} else if (validation.fileInfo?.mimeType?.startsWith('image/')) {
				// อัพโหลดรูปภาพพร้อม thumbnails
				uploadResult = await cloudinaryUpload.uploadPublic(data.file, {
					folder: `gallery/${folder}`,
					resourceType: "image",
					quality: "auto:good"
				}, onProgress);

				fileType = FileType.IMAGE;

				// สร้าง transformations สำหรับรูปภาพ
				const transformations = [];
				if (data.generateThumbnails !== false) {
					// Thumbnail (150x150)
					const thumbnailResult = await cloudinaryUpload.uploadPublic(data.file, {
						folder: `gallery/${folder}/thumbnails`,
						publicId: `${uploadResult.publicId}_thumb`,
						resourceType: "image",
						transformation: {
							width: 150,
							height: 150,
							crop: "fill",
							quality: "auto:good",
							format: "webp"
						}
					});

					transformations.push({
						name: "thumbnail",
						width: 150,
						height: 150,
						crop: "fill",
						quality: "auto:good",
						format: "webp",
						url: thumbnailResult.secureUrl,
						publicId: thumbnailResult.publicId
					});

					// Medium (400x400)
					const mediumResult = await cloudinaryUpload.uploadPublic(data.file, {
						folder: `gallery/${folder}/medium`,
						publicId: `${uploadResult.publicId}_medium`,
						resourceType: "image",
						transformation: {
							width: 400,
							height: 400,
							crop: "limit",
							quality: "auto:good",
							format: "webp"
						}
					});

					transformations.push({
						name: "medium",
						width: 400,
						height: 400,
						crop: "limit",
						quality: "auto:good",
						format: "webp",
						url: mediumResult.secureUrl,
						publicId: mediumResult.publicId
					});

					// Large (800x800)
					const largeResult = await cloudinaryUpload.uploadPublic(data.file, {
						folder: `gallery/${folder}/large`,
						publicId: `${uploadResult.publicId}_large`,
						resourceType: "image",
						transformation: {
							width: 800,
							height: 800,
							crop: "limit",
							quality: "auto:good",
							format: "webp"
						}
					});

					transformations.push({
						name: "large",
						width: 800,
						height: 800,
						crop: "limit",
						quality: "auto:good",
						format: "webp",
						url: largeResult.secureUrl,
						publicId: largeResult.publicId
					});
				}

				// สร้างข้อมูลไฟล์ในฐานข้อมูล
				const galleryData = {
					title: data.title,
					description: data.description,
					altText: data.altText || data.title,
					originalName: data.title,
					fileName: uploadResult.publicId,
					fileType,
					mimeType: uploadResult.format,
					metadata: {
						width: uploadResult.width,
						height: uploadResult.height,
						format: uploadResult.format,
						size: uploadResult.bytes
					},
					cloudinaryPublicId: uploadResult.publicId,
					cloudinaryUrl: uploadResult.url,
					cloudinarySecureUrl: uploadResult.secureUrl,
					transformations,
					folder,
					tags: data.tags || [],
					status: FileStatus.ACTIVE,
					uploadedBy,
					usageCount: 0,
					usedBy: [],
					viewCount: 0,
					downloadCount: 0
				};

				const gallery = new Gallery(galleryData);
				await gallery.save();
				return gallery;

			} else {
				// อัพโหลดไฟล์ประเภทอื่นๆ (เอกสาร)
				uploadResult = await cloudinaryUpload.uploadPublic(data.file, {
					folder: `gallery/${folder}`,
					resourceType: "raw"
				}, onProgress);

				fileType = FileType.DOCUMENT;

				// สร้างข้อมูลไฟล์ในฐานข้อมูล
				const galleryData = {
					title: data.title,
					description: data.description,
					altText: data.altText || data.title,
					originalName: data.title,
					fileName: uploadResult.publicId,
					fileType,
					mimeType: uploadResult.format,
					metadata: {
						format: uploadResult.format,
						size: uploadResult.bytes
					},
					cloudinaryPublicId: uploadResult.publicId,
					cloudinaryUrl: uploadResult.url,
					cloudinarySecureUrl: uploadResult.secureUrl,
					transformations: [],
					folder,
					tags: data.tags || [],
					status: FileStatus.ACTIVE,
					uploadedBy,
					usageCount: 0,
					usedBy: [],
					viewCount: 0,
					downloadCount: 0
				};

				const gallery = new Gallery(galleryData);
				await gallery.save();
				return gallery;
			}
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถอัพโหลดไฟล์ได้: ${error.message}`);
		}
	}

	/**
	 * ลบหลายไฟล์แบบ batch พร้อม error handling
	 */
	async deleteBatch(fileIds: string[]): Promise<{
		successful: string[];
		failed: Array<{ fileId: string; error: string }>;
		total: number;
	}> {
		try {
			const results = {
				successful: [] as string[],
				failed: [] as Array<{ fileId: string; error: string }>,
				total: fileIds.length
			};

			// ลบแบบ parallel
			const promises = fileIds.map(async (fileId) => {
				try {
					await this.deleteFile(fileId);
					results.successful.push(fileId);
				} catch (error: any) {
					results.failed.push({
						fileId,
						error: error.message
					});
				}
			});

			await Promise.all(promises);
			return results;
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถลบ batch ได้: ${error.message}`);
		}
	}

	/**
	 * สร้าง responsive images สำหรับรูปภาพ
	 */
	async createResponsiveImages(
		fileId: string,
		breakpoints: Array<{ width: number; suffix: string }> = [
			{ width: 320, suffix: 'mobile' },
			{ width: 768, suffix: 'tablet' },
			{ width: 1200, suffix: 'desktop' }
		]
	): Promise<IGallery> {
		try {
			const file = await Gallery.findById(fileId);
			if (!file) {
				throw throwError.notFound("ไม่พบไฟล์");
			}

			if (file.fileType !== FileType.IMAGE) {
				throw throwError.badRequest("สามารถสร้าง responsive images ได้เฉพาะรูปภาพเท่านั้น");
			}

			// สร้างรูปภาพสำหรับแต่ละ breakpoint
			const responsiveTransformations = await Promise.all(
				breakpoints.map(async ({ width, suffix }) => {
					const transformationResult = await cloudinaryUpload.uploadPublic(
						file.cloudinarySecureUrl,
						{
							folder: `gallery/${file.folder}/responsive`,
							publicId: `${file.cloudinaryPublicId}_${suffix}`,
							resourceType: "image",
							transformation: {
								width,
								crop: 'scale',
								quality: 'auto:good',
								format: 'auto'
							}
						}
					);

					return {
						name: `responsive_${suffix}`,
						width,
						height: undefined,
						crop: 'scale',
						quality: 'auto:good',
						format: 'auto',
						url: transformationResult.secureUrl,
						publicId: transformationResult.publicId
					};
				})
			);

			// เพิ่ม responsive transformations ในไฟล์
			file.transformations.push(...responsiveTransformations);
			await file.save();

			return file;
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถสร้าง responsive images ได้: ${error.message}`);
		}
	}

	/**
	 * อัพโหลดพร้อมใส่ลายน้ำ
	 */
	async uploadWithWatermark(
		data: IUploadFileData & {
			watermark: {
				text?: string;
				image?: string;
				position?: string;
				opacity?: number;
			};
		},
		uploadedBy: string
	): Promise<IGallery> {
		try {
			// ตรวจสอบไฟล์แบบละเอียด
			const validation = await cloudinaryUpload.validateFile(data.file, {
				maxFileSize: 10 * 1024 * 1024, // 10MB
				allowedMimeTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
				allowedExtensions: ['jpg', 'jpeg', 'png', 'gif', 'webp']
			});

			if (!validation.isValid) {
				throw throwError.badRequest(`ไฟล์ไม่ถูกต้อง: ${validation.errors.join(', ')}`);
			}

			const folder = data.folder || "general";

			// อัพโหลดพร้อมลายน้ำ
			const uploadResult = await cloudinaryUpload.uploadWithWatermark(
				data.file,
				data.watermark,
				{
					folder: `gallery/${folder}`,
					resourceType: "image",
					quality: "auto:good"
				}
			);

			// สร้างข้อมูลไฟล์ในฐานข้อมูล
			const galleryData = {
				title: data.title,
				description: data.description,
				altText: data.altText || data.title,
				originalName: data.title,
				fileName: uploadResult.publicId,
				fileType: FileType.IMAGE,
				mimeType: uploadResult.format,
				metadata: {
					width: uploadResult.width,
					height: uploadResult.height,
					format: uploadResult.format,
					size: uploadResult.bytes
				},
				cloudinaryPublicId: uploadResult.publicId,
				cloudinaryUrl: uploadResult.url,
				cloudinarySecureUrl: uploadResult.secureUrl,
				transformations: [],
				folder,
				tags: data.tags || [],
				status: FileStatus.ACTIVE,
				uploadedBy,
				usageCount: 0,
				usedBy: [],
				viewCount: 0,
				downloadCount: 0
			};

			const gallery = new Gallery(galleryData);
			await gallery.save();

			return gallery;
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถอัพโหลดไฟล์พร้อมลายน้ำได้: ${error.message}`);
		}
	}

	/**
	 * สร้าง archive (ZIP) จากไฟล์หลายไฟล์
	 */
	async createArchive(
		fileIds: string[],
		options: {
			type?: 'upload' | 'zip';
			targetFormat?: string;
		} = {}
	): Promise<{ url: string; publicId: string }> {
		try {
			// ดึงข้อมูลไฟล์
			const files = await Gallery.find({ 
				_id: { $in: fileIds },
				status: FileStatus.ACTIVE 
			});

			if (files.length === 0) {
				throw throwError.badRequest("ไม่พบไฟล์ที่ต้องการสร้าง archive");
			}

			// ดึง public IDs
			const publicIds = files.map(file => file.cloudinaryPublicId);

			// สร้าง archive
			const archiveResult = await cloudinaryUpload.createArchive(publicIds, options);

			return archiveResult;
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถสร้าง archive ได้: ${error.message}`);
		}
	}

	/**
	 * ค้นหาไฟล์ใน Cloudinary
	 */
	async searchCloudinaryFiles(query: {
		expression?: string;
		sortBy?: string;
		maxResults?: number;
		nextCursor?: string;
	} = {}): Promise<any> {
		try {
			return await cloudinaryUpload.searchFiles(query);
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถค้นหาไฟล์ใน Cloudinary ได้: ${error.message}`);
		}
	}

	/**
	 * ดึงข้อมูล metadata ของไฟล์จาก Cloudinary
	 */
	async getCloudinaryFileInfo(fileId: string): Promise<any> {
		try {
			const file = await Gallery.findById(fileId);
			if (!file) {
				throw throwError.notFound("ไม่พบไฟล์");
			}

			const resourceType = file.fileType === FileType.IMAGE ? "image" : 
							   file.fileType === FileType.VIDEO ? "video" : "raw";

			return await cloudinaryUpload.getFileInfo(file.cloudinaryPublicId, resourceType);
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถดึงข้อมูลไฟล์จาก Cloudinary ได้: ${error.message}`);
		}
	}

	/**
	 * สร้าง transformation URL แบบ dynamic
	 */
	generateTransformationUrl(
		fileId: string,
		transformations: Record<string, any>
	): Promise<string> {
		return new Promise(async (resolve, reject) => {
			try {
				const file = await Gallery.findById(fileId);
				if (!file) {
					throw throwError.notFound("ไม่พบไฟล์");
				}

				const url = cloudinaryUpload.generateTransformationUrl(
					file.cloudinaryPublicId,
					transformations,
					{
						secure: true,
						resourceType: file.fileType === FileType.IMAGE ? "image" : 
									 file.fileType === FileType.VIDEO ? "video" : "raw"
					}
				);

				resolve(url);
			} catch (error: any) {
				reject(throwError.internal(`ไม่สามารถสร้าง transformation URL ได้: ${error.message}`));
			}
		});
	}

	/**
	 * ดึงสถิติการใช้งาน
	 */
	async getUsageStats() {
		try {
			const [
				totalFiles,
				totalSize,
				filesByType,
				topUsedFiles,
				recentFiles
			] = await Promise.all([
				Gallery.countDocuments({ status: FileStatus.ACTIVE }),
				Gallery.aggregate([
					{ $match: { status: FileStatus.ACTIVE } },
					{ $group: { _id: null, totalSize: { $sum: "$metadata.size" } } }
				]),
				Gallery.aggregate([
					{ $match: { status: FileStatus.ACTIVE } },
					{ $group: { _id: "$fileType", count: { $sum: 1 } } }
				]),
				Gallery.find({ status: FileStatus.ACTIVE })
					.sort({ usageCount: -1 })
					.limit(10)
					.select("title usageCount fileType"),
				Gallery.find({ status: FileStatus.ACTIVE })
					.sort({ createdAt: -1 })
					.limit(10)
					.select("title createdAt fileType")
			]);

			return {
				totalFiles,
				totalSize: totalSize[0]?.totalSize || 0,
				filesByType,
				topUsedFiles,
				recentFiles
			};
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถดึงสถิติได้: ${error.message}`);
		}
	}

	/**
	 * ทำความสะอาดไฟล์ที่เสียหาย (orphaned files)
	 */
	async cleanupOrphanedFiles(): Promise<{
		cloudinaryOrphans: number;
		databaseOrphans: number;
		totalCleaned: number;
	}> {
		try {
			let cloudinaryOrphans = 0;
			let databaseOrphans = 0;

			// ค้นหาไฟล์ใน Cloudinary ที่ไม่มีในฐานข้อมูล
			const cloudinaryFiles = await this.searchCloudinaryFiles({
				expression: 'folder:gallery/*',
				maxResults: 500
			});

			const dbFiles = await Gallery.find({ status: FileStatus.ACTIVE });
			const dbPublicIds = new Set(dbFiles.map(f => f.cloudinaryPublicId));

			// ลบไฟล์ใน Cloudinary ที่ไม่มีในฐานข้อมูล
			const orphanedCloudinaryFiles = cloudinaryFiles.resources?.filter(
				(file: any) => !dbPublicIds.has(file.public_id)
			) || [];

			if (orphanedCloudinaryFiles.length > 0) {
				const publicIds = orphanedCloudinaryFiles.map((file: any) => file.public_id);
				const deleteResult = await cloudinaryUpload.deleteBatch(publicIds);
				cloudinaryOrphans = deleteResult.successful.length;
			}

			// ค้นหาไฟล์ในฐานข้อมูลที่ไม่มีใน Cloudinary
			const orphanedDbFiles = [];
			for (const dbFile of dbFiles) {
				try {
					await cloudinaryUpload.getFileInfo(dbFile.cloudinaryPublicId);
				} catch (error) {
					// ไฟล์ไม่มีใน Cloudinary
					orphanedDbFiles.push(dbFile._id.toString());
				}
			}

			if (orphanedDbFiles.length > 0) {
				await Gallery.deleteMany({ _id: { $in: orphanedDbFiles } });
				databaseOrphans = orphanedDbFiles.length;
			}

			return {
				cloudinaryOrphans,
				databaseOrphans,
				totalCleaned: cloudinaryOrphans + databaseOrphans
			};
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถทำความสะอาดไฟล์ที่เสียหายได้: ${error.message}`);
		}
	}

	/**
	 * วิเคราะห์รูปภาพด้วย AI พร้อมหัก MoneyPoint
	 */
	async analyzeImageWithAI(
		fileId: string,
		userId: string,
		options: IAIFeatureOptions = {}
	): Promise<IAIAnalysisResult> {
		try {
			const file = await Gallery.findById(fileId);
			if (!file) {
				throw throwError.notFound("ไม่พบไฟล์");
			}

			if (file.fileType !== FileType.IMAGE) {
				throw throwError.badRequest("สามารถวิเคราะห์ได้เฉพาะรูปภาพเท่านั้น");
			}

			// คำนวณค่าใช้จ่าย
			const requestedFeatures = [];
			if (options.autoTag) requestedFeatures.push('AUTO_TAG');
			if (options.autoAltText) requestedFeatures.push('AUTO_ALT_TEXT');
			if (options.objectDetection) requestedFeatures.push('OBJECT_DETECTION');
			if (options.faceDetection) requestedFeatures.push('FACE_DETECTION');
			if (options.contentModeration) requestedFeatures.push('CONTENT_MODERATION');
			if (options.backgroundRemoval) requestedFeatures.push('BACKGROUND_REMOVAL');

			if (requestedFeatures.length === 0) {
				throw throwError.badRequest("กรุณาเลือกฟีเจอร์ AI อย่างน้อย 1 ฟีเจอร์");
			}

			// Import MoneyPointService
			const { moneyPointService } = await import('./moneypoint.service');

			// หัก MoneyPoint
			const payment = await moneyPointService.processAIFeaturePayment(
				userId,
				requestedFeatures,
				1,
				{
					fileId,
					fileName: file.title,
					features: options
				}
			);

			if (!payment.success) {
				throw throwError.badRequest("ไม่สามารถหัก MoneyPoint ได้");
			}

			// วิเคราะห์รูปภาพ
			const analysisResult = await cloudinaryUpload.analyzeImageWithAI(
				file.cloudinaryPublicId,
				{
					autoTag: options.autoTag,
					objectDetection: options.objectDetection,
					faceDetection: options.faceDetection,
					contentModeration: options.contentModeration,
					ocrText: true // เพิ่ม OCR เสมอ
				}
			);

			// สร้าง Alt Text อัตโนมัติถ้าต้องการ
			let altText = file.altText || '';
			if (options.autoAltText) {
				altText = await cloudinaryUpload.generateAltText(
					file.cloudinaryPublicId,
					file.title
				);
			}

			// อัพเดทข้อมูลไฟล์
			if (options.autoTag && analysisResult.tags.length > 0) {
				// เพิ่ม AI tags เข้าไปใน tags เดิม
				const existingTags = file.tags || [];
				const newTags = [...new Set([...existingTags, ...analysisResult.tags])];
				file.tags = newTags;
			}

			if (options.autoAltText && altText) {
				file.altText = altText;
			}

			await file.save();

			return {
				tags: analysisResult.tags,
				altText,
				objects: analysisResult.objects,
				faces: analysisResult.faces,
				moderation: analysisResult.moderation,
				pointsUsed: payment.cost
			};
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถวิเคราะห์รูปภาพด้วย AI ได้: ${error.message}`);
		}
	}

	/**
	 * ลบพื้นหลังรูปภาพด้วย AI พร้อมหัก MoneyPoint
	 */
	async removeImageBackground(
		fileId: string,
		userId: string,
		options: {
			outputFormat?: string;
			quality?: string;
			saveAsNew?: boolean;
		} = {}
	): Promise<{
		originalFile: IGallery;
		processedUrl: string;
		newFile?: IGallery;
		pointsUsed: number;
	}> {
		try {
			const file = await Gallery.findById(fileId);
			if (!file) {
				throw throwError.notFound("ไม่พบไฟล์");
			}

			if (file.fileType !== FileType.IMAGE) {
				throw throwError.badRequest("สามารถลบพื้นหลังได้เฉพาะรูปภาพเท่านั้น");
			}

			// Import MoneyPointService
			const { moneyPointService } = await import('./moneypoint.service');

			// หัก MoneyPoint
			const payment = await moneyPointService.processAIFeaturePayment(
				userId,
				['BACKGROUND_REMOVAL'],
				1,
				{
					fileId,
					fileName: file.title,
					options
				}
			);

			if (!payment.success) {
				throw throwError.badRequest("ไม่สามารถหัก MoneyPoint ได้");
			}

			// ลบพื้นหลัง
			const processedUrl = await cloudinaryUpload.removeBackground(
				file.cloudinaryPublicId,
				{
					outputFormat: options.outputFormat || 'png',
					quality: options.quality || 'auto:good'
				}
			);

			let newFile: IGallery | undefined;

			// บันทึกเป็นไฟล์ใหม่ถ้าต้องการ
			if (options.saveAsNew) {
				const newGalleryData = {
					title: `${file.title} (ลบพื้นหลัง)`,
					description: `${file.description || ''} - ลบพื้นหลังด้วย AI`,
					altText: file.altText,
					originalName: `${file.originalName}_no_bg`,
					fileName: `${file.fileName}_no_bg`,
					fileType: FileType.IMAGE,
					mimeType: 'png',
					metadata: {
						...file.metadata,
						format: 'png'
					},
					cloudinaryPublicId: `${file.cloudinaryPublicId}_no_bg`,
					cloudinaryUrl: processedUrl.replace('https://', 'http://'),
					cloudinarySecureUrl: processedUrl,
					transformations: [],
					folder: file.folder,
					tags: [...(file.tags || []), 'ai-processed', 'background-removed'],
					status: FileStatus.ACTIVE,
					uploadedBy: userId,
					usageCount: 0,
					usedBy: [],
					viewCount: 0,
					downloadCount: 0
				};

				newFile = new Gallery(newGalleryData);
				await newFile.save();
			}

			return {
				originalFile: file,
				processedUrl,
				newFile,
				pointsUsed: payment.cost
			};
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถลบพื้นหลังได้: ${error.message}`);
		}
	}

	/**
	 * ปรับปรุงคุณภาพรูปภาพด้วย AI พร้อมหัก MoneyPoint
	 */
	async enhanceImage(
		fileId: string,
		userId: string,
		options: {
			enhance?: boolean;
			upscale?: boolean;
			denoise?: boolean;
			sharpen?: boolean;
			colorize?: boolean;
			saveAsNew?: boolean;
		} = {}
	): Promise<{
		originalFile: IGallery;
		processedUrl: string;
		newFile?: IGallery;
		pointsUsed: number;
	}> {
		try {
			const file = await Gallery.findById(fileId);
			if (!file) {
				throw throwError.notFound("ไม่พบไฟล์");
			}

			if (file.fileType !== FileType.IMAGE) {
				throw throwError.badRequest("สามารถปรับปรุงได้เฉพาะรูปภาพเท่านั้น");
			}

			// คำนวณค่าใช้จ่าย (ใช้ราคาเดียวกับ background removal)
			const enhanceFeatures = [];
			if (options.enhance || options.upscale || options.denoise || options.sharpen || options.colorize) {
				enhanceFeatures.push('BACKGROUND_REMOVAL'); // ใช้ราคาเดียวกัน
			}

			if (enhanceFeatures.length === 0) {
				throw throwError.badRequest("กรุณาเลือกฟีเจอร์ปรับปรุงอย่างน้อย 1 ฟีเจอร์");
			}

			// Import MoneyPointService
			const { moneyPointService } = await import('./moneypoint.service');

			// หัก MoneyPoint
			const payment = await moneyPointService.processAIFeaturePayment(
				userId,
				enhanceFeatures,
				1,
				{
					fileId,
					fileName: file.title,
					options
				}
			);

			if (!payment.success) {
				throw throwError.badRequest("ไม่สามารถหัก MoneyPoint ได้");
			}

			// ปรับปรุงรูปภาพ
			const processedUrl = await cloudinaryUpload.enhanceImage(
				file.cloudinaryPublicId,
				options
			);

			let newFile: IGallery | undefined;

			// บันทึกเป็นไฟล์ใหม่ถ้าต้องการ
			if (options.saveAsNew) {
				const enhanceTypes = [];
				if (options.enhance) enhanceTypes.push('enhanced');
				if (options.upscale) enhanceTypes.push('upscaled');
				if (options.denoise) enhanceTypes.push('denoised');
				if (options.sharpen) enhanceTypes.push('sharpened');
				if (options.colorize) enhanceTypes.push('colorized');

				const newGalleryData = {
					title: `${file.title} (${enhanceTypes.join(', ')})`,
					description: `${file.description || ''} - ปรับปรุงด้วย AI`,
					altText: file.altText,
					originalName: `${file.originalName}_enhanced`,
					fileName: `${file.fileName}_enhanced`,
					fileType: FileType.IMAGE,
					mimeType: file.mimeType,
					metadata: file.metadata,
					cloudinaryPublicId: `${file.cloudinaryPublicId}_enhanced`,
					cloudinaryUrl: processedUrl.replace('https://', 'http://'),
					cloudinarySecureUrl: processedUrl,
					transformations: [],
					folder: file.folder,
					tags: [...(file.tags || []), 'ai-processed', 'enhanced', ...enhanceTypes],
					status: FileStatus.ACTIVE,
					uploadedBy: userId,
					usageCount: 0,
					usedBy: [],
					viewCount: 0,
					downloadCount: 0
				};

				newFile = new Gallery(newGalleryData);
				await newFile.save();
			}

			return {
				originalFile: file,
				processedUrl,
				newFile,
				pointsUsed: payment.cost
			};
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถปรับปรุงรูปภาพได้: ${error.message}`);
		}
	}

	/**
	 * วิเคราะห์หลายรูปภาพพร้อมกันด้วย AI (Batch AI Analysis)
	 */
	async batchAnalyzeWithAI(
		fileIds: string[],
		userId: string,
		options: IAIFeatureOptions = {}
	): Promise<{
		results: Array<{
			fileId: string;
			fileName: string;
			success: boolean;
			analysis?: IAIAnalysisResult;
			error?: string;
		}>;
		totalPointsUsed: number;
		successCount: number;
		failureCount: number;
	}> {
		try {
			// คำนวณค่าใช้จ่ายรวม
			const requestedFeatures = [];
			if (options.autoTag) requestedFeatures.push('AUTO_TAG');
			if (options.autoAltText) requestedFeatures.push('AUTO_ALT_TEXT');
			if (options.objectDetection) requestedFeatures.push('OBJECT_DETECTION');
			if (options.faceDetection) requestedFeatures.push('FACE_DETECTION');
			if (options.contentModeration) requestedFeatures.push('CONTENT_MODERATION');

			if (requestedFeatures.length === 0) {
				throw throwError.badRequest("กรุณาเลือกฟีเจอร์ AI อย่างน้อย 1 ฟีเจอร์");
			}

			// Import MoneyPointService
			const { moneyPointService } = await import('./moneypoint.service');

			// หัก MoneyPoint สำหรับทั้งหมด
			const payment = await moneyPointService.processAIFeaturePayment(
				userId,
				requestedFeatures,
				fileIds.length,
				{
					fileIds,
					batchProcessing: true,
					features: options
				}
			);

			if (!payment.success) {
				throw throwError.badRequest("ไม่สามารถหัก MoneyPoint ได้");
			}

			const results = [];
			let successCount = 0;
			let failureCount = 0;

			// ประมวลผลทีละไฟล์
			for (const fileId of fileIds) {
				try {
					const file = await Gallery.findById(fileId);
					if (!file) {
						results.push({
							fileId,
							fileName: 'Unknown',
							success: false,
							error: 'ไม่พบไฟล์'
						});
						failureCount++;
						continue;
					}

					if (file.fileType !== FileType.IMAGE) {
						results.push({
							fileId,
							fileName: file.title,
							success: false,
							error: 'ไม่ใช่รูปภาพ'
						});
						failureCount++;
						continue;
					}

					// วิเคราะห์รูปภาพ (ไม่หัก points เพิ่ม เพราะหักไปแล้ว)
					const analysisResult = await cloudinaryUpload.analyzeImageWithAI(
						file.cloudinaryPublicId,
						{
							autoTag: options.autoTag,
							objectDetection: options.objectDetection,
							faceDetection: options.faceDetection,
							contentModeration: options.contentModeration,
							ocrText: true
						}
					);

					// สร้าง Alt Text อัตโนมัติถ้าต้องการ
					let altText = file.altText || '';
					if (options.autoAltText) {
						altText = await cloudinaryUpload.generateAltText(
							file.cloudinaryPublicId,
							file.title
						);
					}

					// อัพเดทข้อมูลไฟล์
					if (options.autoTag && analysisResult.tags.length > 0) {
						const existingTags = file.tags || [];
						const newTags = [...new Set([...existingTags, ...analysisResult.tags])];
						file.tags = newTags;
					}

					if (options.autoAltText && altText) {
						file.altText = altText;
					}

					await file.save();

					results.push({
						fileId,
						fileName: file.title,
						success: true,
						analysis: {
							tags: analysisResult.tags,
							altText,
							objects: analysisResult.objects,
							faces: analysisResult.faces,
							moderation: analysisResult.moderation,
							pointsUsed: 0 // หักรวมแล้ว
						}
					});
					successCount++;

				} catch (error: any) {
					results.push({
						fileId,
						fileName: 'Unknown',
						success: false,
						error: error.message
					});
					failureCount++;
				}
			}

			return {
				results,
				totalPointsUsed: payment.cost,
				successCount,
				failureCount
			};
		} catch (error: any) {
			throw throwError.internal(`ไม่สามารถวิเคราะห์ batch ได้: ${error.message}`);
		}
	}
}

export const galleryService = new GalleryService();