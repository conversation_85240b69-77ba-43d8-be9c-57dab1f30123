import { Schema, model, type Document } from "mongoose";

// Enum สำหรับประเภทสินค้า
export enum ProductType {
	PHYSICAL = "physical",        // สินค้าที่จับต้องได้
	DIGITAL = "digital",          // สินค้าดิจิทัล (ดาวน์โหลด)
	SERVICE = "service",          // บริการ
	SUBSCRIPTION = "subscription", // สมาชิก/บอกรับ
}

// Enum สำหรับสถานะสินค้า
export enum ProductStatus {
	DRAFT = "draft",           // ร่าง
	ACTIVE = "active",         // เปิดขาย
	INACTIVE = "inactive",     // ปิดขาย
	OUT_OF_STOCK = "out_of_stock", // หมดสต็อก
	DISCONTINUED = "discontinued", // เลิกผลิต
}

// Enum สำหรับประเภทไฟล์ดิจิทัล
export enum DigitalFileType {
	IMAGE = "image",
	VIDEO = "video", 
	AUDIO = "audio",
	DOCUMENT = "document",
	SOFTWARE = "software",
	EBOOK = "ebook",
	TEMPLATE = "template",
	OTHER = "other",
}

// Interface สำหรับไฟล์ดิจิทัล
export interface IDigitalFile {
	name: string;
	originalName: string;
	fileType: DigitalFileType;
	mimeType: string;
	size: number; // bytes
	downloadUrl: string;
	cloudinaryPublicId?: string;
	isPreviewable: boolean;
	previewUrl?: string;
	downloadCount: number;
	uploadedAt: Date;
}

// Interface สำหรับข้อมูลการจัดส่ง
export interface IShippingInfo {
	weight?: number; // กรัม
	dimensions?: {
		length: number; // ซม.
		width: number;  // ซม.
		height: number; // ซม.
	};
	shippingClass?: string; // ประเภทการจัดส่ง
	requiresShipping: boolean;
}

// Interface สำหรับ SEO
export interface ISEO {
	metaTitle?: string;
	metaDescription?: string;
	keywords?: string[];
	slug: string;
}

// Interface สำหรับตัวเลือกสินค้า (เช่น สี, ขนาด)
export interface IProductVariant {
	name: string;
	value: string;
	priceAdjustment: number; // +/- จากราคาหลัก
	stockQuantity?: number;
	sku?: string;
	isDefault: boolean;
}

// Interface สำหรับราคา
export interface IPricing {
	basePrice: number;
	salePrice?: number;
	costPrice?: number;
	currency: string;
	taxRate?: number;
	isOnSale: boolean;
	saleStartDate?: Date;
	saleEndDate?: Date;
}

// Interface สำหรับสต็อก
export interface IInventory {
	trackQuantity: boolean;
	quantity?: number;
	lowStockThreshold?: number;
	allowBackorder: boolean;
	stockStatus: "in_stock" | "low_stock" | "out_of_stock";
}

// Main Product Interface
export interface IProduct extends Document {
	// ข้อมูลพื้นฐาน
	name: string;
	description: string;
	shortDescription?: string;
	type: ProductType;
	status: ProductStatus;
	
	// รูปภาพ (เชื่อมกับ Gallery)
	coverImageId?: string; // ID ของรูปปก
	galleryImageIds: string[]; // Array ของ Gallery Image IDs
	
	// ไฟล์ดิจิทัล (สำหรับสินค้าดิจิทัล)
	digitalFiles: IDigitalFile[];
	
	// ราคาและสต็อก
	pricing: IPricing;
	inventory: IInventory;
	
	// ตัวเลือกสินค้า
	variants: IProductVariant[];
	
	// หมวดหมู่และแท็ก
	categoryIds: string[]; // เชื่อมกับ Category
	tags: string[];
	
	// ข้อมูลการจัดส่ง
	shipping: IShippingInfo;
	
	// SEO
	seo: ISEO;
	
	// ข้อมูลเพิ่มเติม
	features: string[]; // คุณสมบัติเด่น
	specifications: Record<string, any>; // ข้อมูลจำเพาะ
	
	// การจัดการ
	createdBy: string; // User ID
	updatedBy?: string; // User ID
	
	// สถิติ
	viewCount: number;
	purchaseCount: number;
	rating: {
		average: number;
		count: number;
	};
	
	// วันที่
	createdAt: Date;
	updatedAt: Date;
	publishedAt?: Date;
}

// Schema สำหรับไฟล์ดิจิทัล
const digitalFileSchema = new Schema<IDigitalFile>({
	name: { type: String, required: true },
	originalName: { type: String, required: true },
	fileType: { 
		type: String, 
		enum: Object.values(DigitalFileType),
		required: true 
	},
	mimeType: { type: String, required: true },
	size: { type: Number, required: true },
	downloadUrl: { type: String, required: true },
	cloudinaryPublicId: { type: String },
	isPreviewable: { type: Boolean, default: false },
	previewUrl: { type: String },
	downloadCount: { type: Number, default: 0 },
	uploadedAt: { type: Date, default: Date.now },
});

// Schema สำหรับข้อมูลการจัดส่ง
const shippingInfoSchema = new Schema<IShippingInfo>({
	weight: { type: Number },
	dimensions: {
		length: { type: Number },
		width: { type: Number },
		height: { type: Number },
	},
	shippingClass: { type: String },
	requiresShipping: { type: Boolean, default: true },
});

// Schema สำหรับ SEO
const seoSchema = new Schema<ISEO>({
	metaTitle: { type: String },
	metaDescription: { type: String },
	keywords: [{ type: String }],
	slug: { type: String, required: true, unique: true },
});

// Schema สำหรับตัวเลือกสินค้า
const variantSchema = new Schema<IProductVariant>({
	name: { type: String, required: true },
	value: { type: String, required: true },
	priceAdjustment: { type: Number, default: 0 },
	stockQuantity: { type: Number },
	sku: { type: String },
	isDefault: { type: Boolean, default: false },
});

// Schema สำหรับราคา
const pricingSchema = new Schema<IPricing>({
	basePrice: { type: Number, required: true, min: 0 },
	salePrice: { type: Number, min: 0 },
	costPrice: { type: Number, min: 0 },
	currency: { type: String, default: "THB" },
	taxRate: { type: Number, min: 0, max: 100 },
	isOnSale: { type: Boolean, default: false },
	saleStartDate: { type: Date },
	saleEndDate: { type: Date },
});

// Schema สำหรับสต็อก
const inventorySchema = new Schema<IInventory>({
	trackQuantity: { type: Boolean, default: true },
	quantity: { type: Number, min: 0 },
	lowStockThreshold: { type: Number, min: 0 },
	allowBackorder: { type: Boolean, default: false },
	stockStatus: { 
		type: String, 
		enum: ["in_stock", "low_stock", "out_of_stock"],
		default: "in_stock" 
	},
});

// Main Product Schema
const productSchema = new Schema<IProduct>({
	// ข้อมูลพื้นฐาน
	name: { 
		type: String, 
		required: true,
		trim: true,
		maxlength: 200 
	},
	description: { 
		type: String, 
		required: true,
		maxlength: 5000 
	},
	shortDescription: { 
		type: String,
		maxlength: 500 
	},
	type: { 
		type: String, 
		enum: Object.values(ProductType),
		required: true 
	},
	status: { 
		type: String, 
		enum: Object.values(ProductStatus),
		default: ProductStatus.DRAFT 
	},
	
	// รูปภาพ
	coverImageId: { 
		type: String,
		ref: "Gallery" 
	},
	galleryImageIds: [{ 
		type: String,
		ref: "Gallery" 
	}],
	
	// ไฟล์ดิจิทัล
	digitalFiles: [digitalFileSchema],
	
	// ราคาและสต็อก
	pricing: { 
		type: pricingSchema, 
		required: true 
	},
	inventory: { 
		type: inventorySchema, 
		required: true 
	},
	
	// ตัวเลือกสินค้า
	variants: [variantSchema],
	
	// หมวดหมู่และแท็ก
	categoryIds: [{ 
		type: String,
		ref: "Category" 
	}],
	tags: [{ 
		type: String,
		trim: true 
	}],
	
	// ข้อมูลการจัดส่ง
	shipping: { 
		type: shippingInfoSchema, 
		required: true 
	},
	
	// SEO
	seo: { 
		type: seoSchema, 
		required: true 
	},
	
	// ข้อมูลเพิ่มเติม
	features: [{ 
		type: String,
		trim: true 
	}],
	specifications: { 
		type: Schema.Types.Mixed,
		default: {} 
	},
	
	// การจัดการ
	createdBy: { 
		type: String,
		required: true,
		ref: "User" 
	},
	updatedBy: { 
		type: String,
		ref: "User" 
	},
	
	// สถิติ
	viewCount: { 
		type: Number, 
		default: 0 
	},
	purchaseCount: { 
		type: Number, 
		default: 0 
	},
	rating: {
		average: { 
			type: Number, 
			default: 0,
			min: 0,
			max: 5 
		},
		count: { 
			type: Number, 
			default: 0 
		},
	},
	
	// วันที่
	publishedAt: { type: Date },
}, {
	timestamps: true,
	toJSON: { virtuals: true },
	toObject: { virtuals: true },
});

// Indexes สำหรับประสิทธิภาพ
// productSchema.index({ "seo.slug": 1 });
// productSchema.index({ status: 1, type: 1 });
// productSchema.index({ categoryIds: 1 });
// productSchema.index({ tags: 1 });
// productSchema.index({ "pricing.basePrice": 1 });
// productSchema.index({ createdAt: -1 });
// productSchema.index({ publishedAt: -1 });
// productSchema.index({ viewCount: -1 });
// productSchema.index({ "rating.average": -1 });

// Virtual สำหรับราคาที่แสดง (ราคาลดหรือราคาปกติ)
productSchema.virtual("displayPrice").get(function() {
	if (this.pricing.isOnSale && this.pricing.salePrice) {
		const now = new Date();
		const saleStart = this.pricing.saleStartDate;
		const saleEnd = this.pricing.saleEndDate;
		
		if ((!saleStart || now >= saleStart) && (!saleEnd || now <= saleEnd)) {
			return this.pricing.salePrice;
		}
	}
	return this.pricing.basePrice;
});

// Virtual สำหรับการตรวจสอบว่าสินค้าลดราคาหรือไม่
productSchema.virtual("isOnSale").get(function() {
	if (!this.pricing.isOnSale || !this.pricing.salePrice) return false;
	
	const now = new Date();
	const saleStart = this.pricing.saleStartDate;
	const saleEnd = this.pricing.saleEndDate;
	
	return (!saleStart || now >= saleStart) && (!saleEnd || now <= saleEnd);
});

// Middleware สำหรับอัพเดท slug อัตโนมัติ
productSchema.pre("save", function(next) {
	if (this.isModified("name") && !this.seo.slug) {
		this.seo.slug = this.name
			.toLowerCase()
			.replace(/[^a-z0-9\u0E00-\u0E7F]+/g, "-") // รองรับภาษาไทย
			.replace(/^-+|-+$/g, "");
	}
	next();
});

// Middleware สำหรับอัพเดทสถานะสต็อก
productSchema.pre("save", function(next) {
	if (this.inventory.trackQuantity && typeof this.inventory.quantity === "number") {
		if (this.inventory.quantity <= 0) {
			this.inventory.stockStatus = "out_of_stock";
		} else if (this.inventory.lowStockThreshold && this.inventory.quantity <= this.inventory.lowStockThreshold) {
			this.inventory.stockStatus = "low_stock";
		} else {
			this.inventory.stockStatus = "in_stock";
		}
	}
	next();
});

export const Product = model<IProduct>("Product", productSchema);