// Models
export * from "./product.model";
export * from "./gallery.model";
export * from "./category.model";

// Services
export * from "./product.service";
export * from "./gallery.service";
export * from "./category.service";
export * from "./moneypoint.service";

// Schemas
export * from "./product.schemas";
export * from "./gallery.schemas";
export * from "./category.schemas";

// Routes
export { productRoute } from "./product.route";
export { galleryRoute } from "./gallery.route";
export { categoryRoute } from "./category.route";
export { moneyPointRoute } from "./moneypoint.route";

// Main router
import { Elysia } from "elysia";
import { productRoute } from "./product.route";
import { galleryRoute } from "./gallery.route";
import { categoryRoute } from "./category.route";
import { moneyPointRoute } from "./moneypoint.route";

export const productModule = new Elysia({ prefix: "/api" })
    .use(productRoute)
    .use(galleryRoute)
    .use(categoryRoute)
    .use(moneyPointRoute);