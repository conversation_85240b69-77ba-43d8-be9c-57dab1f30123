import { Elysia, t } from "elysia";
import { moneyPointService, AI_FEATURE_COSTS } from "./moneypoint.service";
import { userAuthGuard } from "@/core/plugin/auth";

export const moneyPointRoute = new Elysia({ prefix: "/moneypoint" })
    .use(userAuthGuard)

    // ดึงยอด MoneyPoint ปัจจุบัน
    .get("/balance", async ({ user }) => {
        const balance = await moneyPointService.getUserBalance(user._id);

        return {
            success: true,
            message: "ดึงยอด MoneyPoint สำเร็จ",
            data: { balance }
        };
    })

    // ดึงราคา AI Features
    .get("/ai-pricing", async () => {
        return {
            success: true,
            message: "ดึงราคา AI Features สำเร็จ",
            data: {
                features: AI_FEATURE_COSTS,
                description: {
                    AUTO_TAG: "สร้าง tags อัตโนมัติจากเนื้อหารูปภาพ",
                    AUTO_ALT_TEXT: "สร้าง alt text อัตโนมัติสำหรับ SEO",
                    OBJECT_DETECTION: "ตรวจจับวัตถุในรูปภาพ",
                    FACE_DETECTION: "ตรวจจับใบหน้าและอารมณ์",
                    CONTENT_MODERATION: "ตรวจสอบเนื้อหาไม่เหมาะสม",
                    BACKGROUND_REMOVAL: "ลบพื้นหลังรูปภาพ",
                    BATCH_PROCESSING: "ค่าใช้จ่ายเพิ่มเติมสำหรับการประมวลผลหลายรูป"
                }
            }
        };
    })

    // คำนวณค่าใช้จ่าย AI Features
    .post("/calculate-cost", async ({ body }) => {
        const cost = moneyPointService.calculateAICost(body.features, body.imageCount || 1);

        return {
            success: true,
            message: "คำนวณค่าใช้จ่ายสำเร็จ",
            data: {
                features: body.features,
                imageCount: body.imageCount || 1,
                totalCost: cost,
                breakdown: body.features.map((feature: string) => ({
                    feature,
                    costPerImage: AI_FEATURE_COSTS[feature as keyof typeof AI_FEATURE_COSTS] || 0,
                    totalCost: (AI_FEATURE_COSTS[feature as keyof typeof AI_FEATURE_COSTS] || 0) * (body.imageCount || 1)
                }))
            }
        };
    }, {
        body: t.Object({
            features: t.Array(t.String()),
            imageCount: t.Optional(t.Number({ minimum: 1 }))
        })
    })

    // เพิ่ม MoneyPoint (สำหรับ admin หรือระบบชำระเงิน)
    .post("/add", async ({ body, user }) => {
        const result = await moneyPointService.addPoints({
            userId: user._id,
            amount: body.amount,
            type: 'add',
            reason: body.reason || 'เติม MoneyPoint',
            feature: 'TOP_UP',
            metadata: body.metadata
        });

        return {
            success: true,
            message: `เพิ่ม ${body.amount} points สำเร็จ`,
            data: result
        };
    }, {
        body: t.Object({
            amount: t.Number({ minimum: 1 }),
            reason: t.Optional(t.String()),
            metadata: t.Optional(t.Record(t.String(), t.Any()))
        })
    })

    // ดึงประวัติการใช้งาน MoneyPoint
    .get("/history", async ({ user, query }) => {
        const result = await moneyPointService.getTransactionHistory(user._id, {
            page: query.page || 1,
            limit: query.limit || 20,
            feature: query.feature,
            type: query.type as 'deduct' | 'add' | undefined
        });

        return {
            success: true,
            message: "ดึงประวัติการใช้งานสำเร็จ",
            data: result.transactions,
            pagination: result.pagination
        };
    }, {
        query: t.Object({
            page: t.Optional(t.Number({ minimum: 1 })),
            limit: t.Optional(t.Number({ minimum: 1, maximum: 100 })),
            feature: t.Optional(t.String()),
            type: t.Optional(t.String())
        })
    })

    // ดึงสถิติการใช้งาน MoneyPoint
    .get("/stats", async ({ user }) => {
        const stats = await moneyPointService.getUserStats(user._id);

        return {
            success: true,
            message: "ดึงสถิติการใช้งานสำเร็จ",
            data: stats
        };
    })

    // ตรวจสอบว่า MoneyPoint เพียงพอสำหรับ AI Features หรือไม่
    .post("/check-sufficient", async ({ body, user }) => {
        try {
            const balance = await moneyPointService.getUserBalance(user._id);
            const cost = moneyPointService.calculateAICost(body.features, body.imageCount || 1);

            const sufficient = balance >= cost;

            return {
                success: true,
                message: sufficient ? "MoneyPoint เพียงพอ" : "MoneyPoint ไม่เพียงพอ",
                data: {
                    currentBalance: balance,
                    requiredPoints: cost,
                    sufficient,
                    shortfall: sufficient ? 0 : cost - balance
                }
            };
        } catch (error: any) {
            return {
                success: false,
                message: error.message,
                data: null
            };
        }
    }, {
        body: t.Object({
            features: t.Array(t.String()),
            imageCount: t.Optional(t.Number({ minimum: 1 }))
        })
    });