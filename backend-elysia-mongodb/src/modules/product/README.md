# Product Module

โมดูลสินค้าที่ครอบคลุมและยืดหยุ่น รองรับสินค้าทุกประเภท ทั้งออนไลน์ ออฟไลน์ สินค้าดิจิทัล และไฟล์ต่างๆ

## ✨ คุณสมบัติหลัก

### 🛍️ ประเภทสินค้า
- **Physical** - สินค้าที่จับต้องได้ (ต้องจัดส่ง)
- **Digital** - สินค้าดิจิทัล (ดาวน์โหลดได้)
- **Service** - บริการ
- **Subscription** - สมาชิก/บอกรับ

### 🖼️ ระบบ Gallery แยกต่างหาก (ปรับปรุงใหม่)
- **Smart File Upload** - อัพโหลดพร้อม validation และ progress tracking
- **Batch Operations** - อัพโหลด/ลบหลายไฟล์พร้อมกัน
- **Video Processing** - สร้าง thumbnails และ multiple formats อัตโนมัติ
- **Responsive Images** - สร้างรูปภาพหลายขนาดสำหรับอุปกรณ์ต่างๆ
- **Watermark Support** - ใส่ลายน้ำ (ข้อความ/รูปภาพ)
- **Archive Creation** - สร้าง ZIP จากหลายไฟล์
- **Advanced Search** - ค้นหาทั้งในฐานข้อมูลและ Cloudinary
- **Orphaned File Cleanup** - ทำความสะอาดไฟล์ที่เสียหาย
- **Progress Tracking** - ติดตามความคืบหน้าการอัพโหลด real-time

### 📁 ระบบหมวดหมู่แบบ Hierarchical
- หมวดหมู่หลัก และหมวดหมู่ย่อยไม่จำกัดระดับ
- SEO-friendly URLs
- Breadcrumb navigation
- การเรียงลำดับแบบ drag & drop

### 💰 ระบบราคาและสต็อก
- ราคาปกติ, ราคาลด, ราคาต้นทุน
- การจัดการสต็อกอัตโนมัติ
- ตัวเลือกสินค้า (สี, ขนาด) พร้อมราคาแยก
- รองรับหลายสกุลเงิน

### 📦 ไฟล์ดิจิทัล
- อัพโหลดไฟล์สำหรับสินค้าดิจิทัล
- ระบบดาวน์โหลดที่ปลอดภัย
- นับจำนวนการดาวน์โหลด
- รองรับไฟล์หลายประเภท

### 🎥 Video Processing (ใหม่)
- **Video Thumbnails** - สร้าง thumbnails อัตโนมัติ
- **Multiple Formats** - แปลงเป็น MP4, WebM
- **Adaptive Streaming** - URLs สำหรับคุณภาพต่างๆ
- **Video Compression** - ลดขนาดไฟล์อัตโนมัติ

### 🤖 Image AI Features พร้อม MoneyPoint System (ใหม่)
- **Auto-Tagging** - สร้าง tags อัตโนมัติจากเนื้อหารูปภาพ (2 points)
- **Auto Alt Text** - สร้าง alt text อัตโนมัติสำหรับ SEO (1 point)
- **Object Detection** - ตรวจจับวัตถุในรูปภาพ (3 points)
- **Face Detection** - ตรวจจับใบหน้าและอารมณ์ (2 points)
- **Content Moderation** - ตรวจสอบเนื้อหาไม่เหมาะสม (1 point)
- **Background Removal** - ลบพื้นหลังรูปภาพ (5 points)
- **Image Enhancement** - ปรับปรุงคุณภาพรูปภาพ (5 points)
- **Batch Processing** - ประมวลผลหลายรูปพร้อมกัน (+1 point ต่อรูปเพิ่มเติม)
- **MoneyPoint System** - ระบบชำระเงินด้วย points ของ user

## 🏗️ โครงสร้างโมดูล

```
src/modules/product/
├── product.model.ts      # โมเดลสินค้า
├── gallery.model.ts      # โมเดล Gallery
├── category.model.ts     # โมเดลหมวดหมู่
├── product.service.ts    # บริการจัดการสินค้า
├── gallery.service.ts    # บริการจัดการ Gallery
├── category.service.ts   # บริการจัดการหมวดหมู่
├── product.schemas.ts    # Zod schemas สำหรับ validation
├── gallery.schemas.ts    # Zod schemas สำหรับ Gallery
├── category.schemas.ts   # Zod schemas สำหรับหมวดหมู่
├── product.route.ts      # API Routes สินค้า
├── gallery.route.ts      # API Routes Gallery
├── category.route.ts     # API Routes หมวดหมู่
├── index.ts             # Export หลัก
└── README.md            # เอกสารนี้
```

## 🚀 การใช้งาน

### 1. เพิ่มโมดูลในแอป

```typescript
import { productModule } from "@/modules/product";

const app = new Elysia()
  .use(productModule)
  // ... other modules
```

### 2. สร้างสินค้าใหม่

```typescript
POST /api/products
{
  "name": "iPhone 15 Pro",
  "description": "สมาร์ทโฟนรุ่นล่าสุด",
  "type": "physical",
  "pricing": {
    "basePrice": 39900,
    "currency": "THB"
  },
  "inventory": {
    "trackQuantity": true,
    "quantity": 100
  },
  "shipping": {
    "weight": 200,
    "requiresShipping": true
  },
  "categoryIds": ["category_id_here"]
}
```

### 3. อัพโหลดรูปภาพ

```typescript
// อัพโหลดไปยัง Gallery
POST /api/gallery/upload
FormData: {
  file: [File],
  title: "รูปสินค้า iPhone",
  folder: "products"
}

// เพิ่มรูปให้สินค้า
POST /api/products/{id}/images
{
  "imageIds": ["gallery_id_here"],
  "setCover": true
}
```

### 4. สร้างหมวดหมู่

```typescript
POST /api/categories
{
  "name": "สมาร์ทโฟน",
  "description": "โทรศัพท์มือถือทุกยี่ห้อ",
  "parentId": "electronics_category_id", // optional
  "seo": {
    "slug": "smartphones"
  }
}
```

## 📋 API Endpoints

### Products
- `GET /api/products` - ดึงรายการสินค้า (รองรับการค้นหาและกรอง)
- `POST /api/products` - สร้างสินค้าใหม่
- `GET /api/products/{id}` - ดึงสินค้าตาม ID
- `GET /api/products/slug/{slug}` - ดึงสินค้าตาม SEO slug
- `PUT /api/products/{id}` - อัพเดทสินค้า
- `DELETE /api/products/{id}` - ลบสินค้า
- `POST /api/products/{id}/images` - เพิ่มรูปภาพ
- `DELETE /api/products/{id}/images` - ลบรูปภาพ
- `POST /api/products/{id}/digital-files` - อัพโหลดไฟล์ดิจิทัล

### Gallery
- `GET /api/gallery` - ดึงรายการไฟล์
- `POST /api/gallery/upload` - อัพโหลดไฟล์เดี่ยว
- `POST /api/gallery/upload-multiple` - อัพโหลดหลายไฟล์
- `GET /api/gallery/{id}` - ดึงไฟล์ตาม ID
- `PUT /api/gallery/{id}` - อัพเดทข้อมูลไฟล์
- `DELETE /api/gallery/{id}` - ลบไฟล์
- `GET /api/gallery/folder/{folder}` - ดึงไฟล์ตาม folder
- `GET /api/gallery/unused/files` - ดึงไฟล์ที่ไม่ได้ใช้งาน

### Categories
- `GET /api/categories` - ดึงรายการหมวดหมู่
- `POST /api/categories` - สร้างหมวดหมู่ใหม่
- `GET /api/categories/root` - ดึงหมวดหมู่หลัก
- `GET /api/categories/featured` - ดึงหมวดหมู่เด่น
- `GET /api/categories/tree` - ดึงโครงสร้างแบบ tree
- `GET /api/categories/{id}` - ดึงหมวดหมู่ตาม ID
- `GET /api/categories/slug/{slug}` - ดึงหมวดหมู่ตาม slug
- `PUT /api/categories/{id}` - อัพเดทหมวดหมู่
- `DELETE /api/categories/{id}` - ลบหมวดหมู่

## 🔧 การตั้งค่า

### Environment Variables
```env
# Cloudinary (สำหรับจัดเก็บไฟล์)
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret
```

### Dependencies
```json
{
  "cloudinary": "^1.41.0",
  "mongoose": "^8.0.0"
}
```

## 💡 ตัวอย่างการใช้งาน

### สร้างสินค้าดิจิทัล
```typescript
// 1. สร้างสินค้า
const product = await productService.createProduct({
  name: "Template Website",
  description: "เทมเพลตเว็บไซต์สำเร็จรูป",
  type: "digital",
  pricing: { basePrice: 1500 },
  inventory: { trackQuantity: false },
  shipping: { requiresShipping: false }
}, userId);

// 2. อัพโหลดไฟล์ดิจิทัล
await productService.uploadDigitalFile(product._id, {
  file: zipFile,
  name: "website-template.zip",
  fileType: "template"
});
```

### สร้างสินค้าที่มีตัวเลือก
```typescript
const product = await productService.createProduct({
  name: "เสื้อยืด",
  type: "physical",
  variants: [
    { name: "สี", value: "แดง", isDefault: true },
    { name: "สี", value: "น้ำเงิน", priceAdjustment: 50 },
    { name: "ขนาด", value: "M", isDefault: true },
    { name: "ขนาด", value: "L", priceAdjustment: 100 }
  ]
}, userId);
```

### 🆕 ฟีเจอร์ใหม่ - อัพโหลดพร้อม Progress Tracking
```typescript
// อัพโหลดไฟล์เดี่ยวพร้อม progress
POST /api/gallery/upload-with-progress
FormData: {
  file: [File],
  title: "รูปสินค้า",
  folder: "products"
}

// อัพโหลดหลายไฟล์พร้อม progress
POST /api/gallery/upload-batch-with-progress
FormData: {
  files: [File, File, File],
  titles: ["รูป 1", "รูป 2", "รูป 3"],
  folder: "products"
}
```

### 🆕 ฟีเจอร์ใหม่ - Video Processing
```typescript
// อัพโหลดวิดีโอพร้อมสร้าง thumbnails
const videoResult = await cloudinaryUpload.uploadVideo(videoFile, {
  folder: "products/videos",
  generateThumbnails: true,
  thumbnailTimes: [0, 10, 30], // สร้าง thumbnail ที่ 0, 10, 30 วินาที
  videoFormats: ['mp4', 'webm'],
  quality: 'auto:good'
});

// สร้าง adaptive streaming URLs
const streamingUrls = cloudinaryUpload.generateAdaptiveStreamingUrls(videoPublicId, [
  { quality: 'auto:low', width: 480, height: 360 },
  { quality: 'auto:good', width: 720, height: 480 },
  { quality: 'auto:best', width: 1080, height: 720 }
]);
```

### 🆕 ฟีเจอร์ใหม่ - Responsive Images
```typescript
// สร้าง responsive images สำหรับรูปภาพ
POST /api/gallery/{id}/responsive
{
  "breakpoints": [
    { "width": 320, "suffix": "mobile" },
    { "width": 768, "suffix": "tablet" },
    { "width": 1200, "suffix": "desktop" }
  ]
}
```

### 🆕 ฟีเจอร์ใหม่ - Watermark
```typescript
// อัพโหลดพร้อมลายน้ำ
POST /api/gallery/upload-with-watermark
FormData: {
  file: [File],
  title: "รูปสินค้า",
  watermarkText: "© My Store",
  watermarkPosition: "south_east",
  watermarkOpacity: 70
}
```

### 🆕 ฟีเจอร์ใหม่ - Archive Creation
```typescript
// สร้าง ZIP จากหลายไฟล์
POST /api/gallery/create-archive
{
  "fileIds": ["file1_id", "file2_id", "file3_id"],
  "type": "zip"
}
```

### 🆕 ฟีเจอร์ใหม่ - Advanced File Management
```typescript
// ค้นหาไฟล์ใน Cloudinary
GET /api/gallery/search-cloudinary?expression=folder:products/*&sortBy=created_at

// ทำความสะอาดไฟล์ที่เสียหาย
DELETE /api/gallery/cleanup-orphaned

// ลบหลายไฟล์พร้อมกัน
DELETE /api/gallery/batch
{
  "fileIds": ["file1_id", "file2_id", "file3_id"]
}

// สร้าง transformation URL แบบ dynamic
POST /api/gallery/{id}/transformation-url
{
  "transformations": {
    "width": 300,
    "height": 300,
    "crop": "fill",
    "quality": "auto:good",
    "format": "webp"
  }
}
```

### 🤖 ฟีเจอร์ใหม่ - Image AI Features พร้อม MoneyPoint System
```typescript
// 1. ตรวจสอบยอด MoneyPoint
GET /api/moneypoint/balance
// Response: { "data": { "balance": 100 } }

// 2. ดูราคา AI Features
GET /api/moneypoint/ai-pricing
// Response: {
//   "data": {
//     "features": {
//       "AUTO_TAG": 2,
//       "AUTO_ALT_TEXT": 1,
//       "OBJECT_DETECTION": 3,
//       "FACE_DETECTION": 2,
//       "CONTENT_MODERATION": 1,
//       "BACKGROUND_REMOVAL": 5
//     }
//   }
// }

// 3. คำนวณค่าใช้จ่าย
POST /api/moneypoint/calculate-cost
{
  "features": ["AUTO_TAG", "OBJECT_DETECTION"],
  "imageCount": 3
}
// Response: { "data": { "totalCost": 15 } } // (2+3) * 3 = 15 points

// 4. วิเคราะห์รูปภาพด้วย AI (หัก points อัตโนมัติ)
POST /api/gallery/{fileId}/ai-analyze
{
  "autoTag": true,
  "autoAltText": true,
  "objectDetection": true,
  "contentModeration": true
}
// Response: {
//   "message": "วิเคราะห์รูปภาพด้วย AI สำเร็จ (ใช้ 7 points)",
//   "data": {
//     "tags": ["smartphone", "technology", "mobile"],
//     "altText": "รูปภาพที่มี smartphone, technology",
//     "objects": [
//       { "name": "phone", "confidence": 0.95 },
//       { "name": "screen", "confidence": 0.87 }
//     ],
//     "moderation": { "safe": true },
//     "pointsUsed": 7
//   }
// }

// 5. ลบพื้นหลังรูปภาพ (หัก 5 points)
POST /api/gallery/{fileId}/remove-background
{
  "outputFormat": "png",
  "saveAsNew": true
}
// Response: {
//   "message": "ลบพื้นหลังสำเร็จ (ใช้ 5 points)",
//   "data": {
//     "processedUrl": "https://res.cloudinary.com/...",
//     "newFile": { /* ไฟล์ใหม่ที่บันทึก */ },
//     "pointsUsed": 5
//   }
// }

// 6. ปรับปรุงคุณภาพรูปภาพ (หัก 5 points)
POST /api/gallery/{fileId}/enhance
{
  "enhance": true,
  "upscale": true,
  "denoise": true,
  "saveAsNew": true
}

// 7. วิเคราะห์หลายรูปพร้อมกัน (Batch AI)
POST /api/gallery/batch-ai-analyze
{
  "fileIds": ["file1_id", "file2_id", "file3_id"],
  "autoTag": true,
  "objectDetection": true
}
// Response: {
//   "message": "วิเคราะห์ batch สำเร็จ: 3/3 รูป (ใช้ 17 points)",
//   "data": {
//     "results": [
//       { "fileId": "file1_id", "success": true, "analysis": {...} },
//       { "fileId": "file2_id", "success": true, "analysis": {...} },
//       { "fileId": "file3_id", "success": true, "analysis": {...} }
//     ],
//     "totalPointsUsed": 17, // (2+3)*3 + 2 batch = 17
//     "successCount": 3
//   }
// }

// 8. ดูประวัติการใช้งาน MoneyPoint
GET /api/moneypoint/history?page=1&limit=10&feature=AI_PROCESSING

// 9. ดูสถิติการใช้งาน MoneyPoint
GET /api/moneypoint/stats
// Response: {
//   "data": {
//     "currentBalance": 83,
//     "totalSpent": 17,
//     "totalEarned": 100,
//     "mostUsedFeature": "AI_PROCESSING"
//   }
// }

// 10. เติม MoneyPoint (สำหรับ admin หรือระบบชำระเงิน)
POST /api/moneypoint/add
{
  "amount": 50,
  "reason": "เติมเงินผ่าน Credit Card"
}
```

## 🎯 ข้อดีของการออกแบบนี้

### 1. ความยืดหยุ่น
- รองรับสินค้าทุกประเภท
- ขยายได้ง่าย
- แยกส่วนการทำงานชัดเจน

### 2. ประสิทธิภาพ
- Gallery แยกต่างหาก ลดการซ้ำซ้อน
- Indexes ที่เหมาะสม
- Lazy loading รูปภาพ

### 3. SEO-Friendly
- Slug อัตโนมัติ
- Meta tags ครบถ้วน
- Breadcrumb navigation

### 4. ความปลอดภัย
- Authentication ทุก endpoint
- File validation
- Secure file upload

## 🔄 การขยายระบบ

### เพิ่มประเภทสินค้าใหม่
```typescript
// เพิ่มใน product.model.ts
export enum ProductType {
  // ... existing types
  RENTAL = "rental",      // สินค้าเช่า
  BOOKING = "booking",    // การจอง
}
```

### เพิ่ม Field ใหม่
```typescript
// เพิ่มใน IProduct interface
export interface IProduct extends Document {
  // ... existing fields
  customFields: Record<string, any>; // สำหรับข้อมูลเพิ่มเติม
}
```

## 📊 การติดตาม

ระบบมีการติดตามสถิติต่างๆ:
- จำนวนการดู (viewCount)
- จำนวนการซื้อ (purchaseCount)
- คะแนนรีวิว (rating)
- การใช้งานไฟล์ (usageCount)

## 🛠️ การบำรุงรักษา

### ทำความสะอาดไฟล์ที่ไม่ได้ใช้
```typescript
// ลบไฟล์ที่ไม่ได้ใช้งานเกิน 30 วัน
DELETE /api/gallery/unused/cleanup?days=30
```

### อัพเดทจำนวนสินค้าในหมวดหมู่
```typescript
// ระบบจะอัพเดทอัตโนมัติเมื่อมีการเปลี่ยนแปลง
await category.updateProductCount();
```

---

**หมายเหตุ:** โมดูลนี้ออกแบบมาให้ใช้งานง่าย ขยายได้ และรองรับธุรกิจทุกประเภท ทั้ง E-commerce แบบดั้งเดิม และ Digital marketplace สมัยใหม่