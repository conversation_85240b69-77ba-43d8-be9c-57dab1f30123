import { Elysia, t } from "elysia";
import { productService } from "./product.service";
import { userAuthGuard } from "@/core/plugin/auth";
import {
	createProductSchema,
	updateProductSchema,
	searchProductsSchema,
	addProductImagesSchema,
	removeProductImagesSchema,
	uploadDigitalFileSchema,
	updateRatingSchema,
} from "./product.schemas";

export const productRoute = new Elysia({ prefix: "/products" })
	.use(userAuthGuard)
	
	// สร้างสินค้าใหม่
	.post("/", async ({ body, user }) => {
		const data = createProductSchema.parse(body);
		const product = await productService.createProduct(data, user._id);
		
		return {
			success: true,
			message: "สร้างสินค้าสำเร็จ",
			data: product
		};
	}, {
		body: t.Object({
			name: t.String(),
			description: t.String(),
			shortDescription: t.Optional(t.String()),
			type: t.String(),
			pricing: t.Object({
				basePrice: t.Number(),
				salePrice: t.Optional(t.Number()),
				costPrice: t.Optional(t.Number()),
				currency: t.Optional(t.String()),
				taxRate: t.Optional(t.Number()),
				isOnSale: t.Optional(t.Boolean()),
				saleStartDate: t.Optional(t.String()),
				saleEndDate: t.Optional(t.String()),
			}),
			inventory: t.Object({
				trackQuantity: t.Optional(t.Boolean()),
				quantity: t.Optional(t.Number()),
				lowStockThreshold: t.Optional(t.Number()),
				allowBackorder: t.Optional(t.Boolean()),
			}),
			shipping: t.Object({
				weight: t.Optional(t.Number()),
				dimensions: t.Optional(t.Object({
					length: t.Number(),
					width: t.Number(),
					height: t.Number(),
				})),
				shippingClass: t.Optional(t.String()),
				requiresShipping: t.Optional(t.Boolean()),
			}),
			categoryIds: t.Optional(t.Array(t.String())),
			tags: t.Optional(t.Array(t.String())),
			features: t.Optional(t.Array(t.String())),
			specifications: t.Optional(t.Record(t.String(), t.Any())),
			variants: t.Optional(t.Array(t.Object({
				name: t.String(),
				value: t.String(),
				priceAdjustment: t.Optional(t.Number()),
				stockQuantity: t.Optional(t.Number()),
				sku: t.Optional(t.String()),
				isDefault: t.Optional(t.Boolean()),
			}))),
			seo: t.Optional(t.Object({
				metaTitle: t.Optional(t.String()),
				metaDescription: t.Optional(t.String()),
				keywords: t.Optional(t.Array(t.String())),
				slug: t.Optional(t.String()),
			})),
		})
	})
	
	// ดึงรายการสินค้า
	.get("/", async ({ query }) => {
		const data = searchProductsSchema.parse(query);
		const result = await productService.searchProducts(data);
		
		return {
			success: true,
			message: "ดึงรายการสินค้าสำเร็จ",
			data: result.products,
			pagination: result.pagination
		};
	}, {
		query: t.Object({
			search: t.Optional(t.String()),
			type: t.Optional(t.String()),
			status: t.Optional(t.String()),
			categoryIds: t.Optional(t.Array(t.String())),
			tags: t.Optional(t.Array(t.String())),
			priceMin: t.Optional(t.Number()),
			priceMax: t.Optional(t.Number()),
			inStock: t.Optional(t.Boolean()),
			isOnSale: t.Optional(t.Boolean()),
			createdBy: t.Optional(t.String()),
			page: t.Optional(t.Number()),
			limit: t.Optional(t.Number()),
			sortBy: t.Optional(t.String()),
			sortOrder: t.Optional(t.String()),
		})
	})
	
	// ดึงสินค้าตาม ID
	.get("/:id", async ({ params: { id }, query }) => {
		const incrementView = query.view === "true";
		const product = await productService.getProductById(id, incrementView);
		
		if (!product) {
			return {
				success: false,
				message: "ไม่พบสินค้า",
				data: null
			};
		}
		
		return {
			success: true,
			message: "ดึงข้อมูลสินค้าสำเร็จ",
			data: product
		};
	}, {
		params: t.Object({
			id: t.String()
		}),
		query: t.Object({
			view: t.Optional(t.String())
		})
	})
	
	// ดึงสินค้าตาม slug
	.get("/slug/:slug", async ({ params: { slug }, query }) => {
		const incrementView = query.view === "true";
		const product = await productService.getProductBySlug(slug, incrementView);
		
		if (!product) {
			return {
				success: false,
				message: "ไม่พบสินค้า",
				data: null
			};
		}
		
		return {
			success: true,
			message: "ดึงข้อมูลสินค้าสำเร็จ",
			data: product
		};
	}, {
		params: t.Object({
			slug: t.String()
		}),
		query: t.Object({
			view: t.Optional(t.String())
		})
	})
	
	// อัพเดทสินค้า
	.put("/:id", async ({ params: { id }, body, user }) => {
		const data = updateProductSchema.parse(body);
		const product = await productService.updateProduct(id, data, user._id);
		
		return {
			success: true,
			message: "อัพเดทสินค้าสำเร็จ",
			data: product
		};
	}, {
		params: t.Object({
			id: t.String()
		}),
		body: t.Partial(t.Object({
			name: t.String(),
			description: t.String(),
			shortDescription: t.Optional(t.String()),
			type: t.String(),
			status: t.Optional(t.String()),
			pricing: t.Object({
				basePrice: t.Number(),
				salePrice: t.Optional(t.Number()),
				costPrice: t.Optional(t.Number()),
				currency: t.Optional(t.String()),
				taxRate: t.Optional(t.Number()),
				isOnSale: t.Optional(t.Boolean()),
				saleStartDate: t.Optional(t.String()),
				saleEndDate: t.Optional(t.String()),
			}),
			inventory: t.Object({
				trackQuantity: t.Optional(t.Boolean()),
				quantity: t.Optional(t.Number()),
				lowStockThreshold: t.Optional(t.Number()),
				allowBackorder: t.Optional(t.Boolean()),
			}),
			shipping: t.Object({
				weight: t.Optional(t.Number()),
				dimensions: t.Optional(t.Object({
					length: t.Number(),
					width: t.Number(),
					height: t.Number(),
				})),
				shippingClass: t.Optional(t.String()),
				requiresShipping: t.Optional(t.Boolean()),
			}),
			categoryIds: t.Optional(t.Array(t.String())),
			tags: t.Optional(t.Array(t.String())),
			features: t.Optional(t.Array(t.String())),
			specifications: t.Optional(t.Record(t.String(), t.Any())),
			variants: t.Optional(t.Array(t.Object({
				name: t.String(),
				value: t.String(),
				priceAdjustment: t.Optional(t.Number()),
				stockQuantity: t.Optional(t.Number()),
				sku: t.Optional(t.String()),
				isDefault: t.Optional(t.Boolean()),
			}))),
			seo: t.Optional(t.Object({
				metaTitle: t.Optional(t.String()),
				metaDescription: t.Optional(t.String()),
				keywords: t.Optional(t.Array(t.String())),
				slug: t.Optional(t.String()),
			})),
		}))
	})
	
	// ลบสินค้า
	.delete("/:id", async ({ params: { id } }) => {
		await productService.deleteProduct(id);
		
		return {
			success: true,
			message: "ลบสินค้าสำเร็จ",
			data: null
		};
	}, {
		params: t.Object({
			id: t.String()
		})
	})
	
	// เพิ่มรูปภาพให้สินค้า
	.post("/:id/images", async ({ params: { id }, body }) => {
		const data = addProductImagesSchema.parse(body);
		const product = await productService.addProductImages(
			id, 
			data.imageIds, 
			data.setCover
		);
		
		return {
			success: true,
			message: "เพิ่มรูปภาพสำเร็จ",
			data: product
		};
	}, {
		params: t.Object({
			id: t.String()
		}),
		body: t.Object({
			imageIds: t.Array(t.String()),
			setCover: t.Optional(t.Boolean()),
		})
	})
	
	// ลบรูปภาพจากสินค้า
	.delete("/:id/images", async ({ params: { id }, body }) => {
		const data = removeProductImagesSchema.parse(body);
		const product = await productService.removeProductImages(id, data.imageIds);
		
		return {
			success: true,
			message: "ลบรูปภาพสำเร็จ",
			data: product
		};
	}, {
		params: t.Object({
			id: t.String()
		}),
		body: t.Object({
			imageIds: t.Array(t.String()),
		})
	})
	
	// อัพโหลดไฟล์ดิจิทัล
	.post("/:id/digital-files", async ({ params: { id }, body }) => {
		const data = uploadDigitalFileSchema.parse({
			name: body.name,
			fileType: body.fileType,
			isPreviewable: body.isPreviewable
		});
		
		const product = await productService.uploadDigitalFile(id, {
			file: body.file,
			name: data.name,
			fileType: data.fileType,
			isPreviewable: data.isPreviewable
		});
		
		return {
			success: true,
			message: "อัพโหลดไฟล์ดิจิทัลสำเร็จ",
			data: product
		};
	}, {
		params: t.Object({
			id: t.String()
		}),
		body: t.Object({
			file: t.File(),
			name: t.String(),
			fileType: t.String(),
			isPreviewable: t.Optional(t.Boolean()),
		})
	})
	
	// ลบไฟล์ดิจิทัล
	.delete("/:id/digital-files/:fileIndex", async ({ params: { id, fileIndex } }) => {
		const product = await productService.removeDigitalFile(id, parseInt(fileIndex));
		
		return {
			success: true,
			message: "ลบไฟล์ดิจิทัลสำเร็จ",
			data: product
		};
	}, {
		params: t.Object({
			id: t.String(),
			fileIndex: t.String()
		})
	})
	
	// อัพเดทสถิติการซื้อ (สำหรับระบบภายใน)
	.post("/:id/purchase", async ({ params: { id } }) => {
		await productService.updatePurchaseStats(id);
		
		return {
			success: true,
			message: "อัพเดทสถิติการซื้อสำเร็จ",
			data: null
		};
	}, {
		params: t.Object({
			id: t.String()
		})
	})
	
	// อัพเดทคะแนนรีวิว (สำหรับระบบภายใน)
	.post("/:id/rating", async ({ params: { id }, body }) => {
		const data = updateRatingSchema.parse(body);
		await productService.updateRating(id, data.rating);
		
		return {
			success: true,
			message: "อัพเดทคะแนนรีวิวสำเร็จ",
			data: null
		};
	}, {
		params: t.Object({
			id: t.String()
		}),
		body: t.Object({
			rating: t.Number()
		})
	});