import { z } from "zod";
import { ProductType, ProductStatus, DigitalFileType } from "./product.model";

// Schema สำหรับการสร้างสินค้า
export const createProductSchema = z.object({
	name: z.string().min(1, "กรุณากรอกชื่อสินค้า").max(200, "ชื่อสินค้าต้องไม่เกิน 200 ตัวอักษร"),
	description: z.string().min(1, "กรุณากรอกรายละเอียดสินค้า").max(5000, "รายละเอียดต้องไม่เกิน 5000 ตัวอักษร"),
	shortDescription: z.string().max(500, "รายละเอียดย่อต้องไม่เกิน 500 ตัวอักษร").optional(),
	type: z.nativeEnum(ProductType, { message: "ประเภทสินค้าไม่ถูกต้อง" }),
	
	pricing: z.object({
		basePrice: z.number().min(0, "ราคาต้องไม่ติดลบ"),
		salePrice: z.number().min(0, "ราคาลดต้องไม่ติดลบ").optional(),
		costPrice: z.number().min(0, "ราคาต้นทุนต้องไม่ติดลบ").optional(),
		currency: z.string().default("THB"),
		taxRate: z.number().min(0).max(100, "อัตราภาษีต้องอยู่ระหว่าง 0-100").optional(),
		isOnSale: z.boolean().default(false),
		saleStartDate: z.string().datetime().optional(),
		saleEndDate: z.string().datetime().optional(),
	}),
	
	inventory: z.object({
		trackQuantity: z.boolean().default(true),
		quantity: z.number().min(0, "จำนวนสต็อกต้องไม่ติดลบ").optional(),
		lowStockThreshold: z.number().min(0, "จำนวนสต็อกต่ำต้องไม่ติดลบ").optional(),
		allowBackorder: z.boolean().default(false),
	}),
	
	shipping: z.object({
		weight: z.number().min(0, "น้ำหนักต้องไม่ติดลบ").optional(),
		dimensions: z.object({
			length: z.number().min(0, "ความยาวต้องไม่ติดลบ"),
			width: z.number().min(0, "ความกว้างต้องไม่ติดลบ"),
			height: z.number().min(0, "ความสูงต้องไม่ติดลบ"),
		}).optional(),
		shippingClass: z.string().optional(),
		requiresShipping: z.boolean().default(true),
	}),
	
	categoryIds: z.array(z.string()).optional(),
	tags: z.array(z.string()).optional(),
	features: z.array(z.string()).optional(),
	specifications: z.record(z.any()).optional(),
	
	variants: z.array(z.object({
		name: z.string().min(1, "กรุณากรอกชื่อตัวเลือก"),
		value: z.string().min(1, "กรุณากรอกค่าตัวเลือก"),
		priceAdjustment: z.number().default(0),
		stockQuantity: z.number().min(0, "จำนวนสต็อกต้องไม่ติดลบ").optional(),
		sku: z.string().optional(),
		isDefault: z.boolean().default(false),
	})).optional(),
	
	seo: z.object({
		metaTitle: z.string().optional(),
		metaDescription: z.string().optional(),
		keywords: z.array(z.string()).optional(),
		slug: z.string().optional(),
	}).optional(),
});

// Schema สำหรับการอัพเดทสินค้า
export const updateProductSchema = createProductSchema.partial().extend({
	status: z.nativeEnum(ProductStatus).optional(),
});

// Schema สำหรับการค้นหาสินค้า
export const searchProductsSchema = z.object({
	search: z.string().optional(),
	type: z.nativeEnum(ProductType).optional(),
	status: z.nativeEnum(ProductStatus).optional(),
	categoryIds: z.array(z.string()).optional(),
	tags: z.array(z.string()).optional(),
	priceMin: z.number().min(0).optional(),
	priceMax: z.number().min(0).optional(),
	inStock: z.boolean().optional(),
	isOnSale: z.boolean().optional(),
	createdBy: z.string().optional(),
	page: z.number().min(1).default(1),
	limit: z.number().min(1).max(100).default(20),
	sortBy: z.string().default("createdAt"),
	sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

// Schema สำหรับการเพิ่มรูปภาพ
export const addProductImagesSchema = z.object({
	imageIds: z.array(z.string()).min(1, "กรุณาเลือกรูปภาพอย่างน้อย 1 รูป"),
	setCover: z.boolean().default(false),
});

// Schema สำหรับการลบรูปภาพ
export const removeProductImagesSchema = z.object({
	imageIds: z.array(z.string()).min(1, "กรุณาเลือกรูปภาพที่ต้องการลบ"),
});

// Schema สำหรับการอัพโหลดไฟล์ดิจิทัล
export const uploadDigitalFileSchema = z.object({
	name: z.string().min(1, "กรุณากรอกชื่อไฟล์"),
	fileType: z.nativeEnum(DigitalFileType),
	isPreviewable: z.boolean().default(false),
});

// Schema สำหรับการอัพเดทคะแนนรีวิว
export const updateRatingSchema = z.object({
	rating: z.number().min(1).max(5, "คะแนนต้องอยู่ระหว่าง 1-5"),
});

// Type exports
export type CreateProductInput = z.infer<typeof createProductSchema>;
export type UpdateProductInput = z.infer<typeof updateProductSchema>;
export type SearchProductsInput = z.infer<typeof searchProductsSchema>;
export type AddProductImagesInput = z.infer<typeof addProductImagesSchema>;
export type RemoveProductImagesInput = z.infer<typeof removeProductImagesSchema>;
export type UploadDigitalFileInput = z.infer<typeof uploadDigitalFileSchema>;
export type UpdateRatingInput = z.infer<typeof updateRatingSchema>;