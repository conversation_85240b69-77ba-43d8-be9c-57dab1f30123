import { throwError } from "@/core/utils/error";
import { IRole, Role, SiteRole } from "./role.model";

// ✅ เพิ่ม function ชื่อใหม่ที่ชัดเจนขึ้น
export async function addSiteRole(siteId: string, userId: string, role: SiteRole): Promise<IRole> {
    const exist = await Role.findOne({ siteId, userId });
    if (exist) throwError.conflict('ผู้ใช้นี้มีบทบาทในเว็บไซต์นี้แล้ว');
    const newRole = new Role({ siteId, userId, role });
    await newRole.save();
    return newRole;
  }