import { Elysia, error, t } from "elysia";
import { jwt } from "@elysiajs/jwt";
import {
	changePasswordSchema,
	emailVerificationSchema,
	forgotPasswordSchema,
	signinSchema,
	refreshTokenSchema,
	signupSchema,
	resetPasswordSchema,
	updateProfileSchema,
	UpdateProfileInput,
	ChangePasswordInput,
} from "./user.schemas";
import { UserService } from "./user.service";
import { userJwtPlugin, userAuthGuard } from "@/core/plugin";
import { type IUser } from "./user.model";
import { rateLimitMiddleware, tokenBlacklistMiddleware, getClientIP } from "../auth/middleware";

// JWT configuration for signup/signin
const jwtConfig = {
	name: "jwt",
	secret: process.env.JWT_SECRET || "your-secret-key",
	exp: "7d",
	alg: "HS256",
};

const refreshJwtConfig = {
	name: "refreshJwt",
	secret: process.env.JWT_REFRESH_SECRET || "your-refresh-secret-key",
	exp: "30d",
	alg: "HS256",
};

export const userRoute = new Elysia({ prefix: "/users" })
	.use(jwt(jwtConfig))
	.use(jwt(refreshJwtConfig))

	// Signup with rate limiting
	.use(rateLimitMiddleware('/users/signup'))
	.post(
		"/signup",
		async ({ body, jwt, refreshJwt, headers }) => {
			const data = signupSchema.parse(body);
			const result = await UserService.signup(data);

			// Generate tokens
			const accessToken = await jwt.sign({ userId: result.user._id });
			const refreshToken = await refreshJwt.sign({ userId: result.user._id });

			// Save refresh token to user
			await UserService.updateRefreshToken(result.user._id, refreshToken);

			return {
				success: true,
				message: "ลงทะเบียนสำเร็จ กรุณายืนยันอีเมล",
				data: {
					user: result.user,
					accessToken,
					refreshToken,
				},
			};
		},
		{
			body: t.Object({
				email: t.String(),
				password: t.String(),
				confirmPassword: t.String(),
				agreeToTerms: t.Boolean(),
			}),
		},
	)

	// Signin with rate limiting
	.use(rateLimitMiddleware('/users/signin'))
	.post(
		"/signin",
		async ({ body, jwt, refreshJwt, headers }) => {
			const data = signinSchema.parse(body);
			const result = await UserService.signin(data);

			// กำหนดอายุของ tokens ตาม rememberMe
			const tokenExpiry = data.rememberMe ? '30d' : '7d'; // 30 วันหรือ 7 วัน
			const refreshTokenExpiry = '30d'; // refresh token อายุ 30 วันเสมอ

			// Generate tokens
			const accessToken = await jwt.sign({ userId: result.user._id });
			const refreshToken = await refreshJwt.sign({ userId: result.user._id });

			// Save refresh token to user
			await UserService.updateRefreshToken(result.user._id, refreshToken);

			return {
				success: true,
				message: "เข้าสู่ระบบสำเร็จ",
				data: {
					user: result.user,
					accessToken,
					refreshToken,
					rememberMe: data.rememberMe, // ส่งกลับเพื่อให้ frontend ใช้กำหนด cookie expiry
				},
			};
		},
		{
			body: t.Object({
				email: t.String(),
				password: t.String(),
				rememberMe: t.Optional(t.Boolean()),
			}),
			response: t.Object({
				success: t.Boolean(),
				message: t.String(),
				data: t.Object({
					user: t.Object({
						_id: t.String(),
						email: t.String(),
						firstName: t.String(),
						lastName: t.String(),
						phone: t.Optional(t.String()),
						avatar: t.Optional(t.String()),
						cover: t.Optional(t.String()),
						moneyPoint: t.Number(),
						goldPoint: t.Number(),
						isEmailVerified: t.Boolean(),
						isActive: t.Boolean(),
						createdAt: t.String(),
						updatedAt: t.String(),
					}),
					accessToken: t.String(),
					refreshToken: t.String(),
					rememberMe: t.Optional(t.Boolean()),
				}),
			}),
		},
	)

	// Verify email
	.post(
		"/verify-email",
		async ({ body }) => {
			const data = emailVerificationSchema.parse(body);
			const result = await UserService.verifyEmail(data);

			return {
				success: true,
				message: result.message,
			};
		},
		{
			body: t.Object({
				token: t.String(),
			}),
		},
	)

	// Forgot password with rate limiting
	.use(rateLimitMiddleware('/users/forgot-password'))
	.post(
		"/forgot-password",
		async ({ body, headers }) => {
			const data = forgotPasswordSchema.parse(body);
			const result = await UserService.forgotPassword(data);

			return {
				success: true,
				message: result.message,
			};
		},
		{
			body: t.Object({
				email: t.String(),
			}),
		},
	)

	// Reset password
	.post(
		"/reset-password",
		async ({ body }) => {
			const data = resetPasswordSchema.parse(body);
			const result = await UserService.resetPassword(data);

			return {
				success: true,
				message: result.message,
			};
		},
		{
			body: t.Object({
				token: t.String(),
				password: t.String(),
			}),
		},
	)

	// Refresh token with rate limiting and blacklist check
	.use(rateLimitMiddleware('/users/refresh-token'))
	.use(tokenBlacklistMiddleware)
	.post(
		"/refresh-token",
		async ({ body, jwt, refreshJwt, headers }) => {
			const data = refreshTokenSchema.parse(body);

			// Verify refresh token
			const payload = await refreshJwt.verify(data.refreshToken);
			if (!payload || !payload.userId) {
				throw error(401, { message: "Refresh token ไม่ถูกต้อง" });
			}

			// Generate new tokens
			const accessToken = await jwt.sign({ userId: payload.userId });
			const newRefreshToken = await refreshJwt.sign({ userId: payload.userId });

			// Update refresh token in database
			await UserService.updateRefreshToken(payload.userId as string, newRefreshToken);

			return {
				success: true,
				message: "Token ถูกอัปเดตแล้ว",
				data: {
					accessToken,
					refreshToken: newRefreshToken,
				},
			};
		},
		{
			body: t.Object({
				refreshToken: t.String(),
			}),
		},
	)

	// Protected routes with token blacklist check
	.group("/me", (app) =>
		app
			.use(tokenBlacklistMiddleware)
			.use(userAuthGuard)
			.get("/", async ({ user }: { user: IUser }) => {
				// console.log("user", user); // ⚠️ ไม่ควร log user object ที่อาจมี sensitive data
				return {
					success: true,
					data: user as unknown as IUser,
				};
			})

			// Update profile
			.put(
				"/",
				async ({ user, body }: { user: IUser; body: UpdateProfileInput }) => {
					const data = updateProfileSchema.parse(body);
					const updatedUser = await UserService.updateProfile(user._id, data);

					return {
						success: true,
						message: "อัปเดตโปรไฟล์สำเร็จ",
						data: updatedUser,
					};
				},
				{
					body: t.Object({
						firstName: t.Optional(t.String()),
						lastName: t.Optional(t.String()),
						phone: t.Optional(t.String()),
						avatar: t.Optional(t.String()),
					}),
				},
			)

			// Change password
			.put(
				"/change-password",
				async ({ user, body }: { user: IUser; body: ChangePasswordInput }) => {
					const data = changePasswordSchema.parse(body);
					const result = await UserService.changePassword(user._id as string, data);

					return {
						success: true,
						message: result.message,
					};
				},
				{
					body: t.Object({
						currentPassword: t.String(),
						newPassword: t.String(),
					}),
				},
			)

			// Signout
			.post(
				"/signout",
				async ({ user, headers }: { user: IUser; headers: any }) => {
					// ดึง access token จาก header เพื่อเพิ่มเข้า blacklist
					const authHeader = headers.authorization;
					let accessToken: string | undefined;

					if (authHeader && authHeader.startsWith("Bearer ")) {
						accessToken = authHeader.substring(7);
					}

					const result = await UserService.signout(user._id as string, accessToken);

					return {
						success: true,
						message: result.message,
					};
				},
				{
					body: t.Optional(t.Any()),
				},
			),
	)

	// Resend verification email
	.post(
		"/resend-verification",
		async ({ body }) => {
			const { email } = body;
			const result = await UserService.resendVerificationEmail(email);

			return {
				success: true,
				message: result.message,
			};
		},
		{
			body: t.Object({
				email: t.String(),
			}),
		},
	);
