import mongoose from "mongoose";
import { generateMedelId } from "@/core/utils/genId";

export interface IUser {
	_id: string;
	email: string;
	password: string;
	firstName: string;
	lastName: string;
	phone?: string;
	avatar?: string;
	cover?: string;
	moneyPoint: number;
	goldPoint: number;
	moneyPointTransactions?: Array<{
		id: string;
		amount: number;
		type: 'deduct' | 'add';
		reason: string;
		feature: string;
		metadata?: Record<string, any>;
		timestamp: Date;
		balanceAfter: number;
	}>;
	isEmailVerified: boolean;
	emailVerificationToken?: string;
	emailVerificationExpires?: Date;
	passwordResetToken?: string;
	passwordResetExpires?: Date;
	refreshToken?: string;
	isActive: boolean;
	lastSignin?: Date;
	createdAt: Date;
	updatedAt: Date;
}

const userSchema = new mongoose.Schema<IUser>(
	{
		_id: { type: String, default: () => generateMedelId(5) },
		email: {
			type: String,
			required: true,
			unique: true,
			lowercase: true,
			trim: true,
		},
		password: {
			type: String,
			required: true,
		},
		firstName: {
			type: String,
			required: false,
			trim: true,
		},
		lastName: {
			type: String,
			required: false,
			trim: true,
		},
		avatar: {
			type: String,
			required: false,
			trim: true,
			default: "avatardemo",
		},
		cover: {
			type: String,
			required: false,
			trim: true,
			default: "coverdemo",
		},
		phone: {
			type: String,
			trim: true,
		},
		moneyPoint: {
			type: Number,
			default: 0,
		},
		goldPoint: {
			type: Number,
			default: 0,
		},
		moneyPointTransactions: [{
			id: { type: String, required: true },
			amount: { type: Number, required: true },
			type: { type: String, enum: ['deduct', 'add'], required: true },
			reason: { type: String, required: true },
			feature: { type: String, required: true },
			metadata: { type: mongoose.Schema.Types.Mixed },
			timestamp: { type: Date, required: true },
			balanceAfter: { type: Number, required: true }
		}],
		isEmailVerified: {
			type: Boolean,
			default: false,
		},
		emailVerificationToken: {
			type: String,
		},
		emailVerificationExpires: {
			type: Date,
		},
		passwordResetToken: {
			type: String,
		},
		passwordResetExpires: {
			type: Date,
		},
		refreshToken: {
			type: String,
		},
		isActive: {
			type: Boolean,
			default: true,
		},
		lastSignin: {
			type: Date,
		},
	},
	{
		timestamps: true,
		versionKey: false,
	},
);

// Indexes
// userSchema.index({ email: 1 });
// userSchema.index({ emailVerificationToken: 1 });
// userSchema.index({ passwordResetToken: 1 });
// userSchema.index({ refreshToken: 1 });

export const User = mongoose.model<IUser>("User", userSchema);
