import { z } from "zod";

// Signup schema
export const signupSchema = z
	.object({
		email: z.string().email("อีเมลไม่ถูกต้อง"),
		password: z
			.string()
			.min(8, "รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร")
			.max(20, "รหัสผ่านต้องไม่เกิน 20 ตัวอักษร")
			.regex(/^[a-zA-Z0-9!@#$%^&*()_+-=]+$/, "รหัสผ่านต้องมีเฉพาะตัวอักษรภาษาอังกฤษ ตัวเลข หรืออักขระพิเศษ (!@#$%^&*()_+-=) เท่านั้น"),
		confirmPassword: z.string().min(1, "ยืนยันรหัสผ่านต้องไม่ว่าง"),
		agreeToTerms: z.boolean().refine((val) => val, {
			message: "ต้องยอมรับข้อตกลงและเงื่อนไขการใช้งานเว็บไซต์",
		}),
	})
	.refine((data) => data.password === data.confirmPassword, {
		message: "รหัสผ่านไม่ตรงกัน",
		path: ["confirmPassword"],
	});

// Signin schema
export const signinSchema = z.object({
	email: z.email("อีเมลไม่ถูกต้อง"),
	password: z.string().min(1, "กรุณากรอกรหัสผ่าน"),
	rememberMe: z.boolean().optional().default(false),
});

// Email verification schema
export const emailVerificationSchema = z.object({
	token: z.string().min(1, "Token ไม่ถูกต้อง"),
});

// Forgot password schema
export const forgotPasswordSchema = z.object({
	email: z.string().email("อีเมลไม่ถูกต้อง"),
});

// Reset password schema
export const resetPasswordSchema = z.object({
	token: z.string().min(1, "Token ไม่ถูกต้อง"),
	password: z
		.string()
		.min(8, "รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร")
		.max(20, "รหัสผ่านต้องไม่เกิน 20 ตัวอักษร")
		.regex(/^[a-zA-Z0-9!@#$%^&*()_+-=]+$/, "รหัสผ่านต้องมีเฉพาะตัวอักษรภาษาอังกฤษ ตัวเลข หรืออักขระพิเศษ (!@#$%^&*()_+-=) เท่านั้น"),
});

// Change password schema
export const changePasswordSchema = z.object({
	currentPassword: z.string().min(1, "กรุณากรอกรหัสผ่านปัจจุบัน"),
	newPassword: z
		.string()
		.min(8, "รหัสผ่านใหม่ต้องมีอย่างน้อย 8 ตัวอักษร")
		.max(20, "รหัสผ่านใหม่ต้องไม่เกิน 20 ตัวอักษร")
		.regex(/^[a-zA-Z0-9!@#$%^&*()_+-=]+$/, "รหัสผ่านต้องมีเฉพาะตัวอักษรภาษาอังกฤษ ตัวเลข หรืออักขระพิเศษ (!@#$%^&*()_+-=) เท่านั้น"),
});

// Update profile schema
export const updateProfileSchema = z.object({
	firstName: z.string().min(2, "ชื่อต้องมีอย่างน้อย 2 ตัวอักษร").optional(),
	lastName: z.string().min(2, "นามสกุลต้องมีอย่างน้อย 2 ตัวอักษร").optional(),
	phone: z.string().optional(),
	avatar: z.string().optional(),
});

// Refresh token schema
export const refreshTokenSchema = z.object({
	refreshToken: z.string().min(1, "Refresh token ไม่ถูกต้อง"),
});

export type SignupInput = z.infer<typeof signupSchema>;
export type SigninInput = z.infer<typeof signinSchema>;
export type EmailVerificationInput = z.infer<typeof emailVerificationSchema>;
export type ForgotPasswordInput = z.infer<typeof forgotPasswordSchema>;
export type ResetPasswordInput = z.infer<typeof resetPasswordSchema>;
export type ChangePasswordInput = z.infer<typeof changePasswordSchema>;
export type UpdateProfileInput = z.infer<typeof updateProfileSchema>;
export type RefreshTokenInput = z.infer<typeof refreshTokenSchema>;
