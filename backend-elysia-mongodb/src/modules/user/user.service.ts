import * as argon2 from "argon2";
import { generateToken } from "@/core/utils/genId";
import { type IUser, User } from "./user.model";
import { TokenBlacklistService } from "../auth/blacklist.service";
import { safeUser, safeUserFromDoc } from "./user.utils";
import { EmailService } from "@/core/services/email.service";
import type {
	ChangePasswordInput,
	EmailVerificationInput,
	ForgotPasswordInput,
	SigninInput,
	RefreshTokenInput,
	SignupInput,
	ResetPasswordInput,
	UpdateProfileInput,
} from "./user.schemas";

export class UserService {
	// Signup new user
	static async signup(
		data: SignupInput,
	): Promise<{ user: Omit<IUser, "password">; verificationToken: string }> {
		const { email, password } = data;

		// Check if user already exists
		const existingUser = await User.findOne({ email });
		if (existingUser) {
			throw new Error("อีเมลนี้ถูกใช้งานแล้ว");
		}

		// Hash password
		const hashedPassword = await argon2.hash(password);

		// Generate email verification token
		const verificationToken = generateToken();
		const verificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

		// Create user
		const user = new User({
			email,
			password: hashedPassword,
			firstName: "User", // Default first name
			lastName: "User", // Default last name
			emailVerificationToken: verificationToken,
			emailVerificationExpires: verificationExpires,
		});

		await user.save();

		// Send verification email
		try {
			await EmailService.sendVerificationEmail(email, verificationToken);
		} catch (error) {
			console.error('Failed to send verification email:', error);
			// Don't throw error here, user is already created
		}

		// Return safe user data without sensitive information
		const safeUserData = safeUserFromDoc(user);
		return { user: safeUserData, verificationToken };
	}

	// Signin user
	static async signin(
		data: SigninInput,
	): Promise<{ user: Omit<IUser, "password"> & { createdAt: string; updatedAt: string } }> {
		const { email, password } = data;

		// Find user
		const user = await User.findOne({ email });
		if (!user) {
			throw new Error("อีเมลหรือรหัสผ่านไม่ถูกต้อง");
		}

		// Check if user is active
		if (!user.isActive) {
			throw new Error("บัญชีผู้ใช้ถูกระงับ");
		}

		// Verify password
		const isValidPassword = await argon2.verify(user.password, password);
		if (!isValidPassword) {
			throw new Error("อีเมลหรือรหัสผ่านไม่ถูกต้อง");
		}

		// Update last signin
		user.lastSignin = new Date();
		await user.save();

		// Return safe user data without sensitive information
		const safeUserData = safeUserFromDoc(user);
		const userData = {
			...safeUserData,
			createdAt: user.createdAt.toISOString(),
			updatedAt: user.updatedAt.toISOString(),
		};

		return {
			user: userData as any,
		};
	}

	// Verify email
	static async verifyEmail(data: EmailVerificationInput): Promise<{ message: string }> {
		const { token } = data;

		const user = await User.findOne({
			emailVerificationToken: token,
			emailVerificationExpires: { $gt: new Date() },
		});

		if (!user) {
			throw new Error("Token ไม่ถูกต้องหรือหมดอายุ");
		}

		user.isEmailVerified = true;
		user.emailVerificationToken = undefined;
		user.emailVerificationExpires = undefined;
		await user.save();

		return { message: "ยืนยันอีเมลสำเร็จ" };
	}

	// Forgot password
	static async forgotPassword(data: ForgotPasswordInput): Promise<{ message: string }> {
		const { email } = data;

		const user = await User.findOne({ email });
		if (!user) {
			// Don't reveal if email exists or not
			return { message: "หากอีเมลนี้มีอยู่ในระบบ จะได้รับลิงก์รีเซ็ตรหัสผ่าน" };
		}

		// Generate reset token
		const resetToken = generateToken();
		const resetExpires = new Date(Date.now() + 1 * 60 * 60 * 1000); // 1 hour

		user.passwordResetToken = resetToken;
		user.passwordResetExpires = resetExpires;
		await user.save();

		// Send password reset email
		try {
			await EmailService.sendPasswordResetEmail(email, resetToken);
		} catch (error) {
			console.error('Failed to send password reset email:', error);
			// Don't throw error here, still return success message for security
		}

		return { message: "หากอีเมลนี้มีอยู่ในระบบ จะได้รับลิงก์รีเซ็ตรหัสผ่าน" };
	}

	// Reset password
	static async resetPassword(data: ResetPasswordInput): Promise<{ message: string }> {
		const { token, password } = data;

		const user = await User.findOne({
			passwordResetToken: token,
			passwordResetExpires: { $gt: new Date() },
		});

		if (!user) {
			throw new Error("Token ไม่ถูกต้องหรือหมดอายุ");
		}

		// Hash new password
		const hashedPassword = await argon2.hash(password);

		user.password = hashedPassword;
		user.passwordResetToken = undefined;
		user.passwordResetExpires = undefined;
		await user.save();

		return { message: "รีเซ็ตรหัสผ่านสำเร็จ" };
	}

	// Change password
	static async changePassword(
		userId: string,
		data: ChangePasswordInput,
	): Promise<{ message: string }> {
		const { currentPassword, newPassword } = data;

		const user = await User.findById(userId);
		if (!user) {
			throw new Error("ไม่พบผู้ใช้");
		}

		// Verify current password
		const isValidPassword = await argon2.verify(user.password, currentPassword);
		if (!isValidPassword) {
			throw new Error("รหัสผ่านปัจจุบันไม่ถูกต้อง");
		}

		// Hash new password
		const hashedPassword = await argon2.hash(newPassword);

		user.password = hashedPassword;
		await user.save();

		return { message: "เปลี่ยนรหัสผ่านสำเร็จ" };
	}

	// Update profile
	static async updateProfile(
		userId: string,
		data: UpdateProfileInput,
	): Promise<Omit<IUser, "password">> {
		const user = await User.findById(userId);
		if (!user) {
			throw new Error("ไม่พบผู้ใช้");
		}

		// Update fields
		if (data.firstName) user.firstName = data.firstName;
		if (data.lastName) user.lastName = data.lastName;
		if (data.phone !== undefined) user.phone = data.phone;
		if (data.avatar !== undefined) user.avatar = data.avatar;

		await user.save();

		return safeUserFromDoc(user);
	}

	// Get profile
	static async getProfile(userId: string): Promise<Omit<IUser, "password">> {
		const user = await User.findById(userId);
		if (!user) {
			throw new Error("ไม่พบผู้ใช้");
		}

		return safeUserFromDoc(user);
	}

	// Refresh token
	static async refreshToken(
		data: RefreshTokenInput,
	): Promise<{ userId: string; refreshToken: string }> {
		const { refreshToken } = data;

		const user = await User.findOne({ refreshToken });
		if (!user) {
			throw new Error("Refresh token ไม่ถูกต้อง");
		}

		// Generate new refresh token
		const newRefreshToken = generateToken();
		user.refreshToken = newRefreshToken;
		await user.save();

		return {
			userId: user._id,
			refreshToken: newRefreshToken,
		};
	}

	// Update refresh token
	static async updateRefreshToken(userId: string, refreshToken: string): Promise<void> {
		const user = await User.findById(userId);
		if (!user) {
			throw new Error("ไม่พบผู้ใช้");
		}

		user.refreshToken = refreshToken;
		await user.save();
	}

	// Signout
	static async signout(userId: string, accessToken?: string): Promise<{ message: string }> {
		const user = await User.findById(userId);
		if (!user) {
			throw new Error("ไม่พบผู้ใช้");
		}

		// เพิ่ม access token เข้า blacklist ถ้ามี
		if (accessToken) {
			const accessTokenExpiry = new Date(Date.now() + 60 * 60 * 1000); // 1 ชั่วโมง
			await TokenBlacklistService.addToBlacklist(
				accessToken,
				'access',
				userId,
				accessTokenExpiry,
				'logout'
			);
		}

		// เพิ่ม refresh token เข้า blacklist ถ้ามี
		if (user.refreshToken) {
			const refreshTokenExpiry = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 วัน
			await TokenBlacklistService.addToBlacklist(
				user.refreshToken,
				'refresh',
				userId,
				refreshTokenExpiry,
				'logout'
			);
		}

		// ลบ refresh token จาก database
		user.refreshToken = undefined;
		await user.save();

		return { message: "ออกจากระบบสำเร็จ" };
	}

	// Resend verification email
	static async resendVerificationEmail(email: string): Promise<{ message: string }> {
		const user = await User.findOne({ email });
		if (!user) {
			throw new Error("ไม่พบผู้ใช้");
		}

		if (user.isEmailVerified) {
			throw new Error("อีเมลได้รับการยืนยันแล้ว");
		}

		// Generate new verification token
		const verificationToken = generateToken();
		const verificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

		user.emailVerificationToken = verificationToken;
		user.emailVerificationExpires = verificationExpires;
		await user.save();

		// Send verification email
		try {
			await EmailService.sendVerificationEmail(email, verificationToken);
		} catch (error) {
			console.error('Failed to resend verification email:', error);
			// Don't throw error here, token is already updated
		}

		return { message: "ส่งอีเมลยืนยันใหม่แล้ว" };
	}
}
