import type { IUser } from "./user.model";

/**
 * สร้าง safe user object โดยกรองข้อมูลที่ไม่ควรเปิดเผย
 */
export function safeUser(user: IUser): Omit<IUser, 'password' | 'emailVerificationToken' | 'passwordResetToken' | 'refreshToken'> {
    const {
        password,
        emailVerificationToken,
        passwordResetToken,
        refreshToken,
        ...safeUserData
    } = user;

    return safeUserData;
}

/**
 * สร้าง safe user object จาก MongoDB document
 */
export function safeUserFromDoc(userDoc: any): Omit<IUser, 'password' | 'emailVerificationToken' | 'passwordResetToken' | 'refreshToken'> {
    const userObj = userDoc.toObject ? userDoc.toObject() : userDoc;
    return safeUser(userObj);
}