import { cors } from "@elysiajs/cors";
import { serverTiming } from "@elysiajs/server-timing";
import { Elysia } from "elysia";
import { Logestic } from "logestic";
import { connectDB } from "@/core/config/database";
import { userRoute } from "@/modules/user";
import { customerRoute } from "@/modules/customer";
import { adminRoute } from "@/modules/admin";
import { siteRoute, discountRoute } from "@/modules/site";
import { config } from "@/core/config";

// เชื่อมต่อฐานข้อมูล
await connectDB();

const app = new Elysia({
	aot: true,
	websocket: {
		idleTimeout: 30,
		maxPayloadLength: 1024 * 1024, // 1MB
		backpressureLimit: 1024 * 1024 * 16, // 16MB
		closeOnBackpressureLimit: false,
		perMessageDeflate: false,
	},
})

	.use(
		cors({
			origin: true,
			credentials: true,
			allowedHeaders: ["Content-Type", "Authorization"],
		}),
	)
	.use(Logestic.preset("fancy"))
	.use(serverTiming())
	.get("/", () => ({
		success: true,
		message: "Webshop API v1.0 🚀",
		timestamp: new Date().toISOString(),
	}))
	.get("/health", () => ({
		success: true,
		status: "healthy",
		timestamp: new Date().toISOString(),
	}))
	// .use(chatWebSocket)
	.group(
		"/v1",
		(app) => app.use(userRoute).use(customerRoute).use(adminRoute).use(siteRoute).use(discountRoute),
		// .use(roleRoute)
		// .use(productRoute)
		// .use(chatRoute),
	)
	// Error handling
	.onError(({ error, code, set }) => {
		console.error("API Error:", error);

		// จัดการ validation errors
		if (code === "VALIDATION") {
			set.status = 400;
			return {
				success: false,
				message: "ข้อมูลไม่ถูกต้อง",
				error: "ข้อมูลที่ส่งมาไม่ถูกต้องตามรูปแบบที่กำหนด",
				details: error instanceof Error && "issues" in error ? error.issues : error,
			};
		}

		// จัดการ Zod errors
		if (error instanceof Error && error.name === "ZodError") {
			set.status = 400;
			return {
				success: false,
				message: "ข้อมูลไม่ถูกต้อง",
				error: "ข้อมูลที่ส่งมาไม่ถูกต้องตามรูปแบบที่กำหนด",
				details: "issues" in error ? error.issues : error,
			};
		}

		// จัดการ HTTP errors จาก service
		if (error instanceof Error && error.message) {
			let statusCode = 500;
			if (
				error.message.includes("อีเมลนี้ถูกใช้งานแล้ว") ||
				error.message.includes("Token ไม่ถูกต้องหรือหมดอายุ") ||
				error.message.includes("รหัสผ่านปัจจุบันไม่ถูกต้อง") ||
				error.message.includes("อีเมลได้รับการยืนยันแล้ว")
			) {
				statusCode = 400;
			} else if (
				error.message.includes("อีเมลหรือรหัสผ่านไม่ถูกต้อง") ||
				error.message.includes("บัญชีผู้ใช้ถูกระงับ") ||
				error.message.includes("Refresh token ไม่ถูกต้อง")
			) {
				statusCode = 401;
			} else if (error.message.includes("ไม่พบผู้ใช้")) {
				statusCode = 404;
			}

			set.status = statusCode;
			return {
				success: false,
				message: error.message,
			};
		}

		// จัดการ errors อื่นๆ
		const errorMessage = error instanceof Error ? error.message : "เกิดข้อผิดพลาด";
		set.status = 500;
		return {
			success: false,
			message: errorMessage || "เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์",
		};
	});
app.listen(config.port);

console.log(`🦊 Elysia is running at ${app.server?.hostname}:${app.server?.port}`);
