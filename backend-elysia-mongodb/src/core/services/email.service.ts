import nodemailer from 'nodemailer';
import { config } from '../config/environment';

export interface EmailOptions {
    to: string;
    subject: string;
    html: string;
    text?: string;
}

export class EmailService {
    private static transporter = nodemailer.createTransport({
        host: config.emailHost,
        port: Number(config.emailPort) || 587,
        secure: config.emailSecure,
        auth: {
            user: config.emailUser,
            pass: config.emailPassword,
        },
    });

    static async sendEmail(options: EmailOptions): Promise<void> {
        try {
            await this.transporter.sendMail({
                from: config.emailFrom,
                to: options.to,
                subject: options.subject,
                html: options.html,
                text: options.text,
            });
        } catch (error) {
            console.error('Email sending failed:', error);
            throw new Error('ไม่สามารถส่งอีเมลได้');
        }
    }

    static async sendVerificationEmail(email: string, token: string): Promise<void> {
        const verificationUrl = `${config.dashboardUrl}/verify-email?token=${token}`;
        const logoUrl = `${config.dashboardUrl}/logo.avif`;

        const html = `
			<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
				<img src="${logoUrl}" alt="Is1.Site" style="width: 100px; height: 100px; margin-bottom: 20px;">
				<h2 style="color: #333;">ยืนยันอีเมลของคุณ</h2>
				<p>กรุณาคลิกลิงก์ด้านล่างเพื่อยืนยันอีเมลของคุณ:</p>
				<a href="${verificationUrl}" 
				   style="display: inline-block; padding: 12px 24px; background-color: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 16px 0;">
					ยืนยันอีเมล
				</a>
				<p>หรือคัดลอกลิงก์นี้ไปวางในเบราว์เซอร์:</p>
				<p style="word-break: break-all; color: #666;">${verificationUrl}</p>
				<p style="color: #666; font-size: 14px;">ลิงก์นี้จะหมดอายุใน 24 ชั่วโมง</p>
			</div>
		`;

        await this.sendEmail({
            to: email,
            subject: 'ยืนยันอีเมลของคุณ - Is1.Site',
            html,
            text: `ยืนยันอีเมลของคุณโดยคลิกลิงก์: ${verificationUrl}`,
        });
    }

    static async sendPasswordResetEmail(email: string, token: string): Promise<void> {
        const resetUrl = `${config.dashboardUrl}/reset-password?token=${token}`;
        const logoUrl = `${config.dashboardUrl}/logo.avif`;
        const html = `
			<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
				<img src="${logoUrl}" alt="Is1.Site" style="width: 100px; height: 100px; margin-bottom: 20px;">
				<h2 style="color: #333;">รีเซ็ตรหัสผ่าน</h2>
				<p>คุณได้ขอรีเซ็ตรหัสผ่าน กรุณาคลิกลิงก์ด้านล่าง:</p>
				<a href="${resetUrl}" 
				   style="display: inline-block; padding: 12px 24px; background-color: #dc3545; color: white; text-decoration: none; border-radius: 4px; margin: 16px 0;">
					รีเซ็ตรหัสผ่าน
				</a>
				<p>หรือคัดลอกลิงก์นี้ไปวางในเบราว์เซอร์:</p>
				<p style="word-break: break-all; color: #666;">${resetUrl}</p>
				<p style="color: #666; font-size: 14px;">ลิงก์นี้จะหมดอายุใน 1 ชั่วโมง</p>
				<p style="color: #999; font-size: 12px;">หากคุณไม่ได้ขอรีเซ็ตรหัสผ่าน กรุณาเพิกเฉยต่ออีเมลนี้</p>
			</div>
		`;

        await this.sendEmail({
            to: email,
            subject: 'รีเซ็ตรหัสผ่าน - Is1.Site',
            html,
            text: `รีเซ็ตรหัสผ่านของคุณโดยคลิกลิงก์: ${resetUrl}`,
        });
    }
}