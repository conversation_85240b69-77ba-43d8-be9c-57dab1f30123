import mongoose from "mongoose";

export const connectDB = async () => {
	try {
		const mongoUri = process.env.MONGO_URI || "mongodb://localhost:27017/webshop07";
		await mongoose.connect(mongoUri);
		console.log("✅ MongoDB connected successfully");
	} catch (error) {
		console.error("❌ MongoDB connection failed:", error);
		process.exit(1);
	}
};

export const disconnectDB = async () => {
	try {
		await mongoose.disconnect();
		console.log("🔌 MongoDB disconnected");
	} catch (error) {
		console.error("❌ MongoDB disconnection failed:", error);
	}
};
