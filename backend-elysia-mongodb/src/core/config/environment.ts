export const config = {
	isDev: Bun.env.NODE_ENV !== "production",
	port: Number(Bun.env.PORT) || 3000,
	mongodbUri: Bun.env.MONGODB_URI || "mongodb://localhost:27017/auth",
	
	// JWT Configuration
	jwtSecret: Bun.env.JWT_SECRET || "your-secret-key",
	jwtRefreshSecret: Bun.env.JWT_REFRESH_SECRET || "your-refresh-secret-key",
	jwtExpiresIn: Bun.env.JWT_EXPIRES_IN || "7d",
	refreshTokenExpiresIn: Bun.env.REFRESH_TOKEN_EXPIRES_IN || "30d",
	
	// Customer JWT Configuration
	customerJwtSecret: Bun.env.CUSTOMER_JWT_SECRET || "customer-secret-key",
	customerJwtRefreshSecret: Bun.env.CUSTOMER_JWT_REFRESH_SECRET || "customer-refresh-secret-key",
	// Dashboard URL
	dashboardUrl: Bun.env.DASHBOARD_URL || "http://localhost:8000",

	// Email configuration
	emailHost: Bun.env.SMTP_HOST || "smtp.gmail.com",
	emailPort: Number(Bun.env.SMTP_PORT) || 587,
	emailSecure: Bun.env.SMTP_SECURE === "true",
	emailUser: Bun.env.SMTP_USER || "",
	emailPassword: Bun.env.SMTP_PASS || "",
	emailFrom: Bun.env.SMTP_FROM || "<EMAIL>",

	// Cloudinary configuration
	cloudinaryCloudName: Bun.env.CLOUDINARY_CLOUD_NAME || "",
	cloudinaryApiKey: Bun.env.CLOUDINARY_API_KEY || "",
	cloudinaryApiSecret: Bun.env.CLOUDINARY_API_SECRET || "",
} as const;
