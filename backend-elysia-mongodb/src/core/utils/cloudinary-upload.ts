import { config } from "../config/environment";
import { v2 as cloudinary } from "cloudinary";

// Configure Cloudinary
cloudinary.config({
	cloud_name: config.cloudinaryCloudName,
	api_key: config.cloudinaryApiKey,
	api_secret: config.cloudinaryApiSecret,
});

// Interface สำหรับ upload options
export interface UploadOptions {
	folder?: string;
	publicId?: string;
	transformation?: any;
	resourceType?: "image" | "video" | "raw" | "auto";
	format?: string;
	quality?: string | number;
	width?: number;
	height?: number;
	crop?: string;
	secure?: boolean;
	tags?: string[];
	context?: Record<string, string>;
	metadata?: Record<string, string>;
	overwrite?: boolean;
	allowedFormats?: string[];
	maxFileSize?: number; // bytes
	eager?: string[];
	notification_url?: string;
}

// Interface สำหรับ signed upload options
export interface SignedUploadOptions extends UploadOptions {
	timestamp?: number;
	eager?: string;
	tags?: string[];
}

// Interface สำหรับผลลัพธ์การ upload
export interface UploadResult {
	publicId: string;
	secureUrl: string;
	url: string;
	format: string;
	resourceType: string;
	width?: number;
	height?: number;
	bytes: number;
	signature?: string;
	etag?: string;
	version?: number;
	createdAt?: string;
	tags?: string[];
	context?: Record<string, string>;
	metadata?: Record<string, string>;
}

// Interface สำหรับ batch upload
export interface BatchUploadResult {
	successful: UploadResult[];
	failed: Array<{
		file: string;
		error: string;
	}>;
	total: number;
	successCount: number;
	failureCount: number;
}

// Interface สำหรับ file validation
export interface FileValidationOptions {
	maxFileSize?: number; // bytes
	allowedMimeTypes?: string[];
	allowedExtensions?: string[];
	minWidth?: number;
	minHeight?: number;
	maxWidth?: number;
	maxHeight?: number;
}

// Interface สำหรับ progress tracking
export interface UploadProgress {
	loaded: number;
	total: number;
	percentage: number;
	stage: 'validating' | 'processing' | 'uploading' | 'transforming' | 'complete';
	fileName?: string;
	currentFile?: number;
	totalFiles?: number;
}

// Type สำหรับ progress callback
export type ProgressCallback = (progress: UploadProgress) => void;

class CloudinaryUploadUtil {
	constructor() {
		// ตรวจสอบว่า Cloudinary ได้ถูก config แล้วหรือไม่
		if (!config.cloudinaryCloudName || !config.cloudinaryApiKey || !config.cloudinaryApiSecret) {
			throw new Error("Cloudinary configuration is missing");
		}
	}

	/**
	 * แปลงข้อมูลภาพให้เป็นรูปแบบที่ Cloudinary รองรับ
	 */
	private async processFileInput(file: string | Buffer | File | any): Promise<string | Buffer> {
		// ถ้าเป็น string และเป็น base64
		if (typeof file === "string") {
			if (file.startsWith("data:")) {
				// Base64 with data URL prefix
				const base64Data = file.replace(/^data:image\/\w+;base64,/, "");
				return Buffer.from(base64Data, "base64");
			} else if (file.match(/^[A-Za-z0-9+/]+=*$/)) {
				// Pure base64 string
				return Buffer.from(file, "base64");
			} else {
				// File path
				return file;
			}
		}

		// ถ้าเป็น File object (จาก browser)
		if (file && typeof file.arrayBuffer === "function") {
			const arrayBuffer = await file.arrayBuffer();
			return Buffer.from(arrayBuffer);
		}

		// ถ้าเป็น Buffer แล้ว
		if (Buffer.isBuffer(file)) {
			return file;
		}

		throw new Error("รูปแบบไฟล์ไม่รองรับ");
	}

	/**
	 * อัพโหลดไฟล์แบบ public พร้อม progress tracking
	 * @param file - รองรับ: File path (string), Buffer, Base64 string, หรือ File object
	 * @param options - ตัวเลือกการอัพโหลด
	 * @param onProgress - callback สำหรับติดตามความคืบหน้า
	 */
	async uploadPublic(
		file: string | Buffer | File,
		options: UploadOptions = {},
		onProgress?: ProgressCallback
	): Promise<UploadResult> {
		try {
			// Progress: Validating
			onProgress?.({
				loaded: 0,
				total: 100,
				percentage: 0,
				stage: 'validating'
			});

			// แปลงไฟล์ให้เป็นรูปแบบที่ Cloudinary รองรับ
			const processedFile = await this.processFileInput(file);

			// Progress: Processing
			onProgress?.({
				loaded: 20,
				total: 100,
				percentage: 20,
				stage: 'processing'
			});

			const uploadOptions = {
				folder: options.folder || "uploads",
				resource_type: options.resourceType || "auto",
				public_id: options.publicId,
				transformation: options.transformation,
				format: options.format,
				quality: options.quality,
				width: options.width,
				height: options.height,
				crop: options.crop,
				secure: options.secure !== false, // default เป็น true
				tags: options.tags,
				context: options.context,
				metadata: options.metadata,
				overwrite: options.overwrite,
				eager: options.eager,
			};

			// Progress: Uploading
			onProgress?.({
				loaded: 40,
				total: 100,
				percentage: 40,
				stage: 'uploading'
			});

			const result = await cloudinary.uploader.upload(processedFile, uploadOptions);

			// Progress: Transforming (if transformations exist)
			if (options.transformation || options.eager) {
				onProgress?.({
					loaded: 80,
					total: 100,
					percentage: 80,
					stage: 'transforming'
				});
			}

			// Progress: Complete
			onProgress?.({
				loaded: 100,
				total: 100,
				percentage: 100,
				stage: 'complete'
			});

			return {
				publicId: result.public_id,
				secureUrl: result.secure_url,
				url: result.url,
				format: result.format,
				resourceType: result.resource_type,
				width: result.width,
				height: result.height,
				bytes: result.bytes,
				etag: result.etag,
				version: result.version,
				createdAt: result.created_at,
				tags: result.tags,
				context: result.context,
				metadata: result.metadata,
			};
		} catch (error: any) {
			console.error("Error uploading file to Cloudinary:", error);
			throw new Error(`ไม่สามารถอัพโหลดไฟล์ได้: ${error.message}`);
		}
	}

	/**
	 * อัพโหลดไฟล์แบบ signed (มี signature สำหรับความปลอดภัย)
	 * @param file - รองรับ: File path (string), Buffer, Base64 string, หรือ File object
	 */
	async uploadSigned(
		file: string | Buffer | File,
		options: SignedUploadOptions = {},
	): Promise<UploadResult> {
		try {
			// แปลงไฟล์ให้เป็นรูปแบบที่ Cloudinary รองรับ
			const processedFile = await this.processFileInput(file);
			const timestamp = options.timestamp || Math.round(new Date().getTime() / 1000);

			const uploadOptions = {
				folder: options.folder || "uploads",
				resource_type: options.resourceType || "auto",
				public_id: options.publicId,
				timestamp,
				transformation: options.transformation,
				format: options.format,
				quality: options.quality,
				width: options.width,
				height: options.height,
				crop: options.crop,
				eager: options.eager,
				tags: options.tags,
				secure: options.secure !== false,
			};

			const result = await cloudinary.uploader.upload(processedFile, uploadOptions);

			return {
				publicId: result.public_id,
				secureUrl: result.secure_url,
				url: result.url,
				format: result.format,
				resourceType: result.resource_type,
				width: result.width,
				height: result.height,
				bytes: result.bytes,
				signature: result.signature,
			};
		} catch (error: any) {
			console.error("Error uploading signed file to Cloudinary:", error);
			throw new Error(`ไม่สามารถอัพโหลดไฟล์แบบ signed ได้: ${error.message}`);
		}
	}

	/**
	 * สร้าง signed URL สำหรับ upload จาก client-side
	 */
	generateSignedUploadUrl(options: SignedUploadOptions = {}): {
		signature: string;
		timestamp: number;
		cloudName: string;
		apiKey: string;
		uploadUrl: string;
	} {
		const timestamp = options.timestamp || Math.round(new Date().getTime() / 1000);

		const params = {
			timestamp,
			folder: options.folder || "uploads",
			resource_type: options.resourceType || "auto",
			...(options.publicId && { public_id: options.publicId }),
			...(options.transformation && { transformation: options.transformation }),
			...(options.format && { format: options.format }),
			...(options.quality && { quality: options.quality }),
			...(options.eager && { eager: options.eager }),
			...(options.tags && { tags: options.tags.join(",") }),
		};

		const signature = cloudinary.utils.api_sign_request(params, config.cloudinaryApiSecret);

		return {
			signature,
			timestamp,
			cloudName: config.cloudinaryCloudName,
			apiKey: config.cloudinaryApiKey,
			uploadUrl: `https://api.cloudinary.com/v1_1/${config.cloudinaryCloudName}/upload`,
		};
	}

	/**
	 * ลบไฟล์จาก Cloudinary
	 */
	async deleteFile(
		publicId: string,
		resourceType: "image" | "video" | "raw" = "image",
	): Promise<void> {
		try {
			await cloudinary.uploader.destroy(publicId, { resource_type: resourceType });
		} catch (error: any) {
			console.error("Error deleting file from Cloudinary:", error);
			throw new Error(`ไม่สามารถลบไฟล์ได้: ${error.message}`);
		}
	}

	/**
	 * อัพโหลดรูปโปรไฟล์ (ปรับขนาดอัตโนมัติ)
	 * @param file - รองรับ: File path, Buffer, Base64 string, หรือ File object
	 */
	async uploadProfileImage(file: string | Buffer | File, userId: string): Promise<UploadResult> {
		return this.uploadPublic(file, {
			folder: "profiles",
			publicId: `profile_${userId}`,
			resourceType: "image",
			transformation: {
				width: 400,
				height: 400,
				crop: "fill",
				gravity: "face",
				quality: "auto:good",
				format: "webp",
			},
		});
	}

	/**
	 * อัพโหลดรูปปก (ปรับขนาดอัตโนมัติ)
	 * @param file - รองรับ: File path, Buffer, Base64 string, หรือ File object
	 */
	async uploadCoverImage(file: string | Buffer | File, userId: string): Promise<UploadResult> {
		return this.uploadPublic(file, {
			folder: "covers",
			publicId: `cover_${userId}`,
			resourceType: "image",
			transformation: {
				width: 1200,
				height: 400,
				crop: "fill",
				quality: "auto:good",
				format: "webp",
			},
		});
	}

	/**
	 * ตรวจสอบและแปลงข้อมูลรูปภาพ
	 */
	validateImageInput(file: any): { isValid: boolean; type: string; size?: number } {
		if (typeof file === "string") {
			if (file.startsWith("data:image/")) {
				return { isValid: true, type: "base64-dataurl" };
			} else if (file.match(/^[A-Za-z0-9+/]+=*$/)) {
				return { isValid: true, type: "base64" };
			} else {
				return { isValid: true, type: "filepath" };
			}
		}

		if (Buffer.isBuffer(file)) {
			return { isValid: true, type: "buffer", size: file.length };
		}

		if (file && typeof file.arrayBuffer === "function") {
			return { isValid: true, type: "file", size: file.size };
		}

		return { isValid: false, type: "unknown" };
	}

	/**
	 * ตรวจสอบไฟล์ตามเงื่อนไขที่กำหนด
	 */
	async validateFile(file: any, options: FileValidationOptions = {}): Promise<{
		isValid: boolean;
		errors: string[];
		fileInfo?: {
			size: number;
			type: string;
			mimeType?: string;
			extension?: string;
		};
	}> {
		const errors: string[] = [];
		let fileInfo: any = {};

		try {
			// ตรวจสอบรูปแบบไฟล์พื้นฐาน
			const basicValidation = this.validateImageInput(file);
			if (!basicValidation.isValid) {
				errors.push("รูปแบบไฟล์ไม่รองรับ");
				return { isValid: false, errors };
			}

			// ดึงข้อมูลไฟล์
			if (file && typeof file === 'object' && file.size) {
				fileInfo.size = file.size;
				fileInfo.type = file.type || '';
				fileInfo.mimeType = file.type;
				fileInfo.extension = file.name ? file.name.split('.').pop()?.toLowerCase() : '';
			} else if (Buffer.isBuffer(file)) {
				fileInfo.size = file.length;
				fileInfo.type = 'buffer';
			}

			// ตรวจสอบขนาดไฟล์
			if (options.maxFileSize && fileInfo.size > options.maxFileSize) {
				errors.push(`ขนาดไฟล์เกินกำหนด (${Math.round(fileInfo.size / 1024 / 1024 * 100) / 100}MB > ${Math.round(options.maxFileSize / 1024 / 1024 * 100) / 100}MB)`);
			}

			// ตรวจสอบ MIME type
			if (options.allowedMimeTypes && fileInfo.mimeType && !options.allowedMimeTypes.includes(fileInfo.mimeType)) {
				errors.push(`ประเภทไฟล์ไม่รองรับ (${fileInfo.mimeType})`);
			}

			// ตรวจสอบนามสกุลไฟล์
			if (options.allowedExtensions && fileInfo.extension && !options.allowedExtensions.includes(fileInfo.extension)) {
				errors.push(`นามสกุลไฟล์ไม่รองรับ (.${fileInfo.extension})`);
			}

			return {
				isValid: errors.length === 0,
				errors,
				fileInfo
			};
		} catch (error: any) {
			errors.push(`เกิดข้อผิดพลาดในการตรวจสอบไฟล์: ${error.message}`);
			return { isValid: false, errors };
		}
	}

	/**
	 * อัพโหลดหลายไฟล์พร้อมกัน (Batch Upload)
	 */
	async uploadBatch(
		files: Array<{ file: string | Buffer | File; options?: UploadOptions }>,
		globalOptions: UploadOptions = {}
	): Promise<BatchUploadResult> {
		const results: BatchUploadResult = {
			successful: [],
			failed: [],
			total: files.length,
			successCount: 0,
			failureCount: 0
		};

		// อัพโหลดแบบ parallel แต่จำกัดจำนวน concurrent
		const concurrentLimit = 5;
		const chunks = [];

		for (let i = 0; i < files.length; i += concurrentLimit) {
			chunks.push(files.slice(i, i + concurrentLimit));
		}

		for (const chunk of chunks) {
			const promises = chunk.map(async ({ file, options = {} }, index) => {
				try {
					const mergedOptions = { ...globalOptions, ...options };
					const result = await this.uploadPublic(file, mergedOptions);
					results.successful.push(result);
					results.successCount++;
				} catch (error: any) {
					results.failed.push({
						file: `file_${index}`,
						error: error.message
					});
					results.failureCount++;
				}
			});

			await Promise.all(promises);
		}

		return results;
	}

	/**
	 * ลบหลายไฟล์พร้อมกัน (Batch Delete)
	 */
	async deleteBatch(
		publicIds: string[],
		resourceType: "image" | "video" | "raw" = "image"
	): Promise<{
		successful: string[];
		failed: Array<{ publicId: string; error: string }>;
		total: number;
	}> {
		const results = {
			successful: [] as string[],
			failed: [] as Array<{ publicId: string; error: string }>,
			total: publicIds.length
		};

		// ลบแบบ parallel
		const promises = publicIds.map(async (publicId) => {
			try {
				await this.deleteFile(publicId, resourceType);
				results.successful.push(publicId);
			} catch (error: any) {
				results.failed.push({
					publicId,
					error: error.message
				});
			}
		});

		await Promise.all(promises);
		return results;
	}

	/**
	 * สร้าง URL พร้อม transformation
	 */
	generateTransformationUrl(
		publicId: string,
		transformations: Record<string, any>,
		options: { secure?: boolean; resourceType?: string } = {}
	): string {
		try {
			return cloudinary.url(publicId, {
				...transformations,
				secure: options.secure !== false,
				resource_type: options.resourceType || "image"
			});
		} catch (error: any) {
			throw new Error(`ไม่สามารถสร้าง transformation URL ได้: ${error.message}`);
		}
	}

	/**
	 * ดึงข้อมูล metadata ของไฟล์
	 */
	async getFileInfo(publicId: string, resourceType: "image" | "video" | "raw" = "image"): Promise<any> {
		try {
			return await cloudinary.api.resource(publicId, { resource_type: resourceType });
		} catch (error: any) {
			throw new Error(`ไม่สามารถดึงข้อมูลไฟล์ได้: ${error.message}`);
		}
	}

	/**
	 * ค้นหาไฟล์ใน Cloudinary
	 */
	async searchFiles(query: {
		expression?: string;
		sortBy?: string;
		maxResults?: number;
		nextCursor?: string;
	} = {}): Promise<any> {
		try {
			return await cloudinary.search
				.expression(query.expression || 'resource_type:image')
				.sort_by(query.sortBy || 'created_at', 'desc')
				.max_results(query.maxResults || 50)
				.next_cursor(query.nextCursor)
				.execute();
		} catch (error: any) {
			throw new Error(`ไม่สามารถค้นหาไฟล์ได้: ${error.message}`);
		}
	}

	/**
	 * สร้าง Archive (ZIP) จากไฟล์หลายไฟล์
	 */
	async createArchive(
		publicIds: string[],
		options: {
			type?: 'upload' | 'zip';
			targetFormat?: string;
			mode?: 'create' | 'download';
		} = {}
	): Promise<{ url: string; publicId: string }> {
		try {
			const result = await cloudinary.utils.archive_params({
				type: options.type || 'zip',
				target_format: options.targetFormat,
				mode: options.mode || 'create',
				public_ids: publicIds
			});

			return {
				url: result.url,
				publicId: result.public_id
			};
		} catch (error: any) {
			throw new Error(`ไม่สามารถสร้าง archive ได้: ${error.message}`);
		}
	}

	/**
	 * อัพโหลดพร้อมใส่ลายน้ำ
	 */
	async uploadWithWatermark(
		file: string | Buffer | File,
		watermarkOptions: {
			text?: string;
			image?: string;
			position?: string;
			opacity?: number;
		},
		uploadOptions: UploadOptions = {}
	): Promise<UploadResult> {
		const transformation: any = {};

		if (watermarkOptions.text) {
			transformation.overlay = {
				text: watermarkOptions.text,
				font_family: "Arial",
				font_size: 60,
				font_weight: "bold"
			};
		} else if (watermarkOptions.image) {
			transformation.overlay = watermarkOptions.image;
		}

		if (watermarkOptions.position) {
			transformation.gravity = watermarkOptions.position;
		}

		if (watermarkOptions.opacity) {
			transformation.opacity = watermarkOptions.opacity;
		}

		return this.uploadPublic(file, {
			...uploadOptions,
			transformation
		});
	}

	/**
	 * ปรับขนาดรูปภาพอัตโนมัติตามอุปกรณ์
	 */
	async uploadResponsive(
		file: string | Buffer | File,
		breakpoints: Array<{ width: number; suffix: string }> = [
			{ width: 320, suffix: 'mobile' },
			{ width: 768, suffix: 'tablet' },
			{ width: 1200, suffix: 'desktop' }
		],
		uploadOptions: UploadOptions = {}
	): Promise<{
		original: UploadResult;
		responsive: Array<{ breakpoint: string; result: UploadResult }>;
	}> {
		// อัพโหลดไฟล์ต้นฉบับ
		const original = await this.uploadPublic(file, uploadOptions);

		// สร้างรูปภาพสำหรับแต่ละ breakpoint
		const responsive = await Promise.all(
			breakpoints.map(async ({ width, suffix }) => {
				const result = await this.uploadPublic(file, {
					...uploadOptions,
					publicId: `${uploadOptions.publicId || original.publicId}_${suffix}`,
					transformation: {
						width,
						crop: 'scale',
						quality: 'auto:good',
						format: 'auto'
					}
				});

				return {
					breakpoint: suffix,
					result
				};
			})
		);

		return { original, responsive };
	}

	/**
	 * อัพโหลดวิดีโอพร้อมสร้าง thumbnail และ multiple formats
	 */
	async uploadVideo(
		file: string | Buffer | File,
		options: UploadOptions & {
			generateThumbnails?: boolean;
			thumbnailTimes?: number[]; // เวลาในวินาทีที่ต้องการสร้าง thumbnail
			videoFormats?: string[]; // รูปแบบวิดีโอที่ต้องการ เช่น ['mp4', 'webm']
			quality?: 'auto' | 'auto:low' | 'auto:good' | 'auto:best' | number;
			maxDuration?: number; // ความยาวสูงสุดในวินาที
		} = {},
		onProgress?: ProgressCallback
	): Promise<{
		video: UploadResult;
		thumbnails: UploadResult[];
		formats: Array<{ format: string; result: UploadResult }>;
	}> {
		try {
			// Progress: Validating
			onProgress?.({
				loaded: 0,
				total: 100,
				percentage: 0,
				stage: 'validating'
			});

			// อัพโหลดวิดีโอต้นฉบับ
			const videoResult = await this.uploadPublic(file, {
				...options,
				resourceType: 'video',
				quality: options.quality || 'auto:good'
			}, onProgress);

			// Progress: Processing thumbnails
			onProgress?.({
				loaded: 60,
				total: 100,
				percentage: 60,
				stage: 'transforming'
			});

			const thumbnails: UploadResult[] = [];
			const formats: Array<{ format: string; result: UploadResult }> = [];

			// สร้าง thumbnails
			if (options.generateThumbnails !== false) {
				const thumbnailTimes = options.thumbnailTimes || [0, 10, 30]; // default thumbnails ที่ 0, 10, 30 วินาที

				for (const time of thumbnailTimes) {
					try {
						const thumbnailUrl = cloudinary.url(videoResult.publicId, {
							resource_type: 'video',
							format: 'jpg',
							transformation: [
								{ start_offset: `${time}s` },
								{ width: 400, height: 300, crop: 'fill', quality: 'auto:good' }
							]
						});

						// สร้าง thumbnail โดยการ upload URL
						const thumbnailResult = await this.uploadPublic(thumbnailUrl, {
							folder: `${options.folder || 'uploads'}/thumbnails`,
							publicId: `${videoResult.publicId}_thumb_${time}s`,
							resourceType: 'image'
						});

						thumbnails.push(thumbnailResult);
					} catch (error) {
						console.warn(`Failed to create thumbnail at ${time}s:`, error);
					}
				}
			}

			// สร้างรูปแบบวิดีโอเพิ่มเติม
			if (options.videoFormats && options.videoFormats.length > 0) {
				for (const format of options.videoFormats) {
					try {
						const formatResult = await this.uploadPublic(file, {
							...options,
							resourceType: 'video',
							format,
							publicId: `${videoResult.publicId}_${format}`,
							transformation: {
								quality: options.quality || 'auto:good',
								...(options.maxDuration && { duration: `0-${options.maxDuration}` })
							}
						});

						formats.push({ format, result: formatResult });
					} catch (error) {
						console.warn(`Failed to create ${format} format:`, error);
					}
				}
			}

			// Progress: Complete
			onProgress?.({
				loaded: 100,
				total: 100,
				percentage: 100,
				stage: 'complete'
			});

			return {
				video: videoResult,
				thumbnails,
				formats
			};
		} catch (error: any) {
			throw new Error(`ไม่สามารถอัพโหลดวิดีโอได้: ${error.message}`);
		}
	}

	/**
	 * สร้าง video thumbnail จาก video ที่มีอยู่แล้ว
	 */
	async generateVideoThumbnail(
		videoPublicId: string,
		options: {
			time?: number; // เวลาในวินาทีที่ต้องการสร้าง thumbnail
			width?: number;
			height?: number;
			crop?: string;
			quality?: string;
			format?: string;
		} = {}
	): Promise<string> {
		try {
			return cloudinary.url(videoPublicId, {
				resource_type: 'video',
				format: options.format || 'jpg',
				transformation: [
					{ start_offset: `${options.time || 0}s` },
					{
						width: options.width || 400,
						height: options.height || 300,
						crop: options.crop || 'fill',
						quality: options.quality || 'auto:good'
					}
				]
			});
		} catch (error: any) {
			throw new Error(`ไม่สามารถสร้าง video thumbnail ได้: ${error.message}`);
		}
	}

	/**
	 * แปลงวิดีโอเป็นรูปแบบต่างๆ
	 */
	async convertVideoFormat(
		videoPublicId: string,
		targetFormat: string,
		options: {
			quality?: string | number;
			bitRate?: string;
			fps?: number;
			duration?: string; // เช่น "0-30" สำหรับ 30 วินาทีแรก
			width?: number;
			height?: number;
		} = {}
	): Promise<string> {
		try {
			return cloudinary.url(videoPublicId, {
				resource_type: 'video',
				format: targetFormat,
				transformation: {
					quality: options.quality || 'auto:good',
					...(options.bitRate && { bit_rate: options.bitRate }),
					...(options.fps && { fps: options.fps }),
					...(options.duration && { duration: options.duration }),
					...(options.width && { width: options.width }),
					...(options.height && { height: options.height })
				}
			});
		} catch (error: any) {
			throw new Error(`ไม่สามารถแปลงรูปแบบวิดีโอได้: ${error.message}`);
		}
	}

	/**
	 * สร้าง adaptive streaming URLs สำหรับวิดีโอ
	 */
	generateAdaptiveStreamingUrls(
		videoPublicId: string,
		qualities: Array<{
			quality: string;
			width?: number;
			height?: number;
			bitRate?: string;
		}> = [
				{ quality: 'auto:low', width: 480, height: 360, bitRate: '500k' },
				{ quality: 'auto:good', width: 720, height: 480, bitRate: '1000k' },
				{ quality: 'auto:best', width: 1080, height: 720, bitRate: '2000k' }
			]
	): Array<{ quality: string; url: string; width?: number; height?: number }> {
		try {
			return qualities.map(({ quality, width, height, bitRate }) => ({
				quality,
				width,
				height,
				url: cloudinary.url(videoPublicId, {
					resource_type: 'video',
					format: 'mp4',
					transformation: {
						quality,
						...(width && { width }),
						...(height && { height }),
						...(bitRate && { bit_rate: bitRate })
					}
				})
			}));
		} catch (error: any) {
			throw new Error(`ไม่สามารถสร้าง adaptive streaming URLs ได้: ${error.message}`);
		}
	}

	/**
	 * อัพโหลดไฟล์พร้อม progress tracking แบบ batch
	 */
	async uploadBatchWithProgress(
		files: Array<{ file: string | Buffer | File; options?: UploadOptions; fileName?: string }>,
		globalOptions: UploadOptions = {},
		onProgress?: (progress: UploadProgress) => void
	): Promise<BatchUploadResult> {
		const results: BatchUploadResult = {
			successful: [],
			failed: [],
			total: files.length,
			successCount: 0,
			failureCount: 0
		};

		// อัพโหลดทีละไฟล์เพื่อติดตาม progress
		for (let i = 0; i < files.length; i++) {
			const { file, options = {}, fileName } = files[i];

			try {
				const fileProgress = (progress: UploadProgress) => {
					onProgress?.({
						...progress,
						fileName: fileName || `file_${i + 1}`,
						currentFile: i + 1,
						totalFiles: files.length,
						percentage: Math.round(((i / files.length) * 100) + (progress.percentage / files.length))
					});
				};

				const mergedOptions = { ...globalOptions, ...options };
				const result = await this.uploadPublic(file, mergedOptions, fileProgress);

				results.successful.push(result);
				results.successCount++;
			} catch (error: any) {
				results.failed.push({
					file: fileName || `file_${i + 1}`,
					error: error.message
				});
				results.failureCount++;
			}
		}

		// Final progress
		onProgress?.({
			loaded: 100,
			total: 100,
			percentage: 100,
			stage: 'complete',
			currentFile: files.length,
			totalFiles: files.length
		});

		return results;
	}
}

// Export singleton instance
export const cloudinaryUpload = new CloudinaryUploadUtil();

	/**
	 * วิเคราะห์รูปภาพด้วย AI (Auto-tagging, Object Detection, etc.)
	 */
	async analyzeImageWithAI(
		publicId: string,
		options: {
			autoTag?: boolean;
			objectDetection?: boolean;
			faceDetection?: boolean;
			contentModeration?: boolean;
			ocrText?: boolean;
		} = {}
	): Promise<{
		tags: string[];
		objects: Array<{ name: string; confidence: number; coordinates?: any }>;
		faces: Array<{ confidence: number; coordinates: any; emotions?: any }>;
		moderation: { safe: boolean; categories: Record<string, number> };
		text?: string;
	}> {
		try {
			const analysisResults = {
				tags: [] as string[],
				objects: [] as Array<{ name: string; confidence: number; coordinates?: any }>,
				faces: [] as Array<{ confidence: number; coordinates: any; emotions?: any }>,
				moderation: { safe: true, categories: {} as Record<string, number> },
				text: undefined as string | undefined
			};

			// Auto-tagging
			if (options.autoTag) {
				try {
					const tagResult = await cloudinary.api.resource(publicId, {
						categorization: 'google_tagging',
						auto_tagging: 0.7 // confidence threshold
					});
					
					if (tagResult.tags) {
						analysisResults.tags = tagResult.tags.map((tag: any) => tag.tag || tag);
					}
				} catch (error) {
					console.warn('Auto-tagging failed:', error);
				}
			}

			// Object Detection
			if (options.objectDetection) {
				try {
					const objectResult = await cloudinary.api.resource(publicId, {
						detection: 'coco'
					});
					
					if (objectResult.info && objectResult.info.detection && objectResult.info.detection.coco) {
						analysisResults.objects = objectResult.info.detection.coco.map((obj: any) => ({
							name: obj.label,
							confidence: obj.confidence,
							coordinates: obj.bounding_box
						}));
					}
				} catch (error) {
					console.warn('Object detection failed:', error);
				}
			}

			// Face Detection
			if (options.faceDetection) {
				try {
					const faceResult = await cloudinary.api.resource(publicId, {
						faces: true,
						detection: 'adv_face'
					});
					
					if (faceResult.faces) {
						analysisResults.faces = faceResult.faces.map((face: any) => ({
							confidence: face.confidence || 1,
							coordinates: face,
							emotions: face.attributes?.emotions
						}));
					}
				} catch (error) {
					console.warn('Face detection failed:', error);
				}
			}

			// Content Moderation
			if (options.contentModeration) {
				try {
					const moderationResult = await cloudinary.api.resource(publicId, {
						moderation: 'aws_rek'
					});
					
					if (moderationResult.moderation) {
						const moderation = moderationResult.moderation[0];
						analysisResults.moderation = {
							safe: moderation.status === 'approved',
							categories: moderation.response?.moderation_labels?.reduce((acc: any, label: any) => {
								acc[label.name] = label.confidence;
								return acc;
							}, {}) || {}
						};
					}
				} catch (error) {
					console.warn('Content moderation failed:', error);
				}
			}

			// OCR Text Recognition
			if (options.ocrText) {
				try {
					const ocrResult = await cloudinary.api.resource(publicId, {
						ocr: 'adv_ocr'
					});
					
					if (ocrResult.info && ocrResult.info.ocr && ocrResult.info.ocr.adv_ocr) {
						const textBlocks = ocrResult.info.ocr.adv_ocr.data;
						analysisResults.text = textBlocks.map((block: any) => block.textAnnotations?.[0]?.description).join(' ');
					}
				} catch (error) {
					console.warn('OCR failed:', error);
				}
			}

			return analysisResults;
		} catch (error: any) {
			throw new Error(`ไม่สามารถวิเคราะห์รูปภาพด้วย AI ได้: ${error.message}`);
		}
	}

	/**
	 * ลบพื้นหลังรูปภาพด้วย AI
	 */
	async removeBackground(
		publicId: string,
		options: {
			outputFormat?: string;
			quality?: string;
		} = {}
	): Promise<string> {
		try {
			return cloudinary.url(publicId, {
				effect: 'background_removal',
				format: options.outputFormat || 'png',
				quality: options.quality || 'auto:good'
			});
		} catch (error: any) {
			throw new Error(`ไม่สามารถลบพื้นหลังได้: ${error.message}`);
		}
	}

	/**
	 * ปรับปรุงคุณภาพรูปภาพด้วย AI
	 */
	async enhanceImage(
		publicId: string,
		options: {
			enhance?: boolean;
			upscale?: boolean;
			denoise?: boolean;
			sharpen?: boolean;
			colorize?: boolean;
		} = {}
	): Promise<string> {
		try {
			const effects = [];
			
			if (options.enhance) effects.push('enhance');
			if (options.upscale) effects.push('upscale');
			if (options.denoise) effects.push('noise_reduction');
			if (options.sharpen) effects.push('sharpen');
			if (options.colorize) effects.push('colorize');

			return cloudinary.url(publicId, {
				effect: effects.join(':'),
				quality: 'auto:best'
			});
		} catch (error: any) {
			throw new Error(`ไม่สามารถปรับปรุงรูปภาพได้: ${error.message}`);
		}
	}

	/**
	 * สร้าง Alt Text อัตโนมัติด้วย AI
	 */
	async generateAltText(
		publicId: string,
		context?: string
	): Promise<string> {
		try {
			// ใช้ Object Detection และ Auto-tagging เพื่อสร้าง Alt Text
			const analysis = await this.analyzeImageWithAI(publicId, {
				autoTag: true,
				objectDetection: true
			});

			let altText = '';

			// สร้าง Alt Text จาก objects ที่ตรวจพบ
			if (analysis.objects.length > 0) {
				const mainObjects = analysis.objects
					.filter(obj => obj.confidence > 0.7)
					.slice(0, 3)
					.map(obj => obj.name);
				
				if (mainObjects.length > 0) {
					altText = `รูปภาพที่มี ${mainObjects.join(', ')}`;
				}
			}

			// ถ้าไม่มี objects ให้ใช้ tags
			if (!altText && analysis.tags.length > 0) {
				const mainTags = analysis.tags.slice(0, 3);
				altText = `รูปภาพเกี่ยวกับ ${mainTags.join(', ')}`;
			}

			// เพิ่ม context ถ้ามี
			if (context) {
				altText = context + (altText ? ` - ${altText}` : '');
			}

			return altText || 'รูปภาพ';
		} catch (error: any) {
			console.warn('Failed to generate alt text:', error);
			return context || 'รูปภาพ';
		}
	}
}}


// Export singleton instance
export const cloudinaryUpload = new CloudinaryUploadUtil();