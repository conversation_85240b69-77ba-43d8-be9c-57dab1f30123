import Elysia from "elysia";

export class HttpError extends Error {
	public statusCode: number;
	public errorData: Record<string, unknown> | unknown[] | undefined;

	constructor(
		statusCode: number,
		message: string,
		errorData?: Record<string, unknown> | unknown[],
	) {
		super(message);
		this.statusCode = statusCode;
		this.errorData = errorData || {};
	}
}

// Helper functions for common HTTP errors
export const throwError = {
	badRequest: (message: string, data?: Record<string, unknown>) =>
		new HttpError(400, message, data),
	unauthorized: (message: string = "ไม่ได้รับอนุญาต", data?: Record<string, unknown>) =>
		new HttpError(401, message, data),
	forbidden: (message: string = "ไม่มีสิทธิ์เข้าถึง", data?: Record<string, unknown>) =>
		new HttpError(403, message, data),
	notFound: (message: string = "ไม่พบข้อมูล", data?: Record<string, unknown>) =>
		new HttpError(404, message, data),
	conflict: (message: string, data?: Record<string, unknown>) => new HttpError(409, message, data),
	unprocessable: (message: string, data?: Record<string, unknown>) =>
		new HttpError(422, message, data),
	internal: (message: string = "เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์", data?: Record<string, unknown>) =>
		new HttpError(500, message, data),
};

export const httpErrorDecorator = new Elysia({
	name: "elysia-http-error-decorator",
}).decorate("HttpError", HttpError);

interface HttpErrorConstructor {
	customFormatter?: (error: HttpError) => Record<string, unknown> | string;
	returnStringOnly?: boolean;
}

export const httpError = (
	params: HttpErrorConstructor = {
		customFormatter: undefined,
		returnStringOnly: false,
	},
) =>
	new Elysia({ name: "elysia-http-error" })
		.error({
			ELYSIA_HTTP_ERROR: HttpError,
		})
		.onError({ as: "global" }, ({ code, error, set }) => {
			if (code === "ELYSIA_HTTP_ERROR") {
				set.status = error.statusCode;
				if (params.customFormatter) {
					return params.customFormatter(error);
				}
				if (params.returnStringOnly) {
					return error.message;
				}
				return {
					success: false,
					error: true,
					code: error.statusCode,
					message: error.message,
					data: error.errorData,
				};
			}
		});
