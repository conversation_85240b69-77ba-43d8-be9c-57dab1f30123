interface EmailOptions {
	to: string;
	subject: string;
	html: string;
	from?: string;
}

export async function sendEmail(options: EmailOptions): Promise<void> {
	const { to, subject, html, from = process.env.EMAIL_FROM || "<EMAIL>" } = options;

	// ใช้ Nodemailer หรือ service อื่นๆ ตามที่ต้องการ
	// ตัวอย่างนี้จะใช้ fetch เรียก email service API

	try {
		// ถ้าใช้ SendGrid
		if (process.env.SENDGRID_API_KEY) {
			const response = await fetch("https://api.sendgrid.com/v3/mail/send", {
				method: "POST",
				headers: {
					Authorization: `Bearer ${process.env.SENDGRID_API_KEY}`,
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					personalizations: [
						{
							to: [{ email: to }],
							subject: subject,
						},
					],
					from: { email: from },
					content: [
						{
							type: "text/html",
							value: html,
						},
					],
				}),
			});

			if (!response.ok) {
				throw new Error(`SendGrid API error: ${response.status}`);
			}
		}
		// ถ้าใช้ Resend
		else if (process.env.RESEND_API_KEY) {
			const response = await fetch("https://api.resend.com/emails", {
				method: "POST",
				headers: {
					Authorization: `Bearer ${process.env.RESEND_API_KEY}`,
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					from: from,
					to: [to],
					subject: subject,
					html: html,
				}),
			});

			if (!response.ok) {
				throw new Error(`Resend API error: ${response.status}`);
			}
		}
		// ถ้าใช้ Mailgun
		else if (process.env.MAILGUN_API_KEY && process.env.MAILGUN_DOMAIN) {
			const formData = new FormData();
			formData.append("from", from);
			formData.append("to", to);
			formData.append("subject", subject);
			formData.append("html", html);

			const response = await fetch(
				`https://api.mailgun.net/v3/${process.env.MAILGUN_DOMAIN}/messages`,
				{
					method: "POST",
					headers: {
						Authorization: `Basic ${Buffer.from(`api:${process.env.MAILGUN_API_KEY}`).toString("base64")}`,
					},
					body: formData,
				},
			);

			if (!response.ok) {
				throw new Error(`Mailgun API error: ${response.status}`);
			}
		}
		// Development mode - log email instead of sending
		else {
			console.log("📧 Email would be sent:");
			console.log(`From: ${from}`);
			console.log(`To: ${to}`);
			console.log(`Subject: ${subject}`);
			console.log(`HTML: ${html}`);
			console.log("✅ Email sent successfully (development mode)");
			console.log("---");
		}
	} catch (error) {
		console.error("Failed to send email:", error);
		// ไม่ throw error เพื่อไม่ให้ระบบหยุดทำงาน
		console.log("📧 Email sending failed, but continuing...");
	}
}
