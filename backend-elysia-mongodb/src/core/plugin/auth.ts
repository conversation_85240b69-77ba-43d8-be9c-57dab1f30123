import { jwt } from "@elysiajs/jwt";
import { Elysia, t } from "elysia";
import { throwError } from "@/core/utils/error";
import { config } from "@/core/config/environment";
import { User, type IUser } from "@/modules/user/user.model";
import { Customer, type ICustomer } from "@/modules/customer/customer.model";

// JWT configuration factory
const createJwtConfig = (name: string, secret: string, exp: string) => ({
	name,
	secret,
	exp,
	alg: "HS256" as const,
});

// User JWT Plugin
export const userJwtPlugin = new Elysia()
	.use(jwt(createJwtConfig("jwt", config.jwtSecret, config.jwtExpiresIn)))
	.use(jwt(createJwtConfig("refreshJwt", config.jwtRefreshSecret, config.refreshTokenExpiresIn)));

// Customer JWT Plugin  
export const customerJwtPlugin = new Elysia()
	.use(jwt(createJwtConfig("customerJwt", config.customerJwtSecret, config.jwtExpiresIn)))
	.use(jwt(createJwtConfig("customerRefreshJwt", config.customerJwtRefreshSecret, config.refreshTokenExpiresIn)));

// Generic auth verification function
async function verifyAuth<T>(
	jwtInstance: any,
	headers: Record<string, string | undefined>,
	model: any,
	userIdField: string,
	notFoundMessage: string
): Promise<T> {
	const authHeader = headers.authorization;

	if (!authHeader || !authHeader.startsWith("Bearer ")) {
		throw throwError.unauthorized("Token ไม่ถูกต้องหรือหมดอายุ");
	}

	const token = authHeader.substring(7);
	const payload = await jwtInstance.verify(token);

	if (!payload || !payload[userIdField]) {
		throw throwError.unauthorized("Token ไม่ถูกต้องหรือหมดอายุ");
	}

	const entity = await model.findById(payload[userIdField]);

	if (!entity || !entity.isActive) {
		throw throwError.unauthorized(notFoundMessage);
	}

	return entity;
}

// User Auth Guard
export const userAuthGuard = new Elysia()
	.use(userJwtPlugin)
	.derive(async ({ jwt, headers }: any): Promise<{ user: IUser }> => {
		const user = await verifyAuth<IUser>(
			jwt,
			headers,
			User,
			"userId",
			"ไม่พบผู้ใช้งาน"
		);
		return { user };
	})
	.guard({
		headers: t.Object({
			authorization: t.String(),
		}),
	})
	.as('scoped');

// Customer Auth Guard
export const customerAuthGuard = new Elysia()
	.use(customerJwtPlugin)
	.derive(async ({ customerJwt, headers }: any): Promise<{ customer: ICustomer }> => {
		const customer = await verifyAuth<ICustomer>(
			customerJwt,
			headers,
			Customer,
			"customerId",
			"ไม่พบลูกค้า"
		);
		return { customer };
	})
	.guard({
		headers: t.Object({
			authorization: t.String(),
		}),
	})
	.as('scoped');

