# Authentication Plugin Refactoring

## การปรับปรุงที่ทำไป

### ก่อนการปรับปรุง
มีไฟล์ plugin ที่ซ้ำซ้อน 5 ไฟล์:
- `authCustomerPlugin.ts` - Auth plugin สำหรับ customer
- `authUserPlugin.ts` - Auth plugin สำหรับ user  
- `customerJwtPlugin.ts` - JWT plugin สำหรับ customer
- `jwtPlugin.ts` - JWT plugin สำหรับ user
- `index.ts` - Export ทั้งหมด

### หลังการปรับปรุง
รวมเป็นไฟล์เดียว:
- `auth.ts` - รวม authentication logic ทั้งหมด
- `index.ts` - Export ทั้งหมด

## ประโยชน์ที่ได้รับ

### 1. ลดความซ้ำซ้อน (DRY Principle)
- ใช้ `createJwtConfig` factory function สำหรับสร้าง JWT config
- ใช้ `verifyAuth` generic function สำหรับตรวจสอบ authentication
- ลดโค้ดจาก ~200 บรรทัด เหลือ ~85 บรรทัด

### 2. การจัดการ Configuration ที่ดีขึ้น
- ใช้ `config` จาก `environment.ts` ทุกที่
- เพิ่ม JWT secrets สำหรับ customer และ refresh tokens
- ไม่มีการ hardcode secrets

### 3. Type Safety ที่ดีขึ้น
- ใช้ TypeScript generics สำหรับ type safety
- ชัดเจนในการ return types
- ลด any types ให้น้อยที่สุด

### 4. Clean Naming Convention
- ใช้ชื่อที่สื่อความหมายชัดเจน
- ไม่มี legacy aliases ที่สับสน
- Consistent naming pattern

## การใช้งาน

### User Authentication
```typescript
import { userJwtPlugin, userAuthGuard } from "@/core/plugin";
```

### Customer Authentication  
```typescript
import { customerJwtPlugin, customerAuthGuard } from "@/core/plugin";
```

## Configuration ใหม่ใน environment.ts

เพิ่ม JWT secrets สำหรับ customer และ refresh tokens:
```typescript
// JWT Configuration
jwtSecret: Bun.env.JWT_SECRET || "your-secret-key",
jwtRefreshSecret: Bun.env.JWT_REFRESH_SECRET || "your-refresh-secret-key",
jwtExpiresIn: Bun.env.JWT_EXPIRES_IN || "7d",
refreshTokenExpiresIn: Bun.env.REFRESH_TOKEN_EXPIRES_IN || "30d",

// Customer JWT Configuration
customerJwtSecret: Bun.env.CUSTOMER_JWT_SECRET || "customer-secret-key",
customerJwtRefreshSecret: Bun.env.CUSTOMER_JWT_REFRESH_SECRET || "customer-refresh-secret-key",
```

## Migration Guide

ชื่อใหม่ที่ใช้แทนชื่อเก่า:

### เก่า → ใหม่
- `authUserPlugin` → `userAuthGuard`
- `authCustomerPlugin` → `customerAuthGuard`
- `jwtPlugin` → `userJwtPlugin`
- `authGuardPlugin` → `userAuthGuard`
- `customerAuthGuardPlugin` → `customerAuthGuard`

## ไฟล์ที่อัปเดตแล้ว
- ✅ `src/modules/user/user.route.ts`
- ✅ `src/modules/customer/customer.route.ts`
- ✅ `src/modules/auth/security.route.ts`
- ✅ `src/modules/site/site.route.ts`

## API ที่ Export

```typescript
export {
  // User authentication
  userJwtPlugin,
  userAuthGuard,
  
  // Customer authentication  
  customerJwtPlugin,
  customerAuthGuard,
} from "@/core/plugin";
```