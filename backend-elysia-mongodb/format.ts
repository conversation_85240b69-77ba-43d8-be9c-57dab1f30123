// ฟังก์ชันสำหรับจัดรูปแบบโค้ด
async function formatCode() {
  console.log('💅 เริ่มต้นการจัดรูปแบบโค้ด...');

  try {
    // รัน Biome formatter
    console.log('\n✨ กำลังจัดรูปแบบโค้ดด้วย Biome...');
    const biomeProc = Bun.spawn(['bunx', '@biomejs/biome', 'format', '--write', './src'], {
      stdout: 'inherit',
      stderr: 'inherit',
      stdin: 'inherit',
    });

    const biomeExitCode = await biomeProc.exited;
    if (biomeExitCode !== 0) {
      console.error('❌ Biome ทำงานไม่สำเร็จ');
      process.exit(1);
    }

    console.log('\n✅ การจัดรูปแบบโค้ดเสร็จสิ้น!');
  } catch (error) {
    console.error('❌ เกิดข้อผิดพลาดในการจัดรูปแบบโค้ด:', error);
    process.exit(1);
  }
}

// เริ่มต้นการจัดรูปแบบโค้ด
formatCode();
