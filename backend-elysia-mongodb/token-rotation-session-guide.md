# 🔄 Token Rotation และ 👥 Session Management Guide

## 🔄 Token Rotation (การหมุนเวียน Token)

### คืออะไร?
Token Rotation คือการสร้าง tokens ใหม่ทุกครั้งที่มีการใช้ refresh token และเพิ่ม tokens เก่าเข้า blacklist

### ทำไมต้องใช้?
1. **ลดความเสี่ยงจาก Token Hijacking**: ถ้า token ถูกขโมย จะใช้ได้เพียงครั้งเดียว
2. **จำกัดอายุการใช้งาน**: Tokens เก่าจะไม่สามารถใช้งานได้อีก
3. **ตรวจจับการโจรกรรม**: ถ้ามีการใช้ token ที่ถูก revoke แล้ว จะรู้ว่ามีปัญหา

### วิธีการทำงาน

#### 1. การ Refresh Token ปกติ (ไม่มี Rotation)
```
User ส่ง refresh_token_A
↓
Server ตรวจสอบ refresh_token_A
↓
Server สร้าง access_token ใหม่
↓
refresh_token_A ยังใช้ได้ต่อไป ⚠️ (เสี่ยง)
```

#### 2. การ Refresh Token แบบ Rotation (ปลอดภัย)
```
User ส่ง refresh_token_A
↓
Server ตรวจสอบ refresh_token_A
↓
Server สร้าง access_token ใหม่ + refresh_token_B
↓
Server เพิ่ม refresh_token_A เข้า blacklist
↓
refresh_token_A ใช้ไม่ได้แล้ว ✅ (ปลอดภัย)
```

### การใช้งานใน Code

```typescript
// ใน refresh token endpoint
const rotationResult = await TokenRotationService.rotateTokens(
  userId,
  oldAccessToken,
  oldRefreshToken,
  jwt.sign,
  refreshJwt.sign
);

return {
  accessToken: rotationResult.accessToken,
  refreshToken: rotationResult.refreshToken
};
```

### Refresh Token Reuse Detection

```typescript
// ตรวจจับการใช้ refresh token ที่ถูก revoke
const reuseCheck = await TokenRotationService.detectRefreshTokenReuse(
  refreshToken,
  userId
);

if (reuseCheck.isReuse) {
  // มีการโจรกรรม token! ยกเลิก tokens ทั้งหมด
  await TokenBlacklistService.blacklistAllUserTokens(userId, 'revoked');
  throw new Error('Security breach detected');
}
```

---

## 👥 Session Management (การจัดการ Session)

### คืออะไร?
Session Management คือการติดตามและจัดการ sessions ของ users ที่ login เข้าระบบจาก devices ต่างๆ

### ประโยชน์
1. **ติดตาม Devices**: รู้ว่า user login จาก device ไหนบ้าง
2. **จำกัด Concurrent Sessions**: ป้องกันการใช้งานพร้อมกันเกินขีดจำกัด
3. **ตรวจจับการ Login ผิดปกติ**: เช่น login จาก IP หรือ device ใหม่
4. **Remote Logout**: ให้ user logout จาก devices อื่นได้

### ข้อมูลที่เก็บใน Session

```typescript
interface IUserSession {
  userId: string;
  sessionId: string;
  deviceInfo: {
    userAgent: string;
    browser: string;    // Chrome, Firefox, Safari
    os: string;         // Windows, macOS, Linux
    device: string;     // Desktop, Mobile, Tablet
  };
  location: {
    ipAddress: string;
    country?: string;
    city?: string;
  };
  isActive: boolean;
  lastActivity: Date;
  loginTime: Date;
  expiresAt: Date;
  refreshToken: string;
}
```

### การใช้งานใน Code

#### 1. สร้าง Session เมื่อ Login
```typescript
const sessionId = await SessionService.createSession({
  userId: user._id,
  userAgent: headers['user-agent'],
  ipAddress: getClientIP(headers),
  refreshToken: newRefreshToken,
  expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 วัน
});
```

#### 2. อัปเดต Activity
```typescript
// ทุกครั้งที่ user ใช้งาน
await SessionService.updateSessionActivity(sessionId);
```

#### 3. ดู Sessions ทั้งหมด
```typescript
const sessions = await SessionService.getUserSessions(userId, currentSessionId);
// ผลลัพธ์:
[
  {
    sessionId: "sess_123",
    deviceInfo: { browser: "Chrome", os: "Windows", device: "Desktop" },
    location: { ipAddress: "***********" },
    loginTime: "2024-01-01T10:00:00Z",
    lastActivity: "2024-01-01T15:30:00Z",
    isActive: true,
    isCurrent: true
  },
  {
    sessionId: "sess_456",
    deviceInfo: { browser: "Safari", os: "iOS", device: "Mobile" },
    location: { ipAddress: "***********" },
    loginTime: "2024-01-01T08:00:00Z",
    lastActivity: "2024-01-01T12:00:00Z",
    isActive: true,
    isCurrent: false
  }
]
```

#### 4. Logout จาก Devices อื่น
```typescript
// Logout จาก devices อื่นทั้งหมด
const revokedCount = await SessionService.revokeAllOtherSessions(
  userId, 
  currentSessionId
);

// Logout จาก device เฉพาะ
await SessionService.revokeSession(sessionId, userId);
```

### การตรวจจับ Suspicious Login

```typescript
const suspiciousCheck = await SessionService.detectSuspiciousLogin(
  userId,
  ipAddress,
  userAgent
);

if (suspiciousCheck.isSuspicious) {
  console.log('Suspicious login detected:', suspiciousCheck.reasons);
  // เช่น: ['IP address ใหม่', 'Device ใหม่']
  
  if (suspiciousCheck.shouldRequire2FA) {
    // ต้องการ 2FA
    return { requireTwoFactor: true };
  }
}
```

### Sliding Session (ขยายอายุอัตโนมัติ)

```typescript
const slidingResult = await TokenRotationService.slidingSession(
  userId,
  lastActivity,
  30 // 30 นาที
);

if (slidingResult.shouldExtend) {
  // ขยายอายุ session
  await SessionService.updateSessionActivity(sessionId);
}
```

---

## 🛡️ การรวมทั้งหมดเข้าด้วยกัน

### Flow การ Login ที่สมบูรณ์

```typescript
// 1. ตรวจสอบ Rate Limit
const rateLimitResult = await RateLimitService.checkRateLimit(
  ipAddress, 
  '/users/signin'
);

if (!rateLimitResult.allowed) {
  throw new Error('Too many requests');
}

// 2. ตรวจสอบ credentials
const user = await UserService.signin({ email, password });

// 3. ตรวจจับ Suspicious Login
const suspiciousCheck = await SessionService.detectSuspiciousLogin(
  user._id,
  ipAddress,
  userAgent
);

// 4. สร้าง tokens
const accessToken = await jwt.sign({ userId: user._id });
const refreshToken = await refreshJwt.sign({ userId: user._id });

// 5. สร้าง session
const sessionId = await SessionService.createSession({
  userId: user._id,
  userAgent,
  ipAddress,
  refreshToken,
  expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
});

// 6. อัปเดต refresh token ใน database
await UserService.updateRefreshToken(user._id, refreshToken);
```

### Flow การ Refresh Token ที่สมบูรณ์

```typescript
// 1. ตรวจสอบ Rate Limit
const rateLimitResult = await RateLimitService.checkRateLimit(
  ipAddress, 
  '/users/refresh-token'
);

// 2. ตรวจสอบ Token Blacklist
const isBlacklisted = await TokenBlacklistService.isTokenBlacklisted(refreshToken);
if (isBlacklisted) {
  throw new Error('Token revoked');
}

// 3. ตรวจจับ Token Reuse
const reuseCheck = await TokenRotationService.detectRefreshTokenReuse(
  refreshToken,
  userId
);

if (reuseCheck.isReuse) {
  // Security breach!
  await TokenBlacklistService.blacklistAllUserTokens(userId, 'revoked');
  throw new Error('Security breach detected');
}

// 4. Token Rotation
const rotationResult = await TokenRotationService.rotateTokens(
  userId,
  oldAccessToken,
  oldRefreshToken,
  jwt.sign,
  refreshJwt.sign
);

// 5. อัปเดต Session Activity
await SessionService.updateSessionActivity(sessionId);
```

### Flow การ Logout ที่สมบูรณ์

```typescript
// 1. เพิ่ม tokens เข้า blacklist
await TokenBlacklistService.addToBlacklist(
  accessToken,
  'access',
  userId,
  new Date(Date.now() + 60 * 60 * 1000), // 1 ชั่วโมง
  'logout'
);

// 2. ยกเลิก session
await SessionService.revokeSession(sessionId, userId);

// 3. ลบ refresh token จาก database
await UserService.updateRefreshToken(userId, null);
```

---

## 📊 การ Monitoring และ Analytics

### Security Dashboard
```typescript
// ดูภาพรวมความปลอดภัย
const overview = await fetch('/api/security/overview');
// ผลลัพธ์:
{
  blacklist: {
    totalBlacklisted: 1250,
    accessTokens: 800,
    refreshTokens: 450,
    expiredTokens: 200
  },
  rateLimit: {
    totalRecords: 500,
    blockedRecords: 25,
    topEndpoints: [
      { endpoint: '/users/signin', count: 200 },
      { endpoint: '/users/signup', count: 150 }
    ]
  },
  sessions: {
    totalActiveSessions: 1500,
    totalUsers: 800,
    averageSessionsPerUser: 1.875,
    topDevices: [
      { device: 'Desktop', count: 900 },
      { device: 'Mobile', count: 600 }
    ]
  }
}
```

### Cleanup Jobs
```typescript
// เริ่มต้น cleanup jobs อัตโนมัติ
CleanupService.startCleanupJobs();

// รัน cleanup ด้วยตนเอง
const result = await CleanupService.runCleanup();
// ผลลัพธ์:
{
  blacklistCleaned: 50,
  rateLimitCleaned: 25,
  sessionsCleaned: 30,
  timestamp: "2024-01-01T12:00:00Z"
}
```

---

## 🎯 สรุป

### ระดับความปลอดภัยหลังจากเพิ่ม Features เหล่านี้: ⭐⭐⭐⭐⭐ (5/5)

**Features ที่เพิ่มแล้ว:**
- ✅ Token Blacklist System
- ✅ Rate Limiting
- ✅ Token Rotation
- ✅ Session Management
- ✅ Suspicious Login Detection
- ✅ Refresh Token Reuse Detection
- ✅ Sliding Sessions
- ✅ Security Monitoring Dashboard
- ✅ Automatic Cleanup Jobs

**ประโยชน์ที่ได้:**
1. **ป้องกัน Token Hijacking**: ด้วย Token Rotation และ Blacklist
2. **ป้องกัน Brute Force**: ด้วย Rate Limiting
3. **ตรวจจับการโจรกรรม**: ด้วย Reuse Detection และ Suspicious Login
4. **จัดการ Multiple Devices**: ด้วย Session Management
5. **Monitoring และ Analytics**: ด้วย Security Dashboard

ระบบ Authentication ตอนนี้มีความปลอดภัยระดับ Enterprise และพร้อมใช้งานในการผลิตแล้วครับ! 🚀