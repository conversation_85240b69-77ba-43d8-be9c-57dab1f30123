{"name": "backend-elysia", "version": "1.0.50", "scripts": {"dev": "bun run --watch src/index.ts", "start": "bun run src/index.ts", "build": "bun build.ts", "typecheck": "tsc --noEmit", "lint": "bunx @biomejs/biome check ./src", "lint:ts": "bun lint.ts", "format": "bunx @biomejs/biome format --write ./src", "format:ts": "bun format.ts", "check": "bunx @biomejs/biome check --fix ./src --verbose", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@elysiajs/cors": "^1.3.3", "@elysiajs/jwt": "^1.3.2", "@elysiajs/server-timing": "^1.3.0", "argon2": "^0.43.1", "cloudinary": "^2.7.0", "elysia": "^1.3.8", "jose": "^6.0.12", "logestic": "^1.2.4", "mongoose": "^8.17.0", "nanoid": "^5.1.5", "nodemailer": "^7.0.5", "zod": "^4.0.15"}, "devDependencies": {"@biomejs/biome": "2.1.3", "@types/nodemailer": "^6.4.17", "bun-types": "^1.2.19"}, "module": "src/index.js"}