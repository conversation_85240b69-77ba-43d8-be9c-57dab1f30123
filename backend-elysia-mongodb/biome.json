{"$schema": "https://biomejs.dev/schemas/2.1.3/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false}, "formatter": {"enabled": true, "indentStyle": "tab", "indentWidth": 2, "lineWidth": 100}, "linter": {"enabled": true, "rules": {"recommended": true, "correctness": {"noUnusedVariables": "error"}, "suspicious": {"noConsole": "off", "noExplicitAny": "off", "noMisleadingCharacterClass": "off", "noArrayIndexKey": "off"}, "complexity": {"noForEach": "off", "noBannedTypes": "off"}, "style": {"useConst": "error", "useTemplate": "error", "useNodejsImportProtocol": "off", "noNonNullAssertion": "off"}}}, "javascript": {"formatter": {"quoteStyle": "double"}}, "assist": {"enabled": true, "actions": {"source": {"organizeImports": "on"}}}}